#!/usr/bin/env python3
"""
完全按照原始方法的流程进行性能测试
包括预处理和独立脚本运行
"""

import os
import shutil
import tempfile
import subprocess
import ast
import cProfile
import pstats
import psutil
from memory_profiler import memory_usage
import json
import argparse
from evalperf_config import EvalPerfConfig

class OriginalMethodTester:
    """完全按照原始方法的性能测试器"""

    def __init__(self, evalperf_dataset_path, original_humaneval_path, original_mbpp_path):
        self.config = EvalPerfConfig()
        self.evalperf_dataset_path = evalperf_dataset_path
        self.original_humaneval_path = original_humaneval_path
        self.original_mbpp_path = original_mbpp_path

        # 加载数据集
        self.evalperf_tasks = self._load_evalperf_dataset()
        self.original_humaneval_tasks = self._load_original_dataset(original_humaneval_path)
        self.original_mbpp_tasks = self._load_original_dataset(original_mbpp_path)

    def _load_evalperf_dataset(self):
        """加载EvalPerf数据集"""
        tasks = {}
        with open(self.evalperf_dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    def _load_original_dataset(self, dataset_path):
        """加载原始数据集"""
        tasks = {}
        with open(dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    def preprocess_add_for_function(self, code_content):
        """预处理：添加for循环（模拟add_for_function.py）"""
        try:
            parsed_code = ast.parse(code_content)

            for node in parsed_code.body:
                if isinstance(node, ast.FunctionDef) and node.name == 'check':
                    # 创建for循环：for item in range(30):
                    loop = ast.For(
                        target=ast.Name(id='item', ctx=ast.Store()),
                        iter=ast.Call(
                            func=ast.Name(id='range', ctx=ast.Load()),
                            args=[ast.Constant(n=30)],
                            keywords=[]
                        ),
                        body=node.body,
                        orelse=[]
                    )
                    node.body = [loop]

            modified_code = ast.fix_missing_locations(parsed_code)
            return ast.unparse(modified_code)
        except:
            # 如果AST处理失败，返回原代码
            return code_content

    def preprocess_add_profile(self, code_content):
        """预处理：添加@profile装饰器（模拟***************）"""
        return "from memory_profiler import profile\n@profile\n" + code_content

    def create_temp_script(self, code_content, add_loop=False, add_profile=False):
        """创建临时脚本文件"""
        # 预处理
        if add_loop:
            code_content = self.preprocess_add_for_function(code_content)
        if add_profile:
            code_content = self.preprocess_add_profile(code_content)

        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code_content)
            return f.name

    def run_cprofile_method(self, script_path):
        """运行cprofile方法（完全按照原始cprofile.py）"""
        try:
            # 编译代码
            with open(script_path, "rb") as file:
                code = compile(file.read(), script_path, 'exec')

            # 运行cProfile
            profile_file = tempfile.mktemp()
            cProfile.run(code, profile_file)

            # 提取cumtime（按照原始方法）
            p = pstats.Stats(profile_file)
            cumtime = p.stats[list(p.stats.keys())[0]][3]

            # 清理
            os.unlink(profile_file)
            return cumtime
        except Exception as e:
            print(f"cProfile测量失败: {e}")
            return 0.0

    def run_memory_method(self, script_path):
        """运行memory方法（完全按照原始memory.py）"""
        try:
            # 启动进程
            command = ["python3", script_path]
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # 监控内存
            mem_usage = memory_usage(proc=process.pid, interval=0.1, timeout=None)
            max_mem = max(mem_usage)

            return max_mem
        except Exception as e:
            print(f"内存测量失败: {e}")
            return 0.0

    def run_cpu_method(self, script_path):
        """运行CPU方法（完全按照原始cpu.py）"""
        try:
            # 启动进程
            command = ["python3", script_path]
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # 先监控内存（按照原始顺序）
            mem_usage = memory_usage(proc=process.pid, interval=0.01, timeout=None)
            max_mem = max(mem_usage)

            # 再监控CPU（这里会失败，因为进程已结束）
            try:
                ps_process = psutil.Process(process.pid)
                cpu_usages = []

                while True:
                    cpu_usage = ps_process.cpu_percent(interval=0.1)
                    if cpu_usage == 0:  # 原始逻辑
                        break
                    cpu_usages.append(cpu_usage)
            except psutil.NoSuchProcess:
                cpu_usages = []

            return max_mem, cpu_usages
        except Exception as e:
            print(f"CPU测量失败: {e}")
            return 0.0, []

    def test_single_task_original_method(self, task_id, model, num_runs=5):
        """使用原始方法测试单个任务"""
        print(f"Testing {task_id} with original method...")

        results = {
            'task_id': task_id,
            'generated_code_results': None,
            'evalperf_canonical_results': None,
            'original_canonical_results': None
        }

        # 1. 测试Generated Code
        generated_path = self.config.get_generated_code_path(model,
                                                           'humaneval' if 'HumanEval' in task_id else 'mbpp',
                                                           task_id)
        if generated_path and os.path.exists(generated_path):
            with open(generated_path, 'r') as f:
                generated_code = f.read()

            results['generated_code_results'] = self._run_original_method_multiple_times(
                generated_code, num_runs, add_loop=True, test_type="Generated Code"
            )

        # 2. 测试EvalPerf Canonical (使用第一个reference解决方案)
        if task_id in self.evalperf_tasks:
            evalperf_code = self.evalperf_tasks[task_id]['reference'][0]  # 使用第一个reference
            results['evalperf_canonical_results'] = self._run_original_method_multiple_times(
                evalperf_code, num_runs, add_loop=True, test_type="EvalPerf Canonical"
            )

        # 3. 测试Original Canonical
        if task_id in self.original_humaneval_tasks:
            original_code = self.original_humaneval_tasks[task_id]['canonical_solution']
        elif task_id in self.original_mbpp_tasks:
            original_code = self.original_mbpp_tasks[task_id]['code']
        else:
            original_code = None

        if original_code:
            results['original_canonical_results'] = self._run_original_method_multiple_times(
                original_code, num_runs, add_loop=True, test_type="Original Canonical"
            )

        return results

    def _run_original_method_multiple_times(self, code_content, num_runs, add_loop=False, test_type=""):
        """多次运行原始方法"""
        execution_times = []
        memory_usages = []
        cpu_usages_list = []

        for run in range(num_runs):
            print(f"  {test_type} - Run {run + 1}/{num_runs}")

            # 创建临时脚本
            script_path = self.create_temp_script(code_content, add_loop=add_loop)

            try:
                # 1. 运行cprofile方法
                exec_time = self.run_cprofile_method(script_path)
                execution_times.append(exec_time)

                # 2. 运行memory方法
                memory_usage = self.run_memory_method(script_path)
                memory_usages.append(memory_usage)

                # 3. 运行CPU方法
                mem_usage_cpu, cpu_usages = self.run_cpu_method(script_path)
                cpu_usages_list.append(cpu_usages)

            except Exception as e:
                print(f"    Run {run + 1} failed: {e}")
                execution_times.append(0.0)
                memory_usages.append(0.0)
                cpu_usages_list.append([])

            finally:
                # 清理临时文件
                if os.path.exists(script_path):
                    os.unlink(script_path)

        # 计算统计数据
        valid_times = [t for t in execution_times if t > 0]
        valid_memories = [m for m in memory_usages if m > 0]

        return {
            'execution_times': execution_times,
            'memory_usages': memory_usages,
            'cpu_usages_list': cpu_usages_list,
            'avg_execution_time': sum(valid_times) / len(valid_times) if valid_times else 0.0,
            'avg_memory_usage': sum(valid_memories) / len(valid_memories) if valid_memories else 0.0,
            'avg_cpu_usage': 0.0,  # 原始方法通常无CPU数据
            'success_count': len(valid_times)
        }

def main():
    parser = argparse.ArgumentParser(description='Original Method Performance Tester')
    parser.add_argument('--model', required=True, help='Model name (e.g., copilot)')
    parser.add_argument('--tasks', nargs='+', help='Specific tasks to test (e.g., HumanEval/0 Mbpp/3)')
    parser.add_argument('--num-runs', type=int, default=5, help='Number of runs per task')

    args = parser.parse_args()

    # 初始化测试器
    tester = OriginalMethodTester(
        evalperf_dataset_path='/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl',
        original_humaneval_path='./json-prompt/human-eval-v2-20210705.jsonl',
        original_mbpp_path='./json-prompt/mbpp.jsonl'
    )

    print(f"=== 原始方法性能测试 ===")
    print(f"模型: {args.model}")
    print(f"任务: {args.tasks}")
    print(f"运行次数: {args.num_runs}")
    print()

    all_results = {}

    for task_id in args.tasks:
        result = tester.test_single_task_original_method(task_id, args.model, args.num_runs)
        all_results[task_id] = result

        # 显示结果
        print(f"\n{task_id} 结果:")
        for test_type in ['generated_code_results', 'evalperf_canonical_results', 'original_canonical_results']:
            if result[test_type]:
                data = result[test_type]
                print(f"  {test_type}:")
                print(f"    执行时间: {data['avg_execution_time']:.6f}s")
                print(f"    内存使用: {data['avg_memory_usage']:.3f}MB")
                print(f"    CPU数据: {len([cpu for cpu in data['cpu_usages_list'] if cpu])}个有效运行")

    # 保存结果
    output_file = f'./original_method_results/{args.model}_original_results.json'
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    print(f"\n结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
