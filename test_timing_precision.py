#!/usr/bin/env python3
"""
测试时间测量精度
"""

import time
import cProfile
import pstats
import tempfile
import subprocess
import sys


def test_timing_precision():
    """测试不同时间测量方法的精度"""

    # 创建一个简单的测试函数
    test_code = """
def largest_divisor(n: int) -> int:
    for i in reversed(range(n)):
        if n % i == 0:
            return i

def main():
    # 测试小数据
    largest_divisor(15)

    # 测试中等数据
    largest_divisor(1000)

    # 测试大数据（如果不超时）
    # largest_divisor(100000)

if __name__ == "__main__":
    main()
"""

    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        temp_file = f.name

    print("=== 时间测量精度测试 ===")

    # 方法1: time.time()
    start = time.time()
    result = subprocess.run([sys.executable, temp_file], capture_output=True, text=True)
    end = time.time()
    print(f"time.time(): {end - start:.8f}s")

    # 方法2: time.perf_counter()
    start = time.perf_counter()
    result = subprocess.run([sys.executable, temp_file], capture_output=True, text=True)
    end = time.perf_counter()
    print(f"time.perf_counter(): {end - start:.8f}s")

    # 方法3: cProfile (我们当前使用的方法)
    try:
        profile_file = tempfile.mktemp(suffix='.prof')
        result = subprocess.run([
            sys.executable, '-m', 'cProfile', '-o', profile_file, temp_file
        ], capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            p = pstats.Stats(profile_file)
            if p.stats:
                cumtime = p.stats[list(p.stats.keys())[0]][3]
                print(f"cProfile cumtime: {cumtime:.8f}s")
        else:
            print(f"cProfile failed: {result.stderr}")

    except Exception as e:
        print(f"cProfile error: {e}")

    # 方法4: 多次运行取平均
    print("\n=== 多次运行测试 ===")
    times = []
    for i in range(10):
        start = time.perf_counter()
        result = subprocess.run([sys.executable, temp_file], capture_output=True, text=True)
        end = time.perf_counter()
        times.append(end - start)

    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)

    print(f"10次运行平均: {avg_time:.8f}s")
    print(f"最小时间: {min_time:.8f}s")
    print(f"最大时间: {max_time:.8f}s")
    print(f"时间变化范围: {(max_time - min_time) / avg_time * 100:.1f}%")


def test_different_complexities():
    """测试不同复杂度的函数"""
    print("\n=== 不同复杂度测试 ===")

    # 简单函数
    def simple_func():
        return 1 + 1

    # 中等复杂度
    def medium_func():
        total = 0
        for i in range(1000):
            total += i
        return total

    # 复杂函数
    def complex_func():
        total = 0
        for i in range(10000):
            for j in range(10):
                total += i * j
        return total

    functions = [
        ("简单函数", simple_func),
        ("中等复杂度", medium_func),
        ("复杂函数", complex_func)
    ]

    for name, func in functions:
        times = []
        for _ in range(10):
            start = time.perf_counter()
            func()
            end = time.perf_counter()
            times.append(end - start)

        avg_time = sum(times) / len(times)
        print(f"{name}: {avg_time:.8f}s")


if __name__ == "__main__":
    test_timing_precision()
    test_different_complexities()
