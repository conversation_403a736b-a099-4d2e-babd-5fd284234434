#!/usr/bin/env python3
"""
EvalPerf集成性能测试框架
基于现有的模型生成结果，在EvalPerf数据集上进行性能测试
"""

import json
import os
import time
import psutil
import cProfile
import pstats
import statistics
import subprocess
import sys
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import tempfile
import ast
from memory_profiler import memory_usage
import numpy as np
from scipy import stats
import math


class EvalPerfIntegratedTester:
    def __init__(self,
                 evalperf_dataset_path: str,
                 original_humaneval_path: str,
                 original_mbpp_path: str):
        """
        初始化EvalPerf集成测试器

        Args:
            evalperf_dataset_path: EvalPerf数据集路径
            original_humaneval_path: 原始HumanEval数据集路径
            original_mbpp_path: 原始MBPP数据集路径
        """
        self.evalperf_dataset_path = evalperf_dataset_path
        self.original_humaneval_path = original_humaneval_path
        self.original_mbpp_path = original_mbpp_path

        # 加载数据集
        self.evalperf_data = self._load_evalperf_data()
        self.original_humaneval_data = self._load_original_data(original_humaneval_path)
        self.original_mbpp_data = self._load_original_data(original_mbpp_path)

    def _load_evalperf_data(self) -> Dict[str, Any]:
        """加载EvalPerf数据集"""
        evalperf_data = {}
        print(f"Loading EvalPerf dataset from {self.evalperf_dataset_path}")

        with open(self.evalperf_dataset_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    item = json.loads(line.strip())
                    evalperf_data[item['task_id']] = item
                except json.JSONDecodeError as e:
                    print(f"Warning: JSON decode error at line {line_num}: {e}")

        print(f"Loaded {len(evalperf_data)} EvalPerf tasks")
        return evalperf_data

    def _load_original_data(self, dataset_path: str) -> Dict[str, Any]:
        """加载原始数据集（HumanEval或MBPP）"""
        data = {}
        with open(dataset_path, 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line.strip())
                data[item['task_id']] = item
        return data

    def _get_canonical_solution(self, task_id: str) -> Tuple[str, float]:
        """获取EvalPerf中性能最佳的canonical solution"""
        if task_id not in self.evalperf_data:
            raise ValueError(f"Task {task_id} not found in EvalPerf dataset")

        item = self.evalperf_data[task_id]
        references = item['reference']
        scores = item['scores']

        # 找到最高分数的索引
        best_idx = scores.index(max(scores))
        best_solution = references[best_idx]
        best_score = scores[best_idx]

        return best_solution, best_score

    def _get_original_canonical_solution(self, task_id: str) -> str:
        """获取原始数据集中的canonical solution"""
        if task_id.startswith('HumanEval/'):
            if task_id in self.original_humaneval_data:
                return self.original_humaneval_data[task_id]['canonical_solution']
        elif task_id.startswith('Mbpp/'):
            # MBPP任务ID需要转换：Mbpp/3 -> 3 (整数)
            mbpp_id = int(task_id.split('/')[-1])  # 提取数字部分并转换为整数
            if mbpp_id in self.original_mbpp_data:
                return self.original_mbpp_data[mbpp_id]['code']

        raise ValueError(f"Original canonical solution not found for {task_id}")

    def _load_generated_code(self, model: str, task_id: str) -> Optional[str]:
        """
        加载模型生成的代码

        Args:
            model: 模型名称 (codellama, deepseek, copilot)
            task_id: 任务ID
        """
        # 使用配置类来获取路径
        from evalperf_config import EvalPerfConfig
        config = EvalPerfConfig()

        # 根据task_id确定数据集类型
        if task_id.startswith('HumanEval/'):
            dataset = 'humaneval'
        elif task_id.startswith('Mbpp/'):
            dataset = 'mbpp'
        else:
            return None

        # 使用配置类获取路径
        path = config.get_generated_code_path(model, dataset, task_id)
        if path and os.path.exists(path):
            with open(path, 'r', encoding='utf-8') as f:
                return f.read()

        return None

    def _extract_function_from_code(self, code: str, entry_point: str) -> str:
        """从完整代码中提取目标函数"""
        try:
            # 解析AST来提取函数
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and node.name == entry_point:
                    # 重新构建函数代码
                    function_lines = code.split('\n')[node.lineno-1:node.end_lineno]
                    return '\n'.join(function_lines)
        except:
            pass

        # 回退到简单的字符串匹配
        lines = code.split('\n')
        function_lines = []
        in_function = False
        indent_level = 0

        for line in lines:
            if line.strip().startswith(f'def {entry_point}('):
                in_function = True
                indent_level = len(line) - len(line.lstrip())
                function_lines.append(line)
            elif in_function:
                if line.strip() == '':
                    function_lines.append(line)
                elif len(line) - len(line.lstrip()) > indent_level:
                    function_lines.append(line)
                else:
                    break

        return '\n'.join(function_lines)

    def _run_performance_test(self,
                            function_code: str,
                            entry_point: str,
                            pe_input: str,
                            num_runs: int = 10,
                            timeout_multiplier: float = 10.0,
                            baseline_time: float = None) -> Dict[str, Any]:
        """
        运行性能测试 - 使用与原来相同的方法，支持基于baseline的超时控制

        Args:
            function_code: 函数代码
            entry_point: 函数入口点
            pe_input: 性能测试输入
            num_runs: 重复运行次数
            timeout_multiplier: 超时倍数（相对于baseline_time）
            baseline_time: 基准时间（canonical solution的执行时间）
        """
        # 解析pe_input
        try:
            pe_input_data = json.loads(pe_input) if isinstance(pe_input, str) else pe_input
        except:
            pe_input_data = pe_input

        results = {
            'execution_times': [],
            'memory_usages': [],
            'cpu_usages_list': [],  # 存储每次运行的CPU使用率列表
            'success_count': 0,
            'error_count': 0,
            'errors': []
        }

        for run_idx in range(num_runs):
            try:
                # 创建临时文件执行代码 - 不使用预处理，直接运行
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                    test_code = f"""
from typing import List, Dict, Any, Optional, Tuple
import json

{function_code}

def main():
    # 解析输入数据
    pe_input_data = {repr(pe_input_data)}

    # 根据pe_input_data的结构调用函数，运行所有测试用例
    if isinstance(pe_input_data, list):
        # 对于EvalPerf，通常是三层嵌套的列表
        # 最外层是测试用例列表，每个测试用例包含函数的参数
        for test_case in pe_input_data:
            if isinstance(test_case, list):
                # test_case是参数列表，使用*解包传递给函数
                {entry_point}(*test_case)
            else:
                # 如果test_case不是列表，直接传递
                {entry_point}(test_case)
    else:
        # 单个参数
        {entry_point}(pe_input_data)

if __name__ == "__main__":
    main()
"""
                    f.write(test_code)
                    temp_file = f.name

                # 计算动态超时时间
                if baseline_time is not None:
                    dynamic_timeout = max(30, baseline_time * timeout_multiplier)  # 最少30秒
                else:
                    dynamic_timeout = 120  # 默认120秒

                # 使用与原来相同的方法进行性能测试
                single_result = self._run_single_performance_test(temp_file, timeout=dynamic_timeout)

                if single_result:
                    results['execution_times'].append(single_result['execution_time'])
                    results['memory_usages'].append(single_result['memory_usage'])
                    results['cpu_usages_list'].append(single_result['cpu_usages'])
                    results['success_count'] += 1
                else:
                    results['error_count'] += 1
                    results['errors'].append("Performance test failed")

                # 清理临时文件
                os.unlink(temp_file)

            except Exception as e:
                results['error_count'] += 1
                results['errors'].append(str(e))

        # 计算统计信息
        if results['execution_times']:
            results['avg_execution_time'] = statistics.mean(results['execution_times'])
            results['std_execution_time'] = statistics.stdev(results['execution_times']) if len(results['execution_times']) > 1 else 0
            results['avg_memory_usage'] = statistics.mean(results['memory_usages'])
            results['std_memory_usage'] = statistics.stdev(results['memory_usages']) if len(results['memory_usages']) > 1 else 0

            # 对CPU使用率进行处理 - 计算每次运行的平均CPU使用率，然后求总平均
            avg_cpu_per_run = []
            for cpu_list in results['cpu_usages_list']:
                if cpu_list:  # 如果CPU列表不为空
                    avg_cpu_per_run.append(statistics.mean(cpu_list))
                else:
                    avg_cpu_per_run.append(0.0)

            results['avg_cpu_usage'] = statistics.mean(avg_cpu_per_run) if avg_cpu_per_run else 0.0
            results['std_cpu_usage'] = statistics.stdev(avg_cpu_per_run) if len(avg_cpu_per_run) > 1 else 0.0

        return results

    def _indent_code(self, code: str, indent_spaces: int) -> str:
        """为代码添加缩进"""
        indent = ' ' * indent_spaces
        lines = code.split('\n')
        indented_lines = [indent + line if line.strip() else line for line in lines]
        return '\n'.join(indented_lines)

    def _calculate_statistical_tests(self,
                                   generated_data: List[float],
                                   canonical_data: List[float],
                                   metric_name: str) -> Dict[str, Any]:
        """
        计算统计测试结果

        Args:
            generated_data: 生成代码的测量数据
            canonical_data: canonical solution的测量数据
            metric_name: 指标名称 (execution_time, memory_usage, cpu_usage)
        """
        if len(generated_data) < 2 or len(canonical_data) < 2:
            return {
                'error': 'Insufficient data for statistical tests',
                'n_generated': len(generated_data),
                'n_canonical': len(canonical_data)
            }

        # 基础统计
        gen_mean = np.mean(generated_data)
        gen_std = np.std(generated_data, ddof=1)
        canon_mean = np.mean(canonical_data)
        canon_std = np.std(canonical_data, ddof=1)

        # 置信区间 (95%)
        gen_ci = stats.t.interval(0.95, len(generated_data)-1,
                                 loc=gen_mean,
                                 scale=gen_std/np.sqrt(len(generated_data)))
        canon_ci = stats.t.interval(0.95, len(canonical_data)-1,
                                   loc=canon_mean,
                                   scale=canon_std/np.sqrt(len(canonical_data)))

        # 配对t检验 (如果样本数量相同)
        paired_test_result = None
        if len(generated_data) == len(canonical_data):
            try:
                t_stat, p_value = stats.ttest_rel(generated_data, canonical_data)
                paired_test_result = {
                    't_statistic': float(t_stat),
                    'p_value': float(p_value),
                    'significant': bool(p_value < 0.05),
                    'test_type': 'paired_t_test'
                }
            except:
                paired_test_result = {'error': 'Paired t-test failed'}

        # 独立样本t检验
        try:
            t_stat, p_value = stats.ttest_ind(generated_data, canonical_data)
            independent_test_result = {
                't_statistic': float(t_stat),
                'p_value': float(p_value),
                'significant': bool(p_value < 0.05),
                'test_type': 'independent_t_test'
            }
        except:
            independent_test_result = {'error': 'Independent t-test failed'}

        # Wilcoxon符号秩检验 (非参数，配对)
        wilcoxon_result = None
        if len(generated_data) == len(canonical_data):
            try:
                w_stat, p_value = stats.wilcoxon(generated_data, canonical_data)
                wilcoxon_result = {
                    'w_statistic': float(w_stat),
                    'p_value': float(p_value),
                    'significant': bool(p_value < 0.05),
                    'test_type': 'wilcoxon_signed_rank'
                }
            except:
                wilcoxon_result = {'error': 'Wilcoxon test failed'}

        # Mann-Whitney U检验 (非参数，独立样本)
        try:
            u_stat, p_value = stats.mannwhitneyu(generated_data, canonical_data,
                                                alternative='two-sided')
            mannwhitney_result = {
                'u_statistic': float(u_stat),
                'p_value': float(p_value),
                'significant': bool(p_value < 0.05),
                'test_type': 'mann_whitney_u'
            }
        except:
            mannwhitney_result = {'error': 'Mann-Whitney U test failed'}

        # 效应量 (Cohen's d)
        try:
            pooled_std = np.sqrt(((len(generated_data)-1)*gen_std**2 +
                                (len(canonical_data)-1)*canon_std**2) /
                               (len(generated_data) + len(canonical_data) - 2))

            # 避免除零错误
            if pooled_std == 0:
                # 如果标准差为0，说明所有数据点相同
                if gen_mean == canon_mean:
                    cohens_d = 0.0  # 没有差异
                else:
                    cohens_d = float('inf') if gen_mean > canon_mean else float('-inf')
            else:
                cohens_d = (gen_mean - canon_mean) / pooled_std

            # 效应量解释
            if np.isinf(cohens_d):
                effect_size_interpretation = 'infinite'
            elif abs(cohens_d) < 0.2:
                effect_size_interpretation = 'negligible'
            elif abs(cohens_d) < 0.5:
                effect_size_interpretation = 'small'
            elif abs(cohens_d) < 0.8:
                effect_size_interpretation = 'medium'
            else:
                effect_size_interpretation = 'large'

            effect_size = {
                'cohens_d': float(cohens_d) if not np.isinf(cohens_d) else None,
                'interpretation': effect_size_interpretation,
                'direction': 'generated_better' if cohens_d < 0 else 'canonical_better'
            }
        except Exception as e:
            effect_size = {'error': f'Effect size calculation failed: {str(e)}'}

        return {
            'metric': metric_name,
            'descriptive_stats': {
                'generated': {
                    'mean': float(gen_mean),
                    'std': float(gen_std),
                    'n': len(generated_data),
                    'confidence_interval_95': [float(gen_ci[0]), float(gen_ci[1])]
                },
                'canonical': {
                    'mean': float(canon_mean),
                    'std': float(canon_std),
                    'n': len(canonical_data),
                    'confidence_interval_95': [float(canon_ci[0]), float(canon_ci[1])]
                }
            },
            'statistical_tests': {
                'paired_t_test': paired_test_result,
                'independent_t_test': independent_test_result,
                'wilcoxon_signed_rank': wilcoxon_result,
                'mann_whitney_u': mannwhitney_result
            },
            'effect_size': effect_size,
            'performance_ratio': {
                'mean_ratio': float(gen_mean / canon_mean) if canon_mean != 0 else float('inf'),
                'better_performance': bool(gen_mean < canon_mean if metric_name == 'execution_time' else gen_mean > canon_mean)
            }
        }

    def _run_single_performance_test(self, script_path: str, timeout: float = 120) -> Optional[Dict[str, Any]]:
        """
        运行单次性能测试 - 使用与原来完全相同的方法
        """
        try:
            # 统一的监控方法：在一次运行中同时监控所有指标
            import threading

            # 存储结果的变量
            execution_time = 0.0
            max_memory_usage = 0.0
            cpu_usages = []

            # 改进策略：对于短进程，使用简化的监控方法
            # 记录开始时间
            start_time = time.perf_counter()

            # 简化的直接CPU监控方法
            # 启动进程
            process = subprocess.Popen([sys.executable, script_path],
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE)

            # 简化的CPU监控
            cpu_thread = None
            try:
                # 立即获取进程对象并初始化
                ps_process = psutil.Process(process.pid)

                # 记录开始时间和初始CPU状态
                monitor_start_time = time.perf_counter()
                initial_cpu_times = ps_process.cpu_times()
                initial_cpu_time = initial_cpu_times.user + initial_cpu_times.system

                # 初始化CPU监控
                ps_process.cpu_percent()

                # 使用线程进行CPU监控，避免阻塞主进程
                def cpu_monitor():
                    nonlocal cpu_usages
                    try:
                        while process.poll() is None:
                            try:
                                cpu_usage = ps_process.cpu_percent(interval=0.01)
                                if cpu_usage > 0:  # 只记录非零值，减少数据冗余
                                    cpu_usages.append(cpu_usage)
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                break
                    except:
                        pass

                # 启动CPU监控线程
                cpu_thread = threading.Thread(target=cpu_monitor)
                cpu_thread.start()

            except Exception:
                # 如果CPU监控完全失败，至少确保进程能正常运行
                pass

            # 启动内存监控
            def monitor_memory():
                nonlocal max_memory_usage
                try:
                    # memory_usage是阻塞的，会等待进程结束
                    mem_usage = memory_usage(proc=process.pid, interval=0.1, timeout=None)
                    max_memory_usage = max(mem_usage) if mem_usage else 0.0
                except Exception:
                    max_memory_usage = 0.0

            # 启动内存监控线程
            memory_thread = threading.Thread(target=monitor_memory)
            memory_thread.start()

            # 等待进程结束
            stdout, stderr = process.communicate(timeout=timeout)
            end_time = time.perf_counter()

            # 等待监控线程结束
            memory_thread.join(timeout=2)  # 最多等2秒
            if cpu_thread:
                cpu_thread.join(timeout=1)     # 最多等1秒

            # 如果没有捕获到实时CPU数据，使用CPU时间估算
            if not cpu_usages and 'ps_process' in locals():
                try:
                    final_cpu_times = ps_process.cpu_times()
                    final_cpu_time = final_cpu_times.user + final_cpu_times.system
                    cpu_time_used = final_cpu_time - initial_cpu_time
                    wall_time = end_time - monitor_start_time

                    if wall_time > 0 and cpu_time_used > 0:
                        estimated_cpu = (cpu_time_used / wall_time) * 100
                        if estimated_cpu > 0.1:
                            cpu_usages.append(min(estimated_cpu, 800.0))
                except:
                    pass

            # 计算执行时间（作为备用，主要还是用cProfile）
            wall_clock_time = end_time - start_time

            # 如果进程成功结束，使用cProfile获取更精确的执行时间
            if process.returncode == 0:
                profile_file = tempfile.mktemp(suffix='.prof')
                try:
                    # 重新运行一次来获取cProfile数据
                    with open(script_path, "rb") as file:
                        code = compile(file.read(), script_path, 'exec')
                        cProfile.run(code, profile_file)

                    # 提取cumtime
                    p = pstats.Stats(profile_file)
                    if p.stats:
                        max_cumtime = 0.0
                        for func, stats in p.stats.items():
                            if not str(func).startswith('~') and '.py' in str(func):
                                cumtime = stats[3]
                                if cumtime > max_cumtime:
                                    max_cumtime = cumtime
                        execution_time = max_cumtime if max_cumtime > 0 else wall_clock_time
                    else:
                        execution_time = wall_clock_time

                    # 清理profile文件
                    if os.path.exists(profile_file):
                        os.unlink(profile_file)
                except:
                    execution_time = wall_clock_time
            else:
                execution_time = wall_clock_time

            return {
                'execution_time': execution_time,
                'memory_usage': max_memory_usage,
                'cpu_usages': cpu_usages
            }

        except Exception as e:
            return None

    def _monitor_memory_usage(self, process, interval=0.01):
        """监控内存使用 - 与原来的方法完全相同"""
        try:
            mem_usage = memory_usage(proc=process.pid, interval=interval, timeout=None)
            return max(mem_usage) if mem_usage else 0.0
        except:
            return 0.0

    def test_model_on_evalperf(self,
                              model: str,
                              num_runs: int = 10,
                              output_dir: str = "./evalperf_results",
                              max_tasks: int = None,
                              specific_tasks: List[str] = None) -> Dict[str, Any]:
        """
        对指定模型在EvalPerf数据集上进行性能测试

        Args:
            model: 模型名称
            num_runs: 每个任务的重复运行次数
            output_dir: 结果输出目录
            max_tasks: 最大测试任务数量（用于快速测试）
            specific_tasks: 指定要测试的任务列表
        """
        os.makedirs(output_dir, exist_ok=True)

        results = {
            'model': model,
            'total_tasks': 0,
            'tested_tasks': 0,
            'successful_tasks': 0,
            'task_results': {}
        }

        print(f"\n=== Testing {model} on EvalPerf ===")

        # 确定要测试的任务列表
        if specific_tasks:
            # 使用指定的任务列表
            tasks_to_test = [task_id for task_id in specific_tasks if task_id in self.evalperf_data]
            print(f"Testing specific tasks: {len(tasks_to_test)} tasks")
        else:
            # 使用所有任务或限制数量
            tasks_to_test = list(self.evalperf_data.keys())
            if max_tasks:
                tasks_to_test = tasks_to_test[:max_tasks]
                print(f"Testing first {len(tasks_to_test)} tasks (limited by max_tasks={max_tasks})")
            else:
                print(f"Testing all {len(tasks_to_test)} tasks")

        for task_id in tasks_to_test:
            results['total_tasks'] += 1
            print(f"\nProcessing {task_id}...")

            try:
                # 获取EvalPerf任务信息
                evalperf_task = self.evalperf_data[task_id]
                entry_point = evalperf_task['entry_point']
                pe_input = evalperf_task['pe_input']

                # 加载模型生成的代码
                generated_code = self._load_generated_code(model, task_id)
                if generated_code is None:
                    print(f"  Generated code not found for {task_id}")
                    continue

                # 提取函数代码
                generated_function = self._extract_function_from_code(generated_code, entry_point)
                if not generated_function:
                    print(f"  Function {entry_point} not found in generated code")
                    continue

                # 获取canonical solutions
                evalperf_canonical, evalperf_score = self._get_canonical_solution(task_id)
                try:
                    original_canonical = self._get_original_canonical_solution(task_id)
                except:
                    original_canonical = None

                results['tested_tasks'] += 1

                # 步骤1: 先测试EvalPerf canonical solution获取baseline
                print(f"  Testing EvalPerf canonical solution (baseline)...")
                evalperf_canonical_results = self._run_performance_test(
                    evalperf_canonical, entry_point, pe_input, num_runs)

                # 获取baseline时间
                baseline_time = None
                if evalperf_canonical_results['success_count'] > 0:
                    baseline_time = evalperf_canonical_results['avg_execution_time']
                    print(f"  Baseline time: {baseline_time:.6f}s")
                else:
                    print(f"  ⚠️ Canonical solution failed, using default timeout")

                # 步骤2: 测试模型生成的代码（使用baseline超时控制）
                print(f"  Testing generated code...")
                generated_results = self._run_performance_test(
                    generated_function, entry_point, pe_input, num_runs,
                    baseline_time=baseline_time)

                # 检查是否因为性能太差而超时
                if generated_results['success_count'] == 0 and baseline_time is not None:
                    print(f"  ⚠️ Generated code timed out (likely >10x slower than baseline)")
                    generated_results['timeout_due_to_poor_performance'] = True

                # 步骤3: 测试原始canonical solution（如果存在）
                original_canonical_results = None
                if original_canonical:
                    print(f"  Testing original canonical solution...")
                    original_canonical_results = self._run_performance_test(
                        original_canonical, entry_point, pe_input, num_runs,
                        baseline_time=baseline_time)

                # 保存任务结果
                task_result = {
                    'task_id': task_id,
                    'entry_point': entry_point,
                    'evalperf_canonical_score': evalperf_score,
                    'generated_code_results': generated_results,
                    'evalperf_canonical_results': evalperf_canonical_results,
                    'original_canonical_results': original_canonical_results,
                    'performance_comparison': self._compare_performance(
                        generated_results, evalperf_canonical_results, original_canonical_results)
                }

                results['task_results'][task_id] = task_result

                if generated_results['success_count'] > 0:
                    results['successful_tasks'] += 1
                    print(f"  ✓ Success: {generated_results['success_count']}/{num_runs} runs")
                else:
                    print(f"  ✗ Failed: All runs failed")

            except Exception as e:
                print(f"  Error processing {task_id}: {e}")
                continue

        # 保存结果
        result_file = os.path.join(output_dir, f"{model}_evalperf_results.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n=== Results Summary ===")
        print(f"Model: {model}")
        print(f"Total tasks: {results['total_tasks']}")
        print(f"Tested tasks: {results['tested_tasks']}")
        print(f"Successful tasks: {results['successful_tasks']}")
        success_rate = results['successful_tasks']/results['tested_tasks']*100 if results['tested_tasks'] > 0 else 0
        print(f"Success rate: {success_rate:.1f}%")
        print(f"Results saved to: {result_file}")

        return results

    def _compare_performance(self,
                           generated_results: Dict[str, Any],
                           evalperf_canonical_results: Dict[str, Any],
                           original_canonical_results: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """比较性能结果 - 包含完整的统计测试"""
        comparison = {}

        if (generated_results['success_count'] > 0 and
            evalperf_canonical_results['success_count'] > 0):

            # 与EvalPerf canonical solution比较 - 基础比率
            comparison['vs_evalperf_canonical'] = {
                'execution_time_ratio': (generated_results['avg_execution_time'] /
                                       evalperf_canonical_results['avg_execution_time']) if evalperf_canonical_results['avg_execution_time'] > 0 else float('inf'),
                'memory_usage_ratio': (generated_results['avg_memory_usage'] /
                                     evalperf_canonical_results['avg_memory_usage']) if evalperf_canonical_results['avg_memory_usage'] > 0 else float('inf'),
                'cpu_usage_ratio': (generated_results['avg_cpu_usage'] /
                                  evalperf_canonical_results['avg_cpu_usage']) if evalperf_canonical_results['avg_cpu_usage'] > 0 else None
            }

            # 详细统计测试 - 与EvalPerf canonical solution比较
            if (len(generated_results['execution_times']) >= 2 and
                len(evalperf_canonical_results['execution_times']) >= 2):

                comparison['statistical_analysis_vs_evalperf'] = {
                    'execution_time': self._calculate_statistical_tests(
                        generated_results['execution_times'],
                        evalperf_canonical_results['execution_times'],
                        'execution_time'
                    ),
                    'memory_usage': self._calculate_statistical_tests(
                        generated_results['memory_usages'],
                        evalperf_canonical_results['memory_usages'],
                        'memory_usage'
                    )
                }

                # CPU使用率需要特殊处理（因为是列表的列表）
                gen_cpu_means = []
                canon_cpu_means = []
                for cpu_list in generated_results['cpu_usages_list']:
                    if cpu_list:
                        gen_cpu_means.append(np.mean(cpu_list))
                for cpu_list in evalperf_canonical_results['cpu_usages_list']:
                    if cpu_list:
                        canon_cpu_means.append(np.mean(cpu_list))

                if len(gen_cpu_means) >= 2 and len(canon_cpu_means) >= 2:
                    comparison['statistical_analysis_vs_evalperf']['cpu_usage'] = self._calculate_statistical_tests(
                        gen_cpu_means, canon_cpu_means, 'cpu_usage'
                    )

            # 与原始canonical solution比较（如果存在）
            if (original_canonical_results and
                original_canonical_results['success_count'] > 0):

                comparison['vs_original_canonical'] = {
                    'execution_time_ratio': (generated_results['avg_execution_time'] /
                                           original_canonical_results['avg_execution_time']) if original_canonical_results['avg_execution_time'] > 0 else float('inf'),
                    'memory_usage_ratio': (generated_results['avg_memory_usage'] /
                                         original_canonical_results['avg_memory_usage']) if original_canonical_results['avg_memory_usage'] > 0 else float('inf'),
                    'cpu_usage_ratio': (generated_results['avg_cpu_usage'] /
                                      original_canonical_results['avg_cpu_usage']) if original_canonical_results['avg_cpu_usage'] > 0 else None
                }

                # 详细统计测试 - 与原始canonical solution比较
                if (len(generated_results['execution_times']) >= 2 and
                    len(original_canonical_results['execution_times']) >= 2):

                    comparison['statistical_analysis_vs_original'] = {
                        'execution_time': self._calculate_statistical_tests(
                            generated_results['execution_times'],
                            original_canonical_results['execution_times'],
                            'execution_time'
                        ),
                        'memory_usage': self._calculate_statistical_tests(
                            generated_results['memory_usages'],
                            original_canonical_results['memory_usages'],
                            'memory_usage'
                        )
                    }

                    # CPU使用率处理
                    orig_cpu_means = []
                    for cpu_list in original_canonical_results['cpu_usages_list']:
                        if cpu_list:
                            orig_cpu_means.append(np.mean(cpu_list))

                    if len(gen_cpu_means) >= 2 and len(orig_cpu_means) >= 2:
                        comparison['statistical_analysis_vs_original']['cpu_usage'] = self._calculate_statistical_tests(
                            gen_cpu_means, orig_cpu_means, 'cpu_usage'
                        )

        return comparison

    def generate_summary_report(self, results: Dict[str, Any], output_path: str):
        """生成汇总报告 - 包含完整的统计分析"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"# EvalPerf Performance Testing Report with Statistical Analysis\n\n")
            f.write(f"## Model: {results['model']}\n\n")
            f.write(f"- Total tasks: {results['total_tasks']}\n")
            f.write(f"- Tested tasks: {results['tested_tasks']}\n")
            f.write(f"- Successful tasks: {results['successful_tasks']}\n")
            success_rate = results['successful_tasks']/results['tested_tasks']*100 if results['tested_tasks'] > 0 else 0
            f.write(f"- Success rate: {success_rate:.1f}%\n\n")

            # 收集所有统计数据
            execution_time_ratios = []
            memory_usage_ratios = []
            cpu_usage_ratios = []
            significant_improvements = {'execution_time': 0, 'memory_usage': 0, 'cpu_usage': 0}
            significant_degradations = {'execution_time': 0, 'memory_usage': 0, 'cpu_usage': 0}
            effect_sizes = {'execution_time': [], 'memory_usage': [], 'cpu_usage': []}

            for task_id, task_result in results['task_results'].items():
                comparison = task_result.get('performance_comparison', {})

                # 基础比率统计
                if 'vs_evalperf_canonical' in comparison:
                    comp = comparison['vs_evalperf_canonical']
                    # 过滤掉无穷大值和None值
                    if comp['execution_time_ratio'] != float('inf') and comp['execution_time_ratio'] is not None:
                        execution_time_ratios.append(comp['execution_time_ratio'])
                    if comp['memory_usage_ratio'] != float('inf') and comp['memory_usage_ratio'] is not None:
                        memory_usage_ratios.append(comp['memory_usage_ratio'])
                    if comp['cpu_usage_ratio'] != float('inf') and comp['cpu_usage_ratio'] is not None:
                        cpu_usage_ratios.append(comp['cpu_usage_ratio'])

                # 统计显著性分析
                if 'statistical_analysis_vs_evalperf' in comparison:
                    stat_analysis = comparison['statistical_analysis_vs_evalperf']

                    for metric in ['execution_time', 'memory_usage', 'cpu_usage']:
                        if metric in stat_analysis:
                            metric_data = stat_analysis[metric]

                            # 检查统计显著性
                            if 'statistical_tests' in metric_data:
                                tests = metric_data['statistical_tests']
                                # 使用配对t检验结果（如果可用），否则使用独立t检验
                                test_result = tests.get('paired_t_test') or tests.get('independent_t_test')

                                if test_result and 'significant' in test_result and test_result['significant']:
                                    # 检查是改善还是恶化
                                    if 'performance_ratio' in metric_data:
                                        if metric_data['performance_ratio']['better_performance']:
                                            significant_improvements[metric] += 1
                                        else:
                                            significant_degradations[metric] += 1

                            # 收集效应量
                            if ('effect_size' in metric_data and
                                'cohens_d' in metric_data['effect_size'] and
                                metric_data['effect_size']['cohens_d'] is not None):
                                effect_sizes[metric].append(metric_data['effect_size']['cohens_d'])

            # 基础性能统计
            if execution_time_ratios or memory_usage_ratios or cpu_usage_ratios:
                f.write(f"## Performance Ratios vs EvalPerf Canonical Solutions\n\n")

                if execution_time_ratios:
                    exec_std = statistics.stdev(execution_time_ratios) if len(execution_time_ratios) > 1 else 0.0
                    f.write(f"- **Execution Time Ratio**: {statistics.mean(execution_time_ratios):.3f} ± {exec_std:.3f}\n")
                else:
                    f.write(f"- **Execution Time Ratio**: N/A (all values infinite)\n")

                if memory_usage_ratios:
                    mem_std = statistics.stdev(memory_usage_ratios) if len(memory_usage_ratios) > 1 else 0.0
                    f.write(f"- **Memory Usage Ratio**: {statistics.mean(memory_usage_ratios):.3f} ± {mem_std:.3f}\n")
                else:
                    f.write(f"- **Memory Usage Ratio**: N/A (all values infinite)\n")

                if cpu_usage_ratios:
                    cpu_std = statistics.stdev(cpu_usage_ratios) if len(cpu_usage_ratios) > 1 else 0.0
                    f.write(f"- **CPU Usage Ratio**: {statistics.mean(cpu_usage_ratios):.3f} ± {cpu_std:.3f}\n")
                else:
                    f.write(f"- **CPU Usage Ratio**: N/A (all values infinite)\n")

                f.write(f"\n*Ratio < 1.0 means the generated code is better than canonical solution*\n\n")

            # 统计显著性总结
            f.write(f"## Statistical Significance Analysis\n\n")
            f.write(f"### Significant Performance Changes (p < 0.05)\n\n")

            total_tasks = results['successful_tasks']
            for metric in ['execution_time', 'memory_usage', 'cpu_usage']:
                improvements = significant_improvements[metric]
                degradations = significant_degradations[metric]
                f.write(f"**{metric.replace('_', ' ').title()}**:\n")
                if total_tasks > 0:
                    f.write(f"- Significant improvements: {improvements}/{total_tasks} ({improvements/total_tasks*100:.1f}%)\n")
                    f.write(f"- Significant degradations: {degradations}/{total_tasks} ({degradations/total_tasks*100:.1f}%)\n")
                    f.write(f"- No significant change: {total_tasks-improvements-degradations}/{total_tasks} ({(total_tasks-improvements-degradations)/total_tasks*100:.1f}%)\n\n")
                else:
                    f.write(f"- No successful tasks to analyze\n\n")

            # 效应量分析
            f.write(f"### Effect Size Analysis (Cohen's d)\n\n")
            for metric in ['execution_time', 'memory_usage', 'cpu_usage']:
                if effect_sizes[metric]:
                    mean_effect = statistics.mean(effect_sizes[metric])
                    f.write(f"**{metric.replace('_', ' ').title()}**:\n")
                    f.write(f"- Mean effect size: {mean_effect:.3f}\n")

                    # 效应量解释
                    if abs(mean_effect) < 0.2:
                        interpretation = "negligible"
                    elif abs(mean_effect) < 0.5:
                        interpretation = "small"
                    elif abs(mean_effect) < 0.8:
                        interpretation = "medium"
                    else:
                        interpretation = "large"

                    f.write(f"- Effect size interpretation: {interpretation}\n")
                    f.write(f"- Direction: {'Generated code better' if mean_effect < 0 else 'Canonical solution better'}\n\n")

            # 置信区间说明
            f.write(f"## Statistical Methods\n\n")
            f.write(f"- **Confidence Intervals**: 95% confidence intervals calculated using t-distribution\n")
            f.write(f"- **Statistical Tests**: Paired t-test (when sample sizes match), Independent t-test, Wilcoxon signed-rank test, Mann-Whitney U test\n")
            f.write(f"- **Effect Size**: Cohen's d with interpretation (negligible: |d| < 0.2, small: 0.2 ≤ |d| < 0.5, medium: 0.5 ≤ |d| < 0.8, large: |d| ≥ 0.8)\n")
            f.write(f"- **Significance Level**: α = 0.05\n\n")


def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='EvalPerf Performance Testing')
    parser.add_argument('--model', type=str, choices=['codellama', 'deepseek', 'copilot'],
                       help='Test specific model only')
    parser.add_argument('--max-tasks', type=int, default=None,
                       help='Maximum number of tasks to test (for quick testing)')
    parser.add_argument('--tasks', type=str, nargs='+', default=None,
                       help='Specific tasks to test (e.g., HumanEval/0 HumanEval/9)')
    parser.add_argument('--num-runs', type=int, default=3,
                       help='Number of runs per task (default: 3 for quick testing)')

    args = parser.parse_args()

    # 配置路径
    evalperf_dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    original_humaneval_path = "./json-prompt/human-eval-v2-20210705.jsonl"
    original_mbpp_path = "./json-prompt/mbpp.jsonl"
    generated_code_base_path = "./replication-final/result"

    # 创建测试器
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path=evalperf_dataset_path,
        original_humaneval_path=original_humaneval_path,
        original_mbpp_path=original_mbpp_path
    )

    # 确定要测试的模型
    if args.model:
        models = [args.model]
    else:
        models = ['codellama', 'deepseek', 'copilot']

    # 设置测试参数
    test_params = {
        'num_runs': args.num_runs,
        'output_dir': "./evalperf_results",
        'max_tasks': args.max_tasks,
        'specific_tasks': args.tasks
    }

    print(f"Test configuration:")
    print(f"  Models: {models}")
    print(f"  Runs per task: {args.num_runs}")
    if args.max_tasks:
        print(f"  Max tasks: {args.max_tasks}")
    if args.tasks:
        print(f"  Specific tasks: {args.tasks}")

    for model in models:
        print(f"\n{'='*50}")
        print(f"Testing {model}")
        print(f"{'='*50}")

        results = tester.test_model_on_evalperf(model=model, **test_params)

        # 生成报告
        report_path = f"./evalperf_results/{model}_summary_report.md"
        tester.generate_summary_report(results, report_path)
        print(f"Summary report saved to: {report_path}")


def quick_test():
    """快速测试函数 - 测试少量任务"""
    print("Running quick test with 3 tasks...")

    # 配置路径
    evalperf_dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    original_humaneval_path = "./json-prompt/human-eval-v2-20210705.jsonl"
    original_mbpp_path = "./json-prompt/mbpp.jsonl"
    generated_code_base_path = "./replication-final/result"

    # 创建测试器
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path=evalperf_dataset_path,
        original_humaneval_path=original_humaneval_path,
        original_mbpp_path=original_mbpp_path
    )

    # 快速测试：只测试copilot，3个任务，每个任务2次
    quick_tasks = ['HumanEval/0', 'HumanEval/9', 'HumanEval/1']

    results = tester.test_model_on_evalperf(
        model='copilot',
        num_runs=2,
        output_dir="./evalperf_results",
        specific_tasks=quick_tasks
    )

    print(f"\n=== Quick Test Results ===")
    print(f"Tested {results['successful_tasks']}/{results['tested_tasks']} tasks successfully")

    return results


if __name__ == "__main__":
    main()
