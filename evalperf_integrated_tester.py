#!/usr/bin/env python3
"""
EvalPerf集成性能测试框架
基于现有的模型生成结果，在EvalPerf数据集上进行性能测试
"""

import json
import os
import time
import psutil
import tracemalloc
import statistics
import subprocess
import sys
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import tempfile
import ast


class EvalPerfIntegratedTester:
    def __init__(self,
                 evalperf_dataset_path: str,
                 original_humaneval_path: str,
                 original_mbpp_path: str,
                 generated_code_base_path: str):
        """
        初始化EvalPerf集成测试器

        Args:
            evalperf_dataset_path: EvalPerf数据集路径
            original_humaneval_path: 原始HumanEval数据集路径
            original_mbpp_path: 原始MBPP数据集路径
            generated_code_base_path: 模型生成代码的基础路径
        """
        self.evalperf_dataset_path = evalperf_dataset_path
        self.original_humaneval_path = original_humaneval_path
        self.original_mbpp_path = original_mbpp_path
        self.generated_code_base_path = generated_code_base_path

        # 加载数据集
        self.evalperf_data = self._load_evalperf_data()
        self.original_humaneval_data = self._load_original_data(original_humaneval_path)
        self.original_mbpp_data = self._load_original_data(original_mbpp_path)

    def _load_evalperf_data(self) -> Dict[str, Any]:
        """加载EvalPerf数据集"""
        evalperf_data = {}
        print(f"Loading EvalPerf dataset from {self.evalperf_dataset_path}")

        with open(self.evalperf_dataset_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    item = json.loads(line.strip())
                    evalperf_data[item['task_id']] = item
                except json.JSONDecodeError as e:
                    print(f"Warning: JSON decode error at line {line_num}: {e}")

        print(f"Loaded {len(evalperf_data)} EvalPerf tasks")
        return evalperf_data

    def _load_original_data(self, dataset_path: str) -> Dict[str, Any]:
        """加载原始数据集（HumanEval或MBPP）"""
        data = {}
        with open(dataset_path, 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line.strip())
                data[item['task_id']] = item
        return data

    def _get_canonical_solution(self, task_id: str) -> Tuple[str, float]:
        """获取EvalPerf中性能最佳的canonical solution"""
        if task_id not in self.evalperf_data:
            raise ValueError(f"Task {task_id} not found in EvalPerf dataset")

        item = self.evalperf_data[task_id]
        references = item['reference']
        scores = item['scores']

        # 找到最高分数的索引
        best_idx = scores.index(max(scores))
        best_solution = references[best_idx]
        best_score = scores[best_idx]

        return best_solution, best_score

    def _get_original_canonical_solution(self, task_id: str) -> str:
        """获取原始数据集中的canonical solution"""
        if task_id.startswith('HumanEval/'):
            if task_id in self.original_humaneval_data:
                return self.original_humaneval_data[task_id]['canonical_solution']
        elif task_id.startswith('Mbpp/'):
            if task_id in self.original_mbpp_data:
                return self.original_mbpp_data[task_id]['canonical_solution']

        raise ValueError(f"Original canonical solution not found for {task_id}")

    def _load_generated_code(self, model: str, task_id: str) -> Optional[str]:
        """
        加载模型生成的代码

        Args:
            model: 模型名称 (codellama, deepseek, copilot)
            task_id: 任务ID
        """
        # 使用配置类来获取路径
        from evalperf_config import EvalPerfConfig
        config = EvalPerfConfig()

        # 根据task_id确定数据集类型
        if task_id.startswith('HumanEval/'):
            dataset = 'humaneval'
        elif task_id.startswith('Mbpp/'):
            dataset = 'mbpp'
        else:
            return None

        # 使用配置类获取路径
        path = config.get_generated_code_path(model, dataset, task_id)
        if path and os.path.exists(path):
            with open(path, 'r', encoding='utf-8') as f:
                return f.read()

        return None

    def _extract_function_from_code(self, code: str, entry_point: str) -> str:
        """从完整代码中提取目标函数"""
        try:
            # 解析AST来提取函数
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and node.name == entry_point:
                    # 重新构建函数代码
                    function_lines = code.split('\n')[node.lineno-1:node.end_lineno]
                    return '\n'.join(function_lines)
        except:
            pass

        # 回退到简单的字符串匹配
        lines = code.split('\n')
        function_lines = []
        in_function = False
        indent_level = 0

        for line in lines:
            if line.strip().startswith(f'def {entry_point}('):
                in_function = True
                indent_level = len(line) - len(line.lstrip())
                function_lines.append(line)
            elif in_function:
                if line.strip() == '':
                    function_lines.append(line)
                elif len(line) - len(line.lstrip()) > indent_level:
                    function_lines.append(line)
                else:
                    break

        return '\n'.join(function_lines)

    def _run_performance_test(self,
                            function_code: str,
                            entry_point: str,
                            pe_input: str,
                            num_runs: int = 10) -> Dict[str, Any]:
        """
        运行性能测试

        Args:
            function_code: 函数代码
            entry_point: 函数入口点
            pe_input: 性能测试输入
            num_runs: 重复运行次数
        """
        # 解析pe_input
        try:
            pe_input_data = json.loads(pe_input) if isinstance(pe_input, str) else pe_input
        except:
            pe_input_data = pe_input

        results = {
            'execution_times': [],
            'memory_usages': [],
            'cpu_usages': [],
            'success_count': 0,
            'error_count': 0,
            'errors': []
        }

        for run_idx in range(num_runs):
            try:
                # 创建临时文件执行代码
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                    test_code = f"""
import json
import time
import tracemalloc
import psutil
import os
from typing import List, Dict, Any, Optional, Tuple

{function_code}

def run_test():
    # 解析输入数据
    pe_input_data = {repr(pe_input_data)}

    # 开始内存跟踪
    tracemalloc.start()

    # 获取进程对象
    process = psutil.Process(os.getpid())
    cpu_percent_start = process.cpu_percent()

    # 执行性能测试
    start_time = time.perf_counter()

    # 根据pe_input_data的结构调用函数
    if isinstance(pe_input_data, list):
        # 对于EvalPerf，通常是三层嵌套的列表
        # 最外层是测试用例列表，每个测试用例可能包含多个参数
        for test_case in pe_input_data:
            if isinstance(test_case, list):
                # 如果test_case是列表，将其作为单个参数传递
                {entry_point}(test_case)
            else:
                # 如果test_case不是列表，直接传递
                {entry_point}(test_case)
    else:
        # 单个参数
        {entry_point}(pe_input_data)

    end_time = time.perf_counter()

    # 获取CPU使用率
    cpu_percent_end = process.cpu_percent()

    # 获取内存使用情况
    current, peak = tracemalloc.get_traced_memory()
    tracemalloc.stop()

    # 输出结果
    execution_time = end_time - start_time
    memory_usage = peak / 1024 / 1024  # MB
    cpu_usage = max(cpu_percent_end - cpu_percent_start, 0)

    print("RESULT:" + str(execution_time) + "," + str(memory_usage) + "," + str(cpu_usage))

if __name__ == "__main__":
    run_test()
"""
                    f.write(test_code)
                    temp_file = f.name

                # 执行测试
                result = subprocess.run([sys.executable, temp_file],
                                      capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    # 解析结果
                    for line in result.stdout.split('\n'):
                        if line.startswith('RESULT:'):
                            # 解析简化的格式: RESULT:time,memory,cpu
                            data_str = line[7:]  # 去掉 "RESULT:"
                            parts = data_str.split(',')
                            if len(parts) == 3:
                                execution_time = float(parts[0])
                                memory_usage = float(parts[1])
                                cpu_usage = float(parts[2])
                                results['execution_times'].append(execution_time)
                                results['memory_usages'].append(memory_usage)
                                results['cpu_usages'].append(cpu_usage)
                                results['success_count'] += 1
                            break
                else:
                    results['error_count'] += 1
                    results['errors'].append(result.stderr)

                # 清理临时文件
                os.unlink(temp_file)

            except Exception as e:
                results['error_count'] += 1
                results['errors'].append(str(e))

        # 计算统计信息
        if results['execution_times']:
            results['avg_execution_time'] = statistics.mean(results['execution_times'])
            results['std_execution_time'] = statistics.stdev(results['execution_times']) if len(results['execution_times']) > 1 else 0
            results['avg_memory_usage'] = statistics.mean(results['memory_usages'])
            results['std_memory_usage'] = statistics.stdev(results['memory_usages']) if len(results['memory_usages']) > 1 else 0
            results['avg_cpu_usage'] = statistics.mean(results['cpu_usages'])
            results['std_cpu_usage'] = statistics.stdev(results['cpu_usages']) if len(results['cpu_usages']) > 1 else 0

        return results

    def test_model_on_evalperf(self,
                              model: str,
                              num_runs: int = 10,
                              output_dir: str = "./evalperf_results") -> Dict[str, Any]:
        """
        对指定模型在EvalPerf数据集上进行性能测试

        Args:
            model: 模型名称
            num_runs: 每个任务的重复运行次数
            output_dir: 结果输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        results = {
            'model': model,
            'total_tasks': 0,
            'tested_tasks': 0,
            'successful_tasks': 0,
            'task_results': {}
        }

        print(f"\n=== Testing {model} on EvalPerf ===")

        for task_id in self.evalperf_data:
            results['total_tasks'] += 1
            print(f"\nProcessing {task_id}...")

            try:
                # 获取EvalPerf任务信息
                evalperf_task = self.evalperf_data[task_id]
                entry_point = evalperf_task['entry_point']
                pe_input = evalperf_task['pe_input']

                # 加载模型生成的代码
                generated_code = self._load_generated_code(model, task_id)
                if generated_code is None:
                    print(f"  Generated code not found for {task_id}")
                    continue

                # 提取函数代码
                generated_function = self._extract_function_from_code(generated_code, entry_point)
                if not generated_function:
                    print(f"  Function {entry_point} not found in generated code")
                    continue

                # 获取canonical solutions
                evalperf_canonical, evalperf_score = self._get_canonical_solution(task_id)
                try:
                    original_canonical = self._get_original_canonical_solution(task_id)
                except:
                    original_canonical = None

                results['tested_tasks'] += 1

                # 测试模型生成的代码
                print(f"  Testing generated code...")
                generated_results = self._run_performance_test(
                    generated_function, entry_point, pe_input, num_runs)

                # 测试EvalPerf canonical solution
                print(f"  Testing EvalPerf canonical solution...")
                evalperf_canonical_results = self._run_performance_test(
                    evalperf_canonical, entry_point, pe_input, num_runs)

                # 测试原始canonical solution（如果存在）
                original_canonical_results = None
                if original_canonical:
                    print(f"  Testing original canonical solution...")
                    original_canonical_results = self._run_performance_test(
                        original_canonical, entry_point, pe_input, num_runs)

                # 保存任务结果
                task_result = {
                    'task_id': task_id,
                    'entry_point': entry_point,
                    'evalperf_canonical_score': evalperf_score,
                    'generated_code_results': generated_results,
                    'evalperf_canonical_results': evalperf_canonical_results,
                    'original_canonical_results': original_canonical_results,
                    'performance_comparison': self._compare_performance(
                        generated_results, evalperf_canonical_results, original_canonical_results)
                }

                results['task_results'][task_id] = task_result

                if generated_results['success_count'] > 0:
                    results['successful_tasks'] += 1
                    print(f"  ✓ Success: {generated_results['success_count']}/{num_runs} runs")
                else:
                    print(f"  ✗ Failed: All runs failed")

            except Exception as e:
                print(f"  Error processing {task_id}: {e}")
                continue

        # 保存结果
        result_file = os.path.join(output_dir, f"{model}_evalperf_results.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n=== Results Summary ===")
        print(f"Model: {model}")
        print(f"Total tasks: {results['total_tasks']}")
        print(f"Tested tasks: {results['tested_tasks']}")
        print(f"Successful tasks: {results['successful_tasks']}")
        print(f"Success rate: {results['successful_tasks']/results['tested_tasks']*100:.1f}%")
        print(f"Results saved to: {result_file}")

        return results

    def _compare_performance(self,
                           generated_results: Dict[str, Any],
                           evalperf_canonical_results: Dict[str, Any],
                           original_canonical_results: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """比较性能结果"""
        comparison = {}

        if (generated_results['success_count'] > 0 and
            evalperf_canonical_results['success_count'] > 0):

            # 与EvalPerf canonical solution比较
            comparison['vs_evalperf_canonical'] = {
                'execution_time_ratio': (generated_results['avg_execution_time'] /
                                       evalperf_canonical_results['avg_execution_time']),
                'memory_usage_ratio': (generated_results['avg_memory_usage'] /
                                     evalperf_canonical_results['avg_memory_usage']),
                'cpu_usage_ratio': (generated_results['avg_cpu_usage'] /
                                  evalperf_canonical_results['avg_cpu_usage'])
            }

            # 与原始canonical solution比较（如果存在）
            if (original_canonical_results and
                original_canonical_results['success_count'] > 0):
                comparison['vs_original_canonical'] = {
                    'execution_time_ratio': (generated_results['avg_execution_time'] /
                                           original_canonical_results['avg_execution_time']),
                    'memory_usage_ratio': (generated_results['avg_memory_usage'] /
                                         original_canonical_results['avg_memory_usage']),
                    'cpu_usage_ratio': (generated_results['avg_cpu_usage'] /
                                      original_canonical_results['avg_cpu_usage'])
                }

        return comparison

    def generate_summary_report(self, results: Dict[str, Any], output_path: str):
        """生成汇总报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"# EvalPerf Performance Testing Report\n\n")
            f.write(f"## Model: {results['model']}\n\n")
            f.write(f"- Total tasks: {results['total_tasks']}\n")
            f.write(f"- Tested tasks: {results['tested_tasks']}\n")
            f.write(f"- Successful tasks: {results['successful_tasks']}\n")
            f.write(f"- Success rate: {results['successful_tasks']/results['tested_tasks']*100:.1f}%\n\n")

            # 性能统计
            execution_time_ratios = []
            memory_usage_ratios = []
            cpu_usage_ratios = []

            for task_id, task_result in results['task_results'].items():
                comparison = task_result.get('performance_comparison', {})
                if 'vs_evalperf_canonical' in comparison:
                    comp = comparison['vs_evalperf_canonical']
                    execution_time_ratios.append(comp['execution_time_ratio'])
                    memory_usage_ratios.append(comp['memory_usage_ratio'])
                    cpu_usage_ratios.append(comp['cpu_usage_ratio'])

            if execution_time_ratios:
                f.write(f"## Performance vs EvalPerf Canonical Solutions\n\n")
                f.write(f"- Average execution time ratio: {statistics.mean(execution_time_ratios):.3f}\n")
                f.write(f"- Average memory usage ratio: {statistics.mean(memory_usage_ratios):.3f}\n")
                f.write(f"- Average CPU usage ratio: {statistics.mean(cpu_usage_ratios):.3f}\n\n")
                f.write(f"*Ratio < 1.0 means the generated code is better than canonical solution*\n\n")


def main():
    """主函数"""
    # 配置路径
    evalperf_dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    original_humaneval_path = "./json-prompt/human-eval-v2-20210705.jsonl"
    original_mbpp_path = "./json-prompt/mbpp.jsonl"
    generated_code_base_path = "./replication-final/result"

    # 创建测试器
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path=evalperf_dataset_path,
        original_humaneval_path=original_humaneval_path,
        original_mbpp_path=original_mbpp_path,
        generated_code_base_path=generated_code_base_path
    )

    # 测试模型列表
    models = ['codellama', 'deepseek', 'copilot']

    for model in models:
        print(f"\n{'='*50}")
        print(f"Testing {model}")
        print(f"{'='*50}")

        results = tester.test_model_on_evalperf(
            model=model,
            num_runs=10,  # 每个任务重复10次
            output_dir="./evalperf_results"
        )

        # 生成报告
        report_path = f"./evalperf_results/{model}_summary_report.md"
        tester.generate_summary_report(results, report_path)
        print(f"Summary report saved to: {report_path}")


if __name__ == "__main__":
    main()
