#!/usr/bin/env python3
"""
调试CPU监控问题
"""

import subprocess
import psutil
import time
import tempfile
import threading
from memory_profiler import memory_usage


def test_cpu_monitoring():
    """测试CPU监控"""
    
    # 创建一个CPU密集型的测试脚本
    test_code = """
def cpu_intensive_task():
    total = 0
    for i in range(1000000):
        total += i * i
    return total

def main():
    for _ in range(5):  # 运行5次
        result = cpu_intensive_task()
    print("Task completed")

if __name__ == "__main__":
    main()
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        temp_file = f.name
    
    print("=== CPU监控测试 ===")
    
    # 方法1: 原始方法
    print("1. 原始方法:")
    process = subprocess.Popen([
        'python', temp_file
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    cpu_usages = monitor_cpu_usage_original(process, interval=0.1)
    process.wait()
    
    print(f"   CPU使用率: {cpu_usages}")
    print(f"   CPU数据点数量: {len(cpu_usages)}")
    
    # 方法2: 改进方法 - 检查进程状态而不是CPU使用率
    print("\n2. 改进方法:")
    process = subprocess.Popen([
        'python', temp_file
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    cpu_usages = monitor_cpu_usage_improved(process, interval=0.1)
    process.wait()
    
    print(f"   CPU使用率: {cpu_usages}")
    print(f"   CPU数据点数量: {len(cpu_usages)}")
    
    # 方法3: 并行监控
    print("\n3. 并行监控方法:")
    cpu_usages = []
    
    def cpu_monitor_thread(process_pid):
        try:
            ps_process = psutil.Process(process_pid)
            while ps_process.is_running():
                try:
                    cpu_usage = ps_process.cpu_percent(interval=0.1)
                    cpu_usages.append(cpu_usage)
                except psutil.NoSuchProcess:
                    break
        except:
            pass
    
    process = subprocess.Popen([
        'python', temp_file
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # 启动监控线程
    monitor_thread = threading.Thread(target=cpu_monitor_thread, args=(process.pid,))
    monitor_thread.start()
    
    # 等待进程结束
    process.wait()
    monitor_thread.join(timeout=1)  # 最多等1秒
    
    print(f"   CPU使用率: {cpu_usages}")
    print(f"   CPU数据点数量: {len(cpu_usages)}")
    
    # 清理
    import os
    if os.path.exists(temp_file):
        os.unlink(temp_file)


def monitor_cpu_usage_original(process, interval=0.1):
    """原始的CPU监控方法"""
    try:
        ps_process = psutil.Process(process.pid)
        cpu_usages = []
        while True:
            try:
                cpu_usage = ps_process.cpu_percent(interval=interval)
                print(f"     CPU: {cpu_usage}%")
                if cpu_usage == 0:  # 原始逻辑
                    break
                cpu_usages.append(cpu_usage)
            except psutil.NoSuchProcess:
                break
        return cpu_usages
    except:
        return []


def monitor_cpu_usage_improved(process, interval=0.1):
    """改进的CPU监控方法"""
    try:
        ps_process = psutil.Process(process.pid)
        cpu_usages = []
        while True:
            try:
                # 检查进程是否还在运行
                if not ps_process.is_running():
                    break
                
                cpu_usage = ps_process.cpu_percent(interval=interval)
                print(f"     CPU: {cpu_usage}%")
                cpu_usages.append(cpu_usage)
                
                # 检查进程状态
                if ps_process.status() == psutil.STATUS_ZOMBIE:
                    break
                    
            except psutil.NoSuchProcess:
                break
        return cpu_usages
    except:
        return []


def test_short_running_process():
    """测试短时间运行的进程"""
    print("\n=== 短时间进程测试 ===")
    
    # 创建一个快速结束的脚本
    quick_code = """
print("Quick task")
result = sum(range(100))
print(f"Result: {result}")
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(quick_code)
        temp_file = f.name
    
    process = subprocess.Popen([
        'python', temp_file
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    cpu_usages = monitor_cpu_usage_improved(process, interval=0.05)  # 更短的间隔
    stdout, stderr = process.communicate()
    
    print(f"进程输出: {stdout.decode()}")
    print(f"CPU使用率: {cpu_usages}")
    print(f"CPU数据点数量: {len(cpu_usages)}")
    
    # 清理
    import os
    if os.path.exists(temp_file):
        os.unlink(temp_file)


if __name__ == "__main__":
    test_cpu_monitoring()
    test_short_running_process()
