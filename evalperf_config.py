#!/usr/bin/env python3
"""
EvalPerf测试配置文件
"""

import os
from pathlib import Path

class EvalPerfConfig:
    """EvalPerf测试配置类"""

    def __init__(self):
        # 基础路径配置
        self.workspace_root = Path("/home/<USER>/experiment/copilot/EMSE/EMSE-revision")
        self.evalperf_root = Path("/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf")

        # 数据集路径
        self.evalperf_dataset_path = self.evalperf_root / "evalperf_dataset.jsonl"
        self.original_humaneval_path = self.workspace_root / "json-prompt/human-eval-v2-20210705.jsonl"
        self.original_mbpp_path = self.workspace_root / "json-prompt/mbpp.jsonl"

        # 生成代码路径配置
        self.generated_code_paths = {
            'codellama': {
                'humaneval': [
                    self.workspace_root / "final-performance-compare/check-performance-before-prompt/codellama/HumanEval2-0.0",
                ],
                'mbpp': [
                    self.workspace_root / "final-performance-compare/check-performance-before-prompt/codellama/MBPP-temp0.0",
                ]
            },
            'deepseek': {
                'humaneval': [
                    self.workspace_root / "final-performance-compare/check-performance-before-prompt/deepseek/HumanEval-long-prompt-after-filter",
                ],
                'mbpp': [
                    self.workspace_root / "/home/<USER>/experiment/copilot/EMSE/EMSE-revision/final-performance-compare/check-performance-before-prompt/deepseek/MBPP-long-prompt-before-filter-addtest-aftercheck-after-fail",
                ]
            },
            'copilot': {
                'humaneval': [
                    self.workspace_root / "final-performance-compare/check-performance-before-prompt/copilot/Humaneval-replication/output/Pass/HumanEval",
                ],
                'mbpp': [
                    self.workspace_root / "/home/<USER>/experiment/copilot/EMSE/EMSE-revision/final-performance-compare/check-performance-before-prompt/copilot/MBPP-replication/output/pass/MBPP",
                ]
            }
        }

        # 输出路径
        self.output_dir = self.workspace_root / "evalperf_results"

        # 测试参数
        self.default_num_runs = 10  # 默认重复运行次数
        self.timeout_seconds = 30   # 单次测试超时时间

        # 模型列表
        self.available_models = ['codellama', 'deepseek', 'copilot']

    def get_generated_code_path(self, model: str, dataset: str, task_id: str) -> str:
        """
        获取生成代码的路径

        Args:
            model: 模型名称
            dataset: 数据集名称 (humaneval/mbpp)
            task_id: 任务ID
        """
        if model not in self.generated_code_paths:
            return None

        if dataset not in self.generated_code_paths[model]:
            return None

        # 提取任务编号
        if task_id.startswith('HumanEval/'):
            task_num = task_id.split('/')[-1]
        elif task_id.startswith('Mbpp/'):
            task_num = task_id.split('/')[-1]
        else:
            return None

        # 尝试所有可能的路径
        for base_path in self.generated_code_paths[model][dataset]:
            possible_files = [
                base_path / f"{task_num}.py",
                base_path / f"task_{task_num}.py",
                base_path / f"{task_id.replace('/', '_')}.py",
            ]

            for file_path in possible_files:
                if file_path.exists():
                    return str(file_path)

        return None

    def validate_paths(self):
        """验证配置的路径是否存在"""
        print("Validating configuration paths...")

        # 检查数据集路径
        if not self.evalperf_dataset_path.exists():
            print(f"❌ EvalPerf dataset not found: {self.evalperf_dataset_path}")
            return False
        else:
            print(f"✅ EvalPerf dataset found: {self.evalperf_dataset_path}")

        if not self.original_humaneval_path.exists():
            print(f"❌ Original HumanEval dataset not found: {self.original_humaneval_path}")
            return False
        else:
            print(f"✅ Original HumanEval dataset found: {self.original_humaneval_path}")

        if not self.original_mbpp_path.exists():
            print(f"❌ Original MBPP dataset not found: {self.original_mbpp_path}")
            return False
        else:
            print(f"✅ Original MBPP dataset found: {self.original_mbpp_path}")

        # 检查生成代码路径
        for model in self.available_models:
            print(f"\nChecking {model} generated code paths:")
            for dataset in ['humaneval', 'mbpp']:
                found_any = False
                for path in self.generated_code_paths[model][dataset]:
                    if path.exists():
                        print(f"  ✅ {dataset}: {path}")
                        found_any = True
                        break
                if not found_any:
                    print(f"  ❌ {dataset}: No valid path found")

        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ Output directory: {self.output_dir}")

        return True

    def get_task_statistics(self):
        """获取任务统计信息"""
        import json

        print("\nAnalyzing task availability...")

        # 加载EvalPerf数据
        evalperf_tasks = set()
        with open(self.evalperf_dataset_path, 'r') as f:
            for line in f:
                data = json.loads(line.strip())
                evalperf_tasks.add(data['task_id'])

        print(f"Total EvalPerf tasks: {len(evalperf_tasks)}")

        # 统计每个模型的可用任务
        for model in self.available_models:
            available_tasks = 0
            for task_id in evalperf_tasks:
                dataset = 'humaneval' if task_id.startswith('HumanEval/') else 'mbpp'
                if self.get_generated_code_path(model, dataset, task_id):
                    available_tasks += 1

            print(f"{model}: {available_tasks}/{len(evalperf_tasks)} tasks available ({available_tasks/len(evalperf_tasks)*100:.1f}%)")


def main():
    """测试配置"""
    config = EvalPerfConfig()

    print("=== EvalPerf Configuration ===")
    print(f"Workspace root: {config.workspace_root}")
    print(f"EvalPerf root: {config.evalperf_root}")
    print(f"Output directory: {config.output_dir}")
    print(f"Available models: {config.available_models}")
    print(f"Default runs per task: {config.default_num_runs}")

    # 验证路径
    if config.validate_paths():
        print("\n✅ Configuration validation passed!")

        # 获取任务统计
        config.get_task_statistics()
    else:
        print("\n❌ Configuration validation failed!")
        return False

    return True


if __name__ == "__main__":
    main()
