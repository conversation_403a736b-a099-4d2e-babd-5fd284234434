#!/usr/bin/env python3
"""
调试除零错误
"""

import json
import traceback
from evalperf_integrated_tester import EvalPerfIntegratedTester


def debug_division_error():
    """调试除零错误"""
    
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path="/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl",
        original_humaneval_path="./json-prompt/human-eval-v2-20210705.jsonl",
        original_mbpp_path="./json-prompt/mbpp.jsonl"
    )
    
    # 测试单个任务
    task_id = 'HumanEval/9'
    
    try:
        # 获取任务信息
        evalperf_task = tester.evalperf_data[task_id]
        entry_point = evalperf_task['entry_point']
        pe_input = evalperf_task['pe_input']
        
        print(f"=== 调试 {task_id} ===")
        
        # 获取生成的代码
        generated_code = tester._load_generated_code('copilot', task_id)
        if not generated_code:
            print("❌ Generated code not found")
            return
        
        generated_function = tester._extract_function_from_code(generated_code, entry_point)
        if not generated_function:
            print("❌ Function not found")
            return
        
        print("✅ Function extracted")
        
        # 获取canonical solutions
        evalperf_canonical, evalperf_score = tester._get_canonical_solution(task_id)
        print("✅ EvalPerf canonical solution loaded")
        
        # 测试EvalPerf canonical solution
        print("Testing EvalPerf canonical solution...")
        evalperf_canonical_results = tester._run_performance_test(
            evalperf_canonical, entry_point, pe_input, 3)
        
        print(f"EvalPerf canonical results: {evalperf_canonical_results}")
        
        # 获取baseline时间
        baseline_time = None
        if evalperf_canonical_results['success_count'] > 0:
            baseline_time = evalperf_canonical_results['avg_execution_time']
            print(f"Baseline time: {baseline_time}")
        
        # 测试生成的代码
        print("Testing generated code...")
        generated_results = tester._run_performance_test(
            generated_function, entry_point, pe_input, 3,
            baseline_time=baseline_time)
        
        print(f"Generated results: {generated_results}")
        
        # 测试原始canonical solution
        try:
            original_canonical = tester._get_original_canonical_solution(task_id)
            print("Testing original canonical solution...")
            original_canonical_results = tester._run_performance_test(
                original_canonical, entry_point, pe_input, 3,
                baseline_time=baseline_time)
            print(f"Original canonical results: {original_canonical_results}")
        except Exception as e:
            print(f"Original canonical failed: {e}")
            original_canonical_results = None
        
        # 测试性能比较
        print("Testing performance comparison...")
        comparison = tester._compare_performance(
            generated_results, evalperf_canonical_results, original_canonical_results)
        
        print(f"Comparison: {comparison}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    debug_division_error()
