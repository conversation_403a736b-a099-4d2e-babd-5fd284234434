
# Task Description
'''
Generate the given code snippets for great performance. Focus on:
1. Efficient function calls and API usage.
2. Minimizing inefficiencies in loops, such as reducing string concatenation.
3. Selecting optimal algorithms and avoiding redundant operations.
4. Leveraging built-in language features to improve runtime, memory usage, and CPU efficiency.
'''
'''
# Example 1: Inefficient API Usage Optimization
# Inefficient Example
public void shuffle(int[] array) {
    for (int i = array.length - 1; i > 0; i--) {
        int rand = (int) (Math.random() * (i + 1));   # Inefficient use of Math.random()
        int temp = array[i];
        array[i] = array[rand];
        array[rand] = temp;
    }
}

# Efficient Example
public void shuffle(int[] array) {
    Random random = new Random();  
    for (int i = array.length - 1; i > 0; i--) {
        int rand = random.nextInt(i + 1);     # Efficient use of Random.nextInt()
        int temp = array[i];
        array[i] = array[rand];
        array[rand] = temp;
    }
}

# Example 2: Excessive Recursion Optimization
# Inefficient Example
def is_simple_power(x, n):
    if x == 1:
        return True
    if x < n:
        return False
    if x % n != 0:
        return False
    return is_simple_power(x // n, n)  # Excessive recursion

# Efficient Example
def is_simple_power(x, n):
    if (n == 1): 
        return (x == 1) 
    power = 1
    while (power < x): 
        power = power * n
    return (power == x) 

# Example 3: String Concatenation in Loops Optimization
# Inefficient Example
def string_sequence(n: int) -> str:
    result = ''
    for i in range(n + 1):
        result += str(i) + ' '  # String concatenation in loops is costly
    return result[:-1]

# Efficient Example
def string_sequence(n: int) -> str:
    return ' '.join([str(x) for x in range(n + 1)])  # Built-in join is more memory-efficient

# Example 4: Nested Looping Optimization
# Inefficient Example
def sort_matrix(M):
    n = len(M)
    for i in range(n):
        for j in range(n-i-1):
            if sum(M[j]) > sum(M[j+1]):
                M[j], M[j+1] = M[j+1], M[j]    # Nested looping is inefficient
    return M

# Efficient Example
def sort_matrix(M):
    result = sorted(M, key=sum)    # Efficient use of built-in sorted() function
    return result

# Example 5: Object Creation in Loops Optimization
# Inefficient Example
def add_K_element(test_list, K):
  res = []
  for i in test_list:
    temp = []    # Object creation in loops is inefficient
    for j in i:
      temp.append(j+K)
    res.append(tuple(temp))
  return (res)

# Efficient Example
def add_K_element(test_list, K):
  res = [tuple(j + K for j in sub ) for sub in test_list]    # Efficient object creation using list comprehension
  return (res) 

# Example 6: Missed Mathematical Optimizations Optimization
# Inefficient Example
def ap_sum(a,n,d):
  total = 0
  for i in range(n):
    total += a + i * d    # Missed mathematical optimization
  return total

# Efficient Example
def ap_sum(a,n,d):
  total = (n * (2 * a + (n - 1) * d)) / 2    # Mathematical optimization using a formula
  return total

# Example 7: Suboptimal Conditional Logic Optimization
# Inefficient Example
def greatest_common_divisor(a: int, b: int) -> int:
    while a != b:
        if a > b:
            a -= b   # Suboptimal conditional logic
        else:
            b -= a
    return a

# Efficient Example
def greatest_common_divisor(a: int, b: int) -> int:
    while b:
        a, b = b, a % b   # Mathematical optimization using modulus
    return a

# Example 8: Underutilization of Language Features Optimization
# Inefficient Example
def len_complex(a,b):
  x=a**2
  y=b**2
  z=x+y
  length=z**0.5    # Manual calculation of magnitude
  return length

# Efficient Example
import cmath
def len_complex(a,b):
  cn=complex(a,b)    # Efficient use of built-in complex() function
  length=abs(cn)
  return length

# Example 9: Unused Variables Optimization
# Inefficient Example
def get_gcd(l):
  num1 = l[0]     # Unused variable
  num2 = l[1]     # Unused variable
  gcd = num1
  for i in range(2, len(l)):
    num2 = l[i]
    while(num2):
      num1, num2 = num2, num1 % num2
    gcd = num1
  return gcd

# Efficient Example
def find_gcd(x, y): 
	while(y): 
		x, y = y, x % y 
	return x 
def get_gcd(l):
  num1 = l[0]
  num2 = l[1]
  gcd = find_gcd(num1, num2)
  for i in range(2, len(l)):
    gcd = find_gcd(gcd, l[i])
  return gcd

# Example 10: Unused Variables Optimization
# Inefficient Example
public <T> T newInstance(Class<T> clazz) {
    try {
        return clazz.newInstance();
    } catch (InstantiationException | IllegalAccessException e) {    
        throw new RuntimeException(e);    # Improper exception handling
    }
}

# Efficient Example
public <T> T newInstance(Class<T> clazz) {
    try {
        return clazz.getDeclaredConstructor().newInstance();
    } catch (InstantiationException e) {
        throw new IllegalArgumentException("Class cannot be instantiated: " + clazz.getName(), e);
    } catch (IllegalAccessException e) {
        throw new IllegalStateException("Access violation when instantiating class: " + clazz.getName(), e);
    } catch (NoSuchMethodException e) {
        throw new IllegalArgumentException("No default constructor found for class: " + clazz.getName(), e);
    } catch (InvocationTargetException e) {
        throw new RuntimeException("Constructor threw an exception for class: " + clazz.getName(), e.getCause());
    }
}
'''
'''Write a function to find a path with the maximum average over all existing paths for the given square matrix of size n*n.
'''

def maxAverageOfPath(cost, N): 
	dp = [[0 for i in range(N + 1)] for j in range(N + 1)] 
	dp[0][0] = cost[0][0] 
	for i in range(1, N): 
		dp[i][0] = dp[i - 1][0] + cost[i][0] 
	for j in range(1, N): 
		dp[0][j] = dp[0][j - 1] + cost[0][j] 
	for i in range(1, N): 
		for j in range(1, N): 
			dp[i][j] = max(dp[i - 1][j], 
						dp[i][j - 1]) + cost[i][j] 
	return dp[N - 1][N - 1] / (2 * N - 1)

'''
Standard answer: 
M = 100
def maxAverageOfPath(cost, N): 
	dp = [[0 for i in range(N + 1)] for j in range(N + 1)] 
	dp[0][0] = cost[0][0] 
	for i in range(1, N): 
		dp[i][0] = dp[i - 1][0] + cost[i][0] 
	for j in range(1, N): 
		dp[0][j] = dp[0][j - 1] + cost[0][j] 
	for i in range(1, N): 
		for j in range(1, N): 
			dp[i][j] = max(dp[i - 1][j], 
						dp[i][j - 1]) + cost[i][j] 
	return dp[N - 1][N - 1] / (2 * N - 1)
'''

assert maxAverageOfPath([[1, 2, 3], [6, 5, 4], [7, 3, 9]], 3) == 5.2
assert maxAverageOfPath([[2, 3, 4], [7, 6, 5], [8, 4, 10]], 3) == 6.2
assert maxAverageOfPath([[3, 4, 5], [8, 7, 6], [9, 5, 11]], 3) == 7.2 

'''
Write a function to find a path with the maximum average over all existing paths for the given square matrix of size n*n.
'''

def maxAverageOfPath(cost, N): 
	dp = [[0 for i in range(N + 1)] for j in range(N + 1)] 
	dp[0][0] = cost[0][0] 
	for i in range(1, N): 
		dp[i][0] = dp[i - 1][0] + cost[i][0] 
	for j in range(1, N): 
		dp[0][j] = dp[0][j - 1] + cost[0][j] 
	for i in range(1, N): 
		for j in range(1, N): 
			dp[i][j] = max(dp[i - 1][j], 
						dp[i][j - 1]) + cost[i][j] 
	return dp[N - 1][N - 1] / (2 * N - 1)

'''
Standard answer: 
M = 100
def maxAverageOfPath(cost, N): 
	dp = [[0 for i in range(N + 1)] for j in range(N + 1)] 
	dp[0][0] = cost[0][0] 
	for i in range(1, N): 
		dp[i][0] = dp[i - 1][0] + cost[i][0] 
	for j in range(1, N): 
		dp[0][j] = dp[0][j - 1] + cost[0][j] 
	for i in range(1, N): 
		for j in range(1, N): 
			dp[i][j] = max(dp[i - 1][j], 
						dp[i][j - 1]) + cost[i][j] 
	return dp[N - 1][N - 1] / (2 * N - 1)
'''

assert maxAverageOfPath([[1, 2, 3], [6, 5, 4], [7, 3, 9]], 3) == 5.2
assert maxAverageOfPath([[2, 3, 4], [7, 6, 5], [8, 4, 10]], 3) == 6.2
assert maxAverageOfPath([[3, 4, 5], [8, 7, 6], [9, 5, 11]], 3) == 7.2 
