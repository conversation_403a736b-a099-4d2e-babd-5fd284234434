#!/usr/bin/env python3
"""
EvalPerf数据集分析和预处理脚本
用于分析EvalPerf数据集的结构，并为性能测试做准备
"""

import json
import os
from typing import Dict, List, Any
from collections import Counter
import statistics

class EvalPerfAnalyzer:
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.data = []
        self.load_data()
    
    def load_data(self):
        """加载EvalPerf数据集"""
        print(f"正在加载数据集: {self.dataset_path}")
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    self.data.append(data)
                except json.JSONDecodeError as e:
                    print(f"警告: 第{line_num}行JSON解析失败: {e}")
        print(f"成功加载 {len(self.data)} 条记录")
    
    def analyze_structure(self):
        """分析数据集结构"""
        print("\n=== EvalPerf数据集结构分析 ===")
        
        if not self.data:
            print("数据集为空")
            return
        
        # 基本统计
        print(f"总记录数: {len(self.data)}")
        
        # 分析字段
        all_keys = set()
        for item in self.data:
            all_keys.update(item.keys())
        print(f"字段列表: {sorted(all_keys)}")
        
        # 分析task_id分布
        task_ids = [item['task_id'] for item in self.data]
        task_counter = Counter(task_ids)
        print(f"唯一Task ID数量: {len(task_counter)}")
        
        # 数据集来源分析
        humaneval_count = sum(1 for tid in task_ids if tid.startswith('HumanEval/'))
        mbpp_count = sum(1 for tid in task_ids if tid.startswith('Mbpp/'))
        other_count = len(task_ids) - humaneval_count - mbpp_count
        
        print(f"HumanEval任务: {humaneval_count}")
        print(f"MBPP任务: {mbpp_count}")
        print(f"其他任务: {other_count}")
        
        # 分析reference数量分布
        ref_counts = [len(item['reference']) for item in self.data]
        print(f"Reference数量统计:")
        print(f"  最小: {min(ref_counts)}")
        print(f"  最大: {max(ref_counts)}")
        print(f"  平均: {statistics.mean(ref_counts):.2f}")
        print(f"  中位数: {statistics.median(ref_counts)}")
        
        # 分析scores数量分布
        score_counts = [len(item['scores']) for item in self.data]
        print(f"Scores数量统计:")
        print(f"  最小: {min(score_counts)}")
        print(f"  最大: {max(score_counts)}")
        print(f"  平均: {statistics.mean(score_counts):.2f}")
        
        return {
            'total_records': len(self.data),
            'unique_tasks': len(task_counter),
            'humaneval_count': humaneval_count,
            'mbpp_count': mbpp_count,
            'ref_stats': {
                'min': min(ref_counts),
                'max': max(ref_counts),
                'mean': statistics.mean(ref_counts),
                'median': statistics.median(ref_counts)
            }
        }
    
    def analyze_pe_input(self):
        """分析性能测试输入数据结构"""
        print("\n=== PE Input结构分析 ===")
        
        pe_input_sizes = []
        pe_input_types = []
        
        for i, item in enumerate(self.data[:5]):  # 分析前5个样本
            pe_input = json.loads(item['pe_input']) if isinstance(item['pe_input'], str) else item['pe_input']
            pe_input_sizes.append(len(str(pe_input)))
            pe_input_types.append(type(pe_input).__name__)
            
            print(f"样本 {i+1} ({item['task_id']}):")
            print(f"  PE Input类型: {type(pe_input)}")
            print(f"  PE Input大小: {len(str(pe_input))} 字符")
            
            if isinstance(pe_input, list) and len(pe_input) > 0:
                print(f"  列表长度: {len(pe_input)}")
                first_item = pe_input[0]
                if isinstance(first_item, list) and len(first_item) > 0:
                    print(f"  嵌套列表长度: {len(first_item)}")
                    if isinstance(first_item[0], list):
                        print(f"  三层嵌套，第一个子列表长度: {len(first_item[0])}")
        
        print(f"\nPE Input大小统计:")
        print(f"  最小: {min(pe_input_sizes)} 字符")
        print(f"  最大: {max(pe_input_sizes)} 字符")
        print(f"  平均: {statistics.mean(pe_input_sizes):.0f} 字符")
    
    def extract_task_info(self) -> List[Dict[str, Any]]:
        """提取任务信息，用于后续处理"""
        task_info = []
        
        for item in self.data:
            info = {
                'task_id': item['task_id'],
                'entry_point': item['entry_point'],
                'prompt': item['prompt'],
                'reference_count': len(item['reference']),
                'references': item['reference'],
                'pe_input': item['pe_input'],
                'scores': item['scores'],
                'dataset_source': 'HumanEval' if item['task_id'].startswith('HumanEval/') else 'MBPP'
            }
            task_info.append(info)
        
        return task_info
    
    def save_processed_data(self, output_dir: str):
        """保存预处理后的数据"""
        os.makedirs(output_dir, exist_ok=True)
        
        task_info = self.extract_task_info()
        
        # 按数据集来源分组保存
        humaneval_tasks = [t for t in task_info if t['dataset_source'] == 'HumanEval']
        mbpp_tasks = [t for t in task_info if t['dataset_source'] == 'MBPP']
        
        # 保存HumanEval任务
        humaneval_file = os.path.join(output_dir, 'evalperf_humaneval.jsonl')
        with open(humaneval_file, 'w', encoding='utf-8') as f:
            for task in humaneval_tasks:
                f.write(json.dumps(task, ensure_ascii=False) + '\n')
        print(f"HumanEval任务保存到: {humaneval_file} ({len(humaneval_tasks)}条)")
        
        # 保存MBPP任务
        mbpp_file = os.path.join(output_dir, 'evalperf_mbpp.jsonl')
        with open(mbpp_file, 'w', encoding='utf-8') as f:
            for task in mbpp_tasks:
                f.write(json.dumps(task, ensure_ascii=False) + '\n')
        print(f"MBPP任务保存到: {mbpp_file} ({len(mbpp_tasks)}条)")
        
        # 保存完整的任务信息
        all_tasks_file = os.path.join(output_dir, 'evalperf_all_tasks.jsonl')
        with open(all_tasks_file, 'w', encoding='utf-8') as f:
            for task in task_info:
                f.write(json.dumps(task, ensure_ascii=False) + '\n')
        print(f"所有任务保存到: {all_tasks_file} ({len(task_info)}条)")
        
        # 保存统计信息
        stats = self.analyze_structure()
        stats_file = os.path.join(output_dir, 'dataset_stats.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"统计信息保存到: {stats_file}")

def main():
    # EvalPerf数据集路径
    dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    output_dir = "./evalperf_processed"
    
    # 创建分析器
    analyzer = EvalPerfAnalyzer(dataset_path)
    
    # 执行分析
    analyzer.analyze_structure()
    analyzer.analyze_pe_input()
    
    # 保存预处理数据
    analyzer.save_processed_data(output_dir)
    
    print(f"\n=== 预处理完成 ===")
    print(f"预处理数据保存在: {output_dir}")

if __name__ == "__main__":
    main()
