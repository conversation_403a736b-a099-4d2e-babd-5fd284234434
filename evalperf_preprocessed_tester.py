#!/usr/bin/env python3
"""
EvalPerf Canonical Solution 测试器 - 仅测试最高score的canonical solution
使用EvalPerf预处理流程：
1. 使用pe_input作为大规模性能测试数据
2. pre_for_crofile.py (函数封装)
3. <EMAIL> (@profile装饰器)
4. cprofile.py (执行时间)
5. memory.py (内存使用)
6. cpu.py (CPU使用，改进版)
"""

import os
import tempfile
import subprocess
import cProfile
import pstats
import psutil
from memory_profiler import memory_usage
import json
import argparse
import ast
import shutil
from evalperf_config import EvalPerfConfig

class EvalPerfPreprocessedTester:
    """EvalPerf Canonical Solution 测试器 - 仅测试最高score的canonical solution"""

    def __init__(self, evalperf_dataset_path):
        self.config = EvalPerfConfig()
        self.evalperf_dataset_path = evalperf_dataset_path

        # 创建临时工作目录
        self.temp_dir = tempfile.mkdtemp(prefix='evalperf_test_')

        # 加载EvalPerf数据集
        self.evalperf_tasks = self._load_evalperf_dataset()

    def _load_evalperf_dataset(self):
        """加载EvalPerf数据集"""
        tasks = {}
        with open(self.evalperf_dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    # def step1_add_for_function(self, code_content, loop_count=30):
    #     """步骤1: 添加for循环（模拟add_for_function.py）"""
    #     try:
    #         parsed_code = ast.parse(code_content)

    #         # 查找check函数并添加循环
    #         for node in parsed_code.body:
    #             if isinstance(node, ast.FunctionDef) and node.name == 'check':
    #                 # 创建for循环：for item in range(30):
    #                 loop = ast.For(
    #                     target=ast.Name(id='item', ctx=ast.Store()),
    #                     iter=ast.Call(
    #                         func=ast.Name(id='range', ctx=ast.Load()),
    #                         args=[ast.Constant(n=loop_count)],
    #                         keywords=[]
    #                     ),
    #                     body=node.body,
    #                     orelse=[]
    #                 )
    #                 node.body = [loop]

    #         modified_code = ast.fix_missing_locations(parsed_code)
    #         return ast.unparse(modified_code)
    #     except Exception:
    #         return code_content

    def step2_pre_for_crofile(self, code_content):
        """步骤2: 函数封装（模拟pre_for_crofile.py）"""
        try:
            # 修正原始脚本的错误：封装后必须调用函数才能执行代码
            lines = code_content.splitlines()
            indented_lines = []
            for line in lines:
                if line.strip():  # 非空行才缩进
                    indented_lines.append('    ' + line)
                else:  # 空行保持空行
                    indented_lines.append('')

            indented_code = '\n'.join(indented_lines)
            encapsulated_code = f"def encapsulated_function():\n{indented_code}\n\nencapsulated_function()"
            return encapsulated_code
        except Exception:
            return code_content

    def step3_add_profile(self, code_content):
        """步骤3: 添加@profile装饰器（模拟***************）"""
        # 完全按照原始脚本的逻辑
        modified_code = "from memory_profiler import profile\n@profile\n" + code_content
        return modified_code

    def create_preprocessed_script(self, code_content, test_type="evalperf_canonical", pe_input=None):
        """创建预处理后的脚本 - 仅支持EvalPerf canonical solution"""

        print(f"DEBUG: create_preprocessed_script called with test_type={test_type}, pe_input={'available' if pe_input else 'None'}")

        if pe_input is not None:
            import re
            func_match = re.search(r'def (\w+)\(', code_content)
            func_name = func_match.group(1) if func_match else 'main_function'
            print(f"DEBUG: 检测到函数名: {func_name}")

            # 处理pe_input - 运行测试用例以获得性能数据
            if isinstance(pe_input, list) and pe_input:
                # EvalPerf canonical 包含完整函数定义
                test_code = f"{code_content}\n\n"
                
                # 添加性能测试代码 - 直接使用pe_input作为numbers参数
                test_code += "# Performance test with pe_input\n"
                test_code += f"test_data = {pe_input}\n"
                test_code += f"result = {func_name}(test_data)\n"
                test_code += "print('测试完成，结果长度:', len(result))"
                print(f"DEBUG: 使用pe_input数组 (长度: {len(pe_input)}) 进行性能测试")
            else:
                test_code = f"{code_content}\n\nprint('无pe_input数据')"
                print("DEBUG: pe_input 无效，使用空的测试代码")

            # EvalPerf统一处理：只进行步骤2和3（函数封装 + @profile）
            step2_code = self.step2_pre_for_crofile(test_code)
            final_code = self.step3_add_profile(step2_code)

        else:
            print("DEBUG: pe_input 为 None，使用传统处理方式")
            base_code = f"{code_content}\n\nprint('pe_input为None')"

            # 步骤2: 函数封装
            step2_code = self.step2_pre_for_crofile(base_code)

            # 步骤3: 添加@profile装饰器
            final_code = self.step3_add_profile(step2_code)

        print(f"DEBUG: 预处理后的代码 (前200字符):\n{final_code[:200]}...")
        return final_code

    def step4_run_cprofile(self, script_content):
        """步骤4: 运行cprofile（模拟cprofile.py）"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name

            try:
                # 完全按照原始cprofile.py的逻辑
                with open(script_path, "rb") as file:
                    code = compile(file.read(), script_path, 'exec')

                profile_file = tempfile.mktemp()
                cProfile.run(code, profile_file)

                # 提取cumtime（按照原始方法）
                p = pstats.Stats(profile_file)
                cumtime = p.stats[list(p.stats.keys())[0]][3]

                os.unlink(profile_file)
                return cumtime
            finally:
                os.unlink(script_path)

        except Exception as e:
            print(f"cProfile测量失败: {e}")
            return 0.0

    def step5_run_memory(self, script_content):
        """步骤5: 运行memory（模拟memory.py）"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name

            try:
                # 完全按照原始memory.py的逻辑
                command = ["python3", script_path]
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                mem_usage = memory_usage(proc=process.pid, interval=0.1, timeout=None)
                max_mem = max(mem_usage)

                return max_mem
            finally:
                os.unlink(script_path)

        except Exception as e:
            print(f"内存测量失败: {e}")
            return 0.0

    def step6_run_cpu(self, script_content):
        """步骤6: 运行CPU（改进的cpu.py，修复时序问题）"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name

            try:
                # 改进版CPU监控（修复原始cpu.py的时序问题）
                command = ["python3", script_path]
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # 并行监控内存和CPU
                import threading

                mem_usage = []
                cpu_usages = []

                def memory_monitor():
                    nonlocal mem_usage
                    try:
                        mem_usage = memory_usage(proc=process.pid, interval=0.01, timeout=None)
                    except:
                        pass

                def cpu_monitor():
                    nonlocal cpu_usages
                    try:
                        ps_process = psutil.Process(process.pid)
                        ps_process.cpu_percent()  # 初始化

                        while process.poll() is None:
                            try:
                                cpu_usage = ps_process.cpu_percent(interval=0.1)  # 使用原始间隔
                                if cpu_usage > 0:
                                    cpu_usages.append(cpu_usage)
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                break
                    except:
                        pass

                # 启动监控线程
                mem_thread = threading.Thread(target=memory_monitor)
                cpu_thread = threading.Thread(target=cpu_monitor)

                mem_thread.start()
                cpu_thread.start()

                # 等待进程结束
                process.wait()

                # 等待监控线程结束
                mem_thread.join(timeout=1)
                cpu_thread.join(timeout=1)

                max_mem = max(mem_usage) if mem_usage else 0.0

                return max_mem, cpu_usages
            finally:
                os.unlink(script_path)

        except Exception as e:
            print(f"CPU测量失败: {e}")
            return 0.0, []

    def test_single_task(self, task_id, model, num_runs=5):
        """测试单个任务 - 仅测试EvalPerf最高score的canonical solution"""
        print(f"Testing {task_id} with EvalPerf canonical solution only...")

        results = {
            'task_id': task_id,
            'evalperf_canonical_results': None
        }

        # 检查任务是否存在于EvalPerf数据集中
        if task_id not in self.evalperf_tasks:
            print(f"ERROR: 任务 {task_id} 不存在于EvalPerf数据集中")
            return results

        # 解析pe_input
        pe_input = self._parse_pe_input(task_id)
        
        # 获取最高score的canonical solution
        evalperf_code = self._get_best_canonical_solution(task_id)
        
        if not evalperf_code:
            print(f"ERROR: 无法获取任务 {task_id} 的canonical solution")
            return results

        print(f"DEBUG: EvalPerf Canonical Code (first 200 chars):\n{evalperf_code[:200]}...")

        # 测试EvalPerf Canonical（使用pe_input）
        results['evalperf_canonical_results'] = self._run_complete_pipeline(
            evalperf_code, num_runs, test_type="evalperf_canonical",
            pe_input=pe_input, test_name="EvalPerf Canonical"
        )

        return results

    def _parse_pe_input(self, task_id):
        """解析pe_input数据"""
        try:
            pe_input_raw = self.evalperf_tasks[task_id]['pe_input']
            print(f"DEBUG: pe_input_raw type: {type(pe_input_raw)}, length: {len(pe_input_raw) if isinstance(pe_input_raw, str) else 'N/A'}")
            
            if isinstance(pe_input_raw, str):
                pe_input_parsed = ast.literal_eval(pe_input_raw)
                print(f"DEBUG: pe_input_parsed type: {type(pe_input_parsed)}, length: {len(pe_input_parsed) if isinstance(pe_input_parsed, list) else 'N/A'}")
                
                # pe_input是三层嵌套: [[[数组]]]，我们需要最内层的数组
                if isinstance(pe_input_parsed, list) and pe_input_parsed:
                    level1 = pe_input_parsed[0]  # 第一层解嵌套
                    if isinstance(level1, list) and level1:
                        pe_input = level1[0]  # 第二层解嵌套，得到实际的数字数组
                        print(f"DEBUG: 成功解析pe_input，类型: {type(pe_input)}, 长度: {len(pe_input) if isinstance(pe_input, list) else 'N/A'}")
                        if isinstance(pe_input, list) and len(pe_input) > 0:
                            print(f"DEBUG: pe_input前5个元素: {pe_input[:5]}")
                        return pe_input
            return None
        except Exception as e:
            print(f"DEBUG: pe_input处理失败: {e}")
            return None

    def _get_best_canonical_solution(self, task_id):
        """获取最高score的canonical solution"""
        task_data = self.evalperf_tasks[task_id]
        scores = task_data.get('scores', [])
        references = task_data.get('reference', [])
        
        if scores and references:
            # 找到最高分对应的索引
            max_score_idx = scores.index(max(scores))
            evalperf_code = references[max_score_idx]
            print(f"DEBUG: 选择第{max_score_idx}个reference作为canonical (score: {scores[max_score_idx]})")
            return evalperf_code
        elif references:
            # 备用方案：使用第一个reference
            print("DEBUG: 使用第0个reference作为canonical (无scores数据)")
            return references[0]
        else:
            return None

    def _run_complete_pipeline(self, code_content, num_runs, test_type="generated", pe_input=None, test_name=""):
        """运行完整的原始脚本流水线"""
        execution_times = []
        memory_usages = []
        cpu_usages_list = []

        for run in range(num_runs):
            print(f"  {test_name} - Run {run + 1}/{num_runs}")

            try:
                # 完整的预处理流水线
                preprocessed_code = self.create_preprocessed_script(code_content, test_type=test_type, pe_input=pe_input)

                # 步骤4: 运行cprofile（模拟cprofile.py）
                exec_time = self.step4_run_cprofile(preprocessed_code)
                execution_times.append(exec_time)

                # 步骤5: 运行memory（模拟memory.py）
                memory_usage = self.step5_run_memory(preprocessed_code)
                memory_usages.append(memory_usage)

                # 步骤6: 运行CPU（改进的cpu.py）
                cpu_memory_usage, cpu_usages = self.step6_run_cpu(preprocessed_code)
                cpu_usages_list.append(cpu_usages)

                print(f"    执行时间: {exec_time:.6f}s, 内存: {memory_usage:.3f}MB, CPU数据点: {len(cpu_usages)}")

            except Exception as e:
                print(f"    Run {run + 1} failed: {e}")
                execution_times.append(0.0)
                memory_usages.append(0.0)
                cpu_usages_list.append([])

        # 计算统计数据
        valid_times = [t for t in execution_times if t > 0]
        valid_memories = [m for m in memory_usages if m > 0]

        # 计算平均CPU使用率
        all_cpu_values = []
        for cpu_list in cpu_usages_list:
            if cpu_list:
                all_cpu_values.extend(cpu_list)
        avg_cpu = sum(all_cpu_values) / len(all_cpu_values) if all_cpu_values else 0.0

        return {
            'execution_times': execution_times,
            'memory_usages': memory_usages,
            'cpu_usages_list': cpu_usages_list,
            'avg_execution_time': sum(valid_times) / len(valid_times) if valid_times else 0.0,
            'avg_memory_usage': sum(valid_memories) / len(valid_memories) if valid_memories else 0.0,
            'avg_cpu_usage': avg_cpu,
            'success_count': len(valid_times)
        }

def main():
    parser = argparse.ArgumentParser(description='EvalPerf Preprocessed Performance Tester')
    parser.add_argument('--model', required=True, help='Model name (e.g., copilot)')
    parser.add_argument('--tasks', nargs='+', help='Specific tasks to test (e.g., HumanEval/0 Mbpp/3)')
    parser.add_argument('--num-runs', type=int, default=5, help='Number of runs per task')

    args = parser.parse_args()

    # 初始化测试器
    tester = EvalPerfPreprocessedTester(
        evalperf_dataset_path='/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl'
    )

    print("=== EvalPerf Canonical Solution 性能测试 ===")
    print(f"模型: {args.model}")
    print(f"任务: {args.tasks}")
    print(f"运行次数: {args.num_runs}")
    print("测试策略:")
    print("  - 仅测试EvalPerf最高score的canonical solution")
    print("  - 使用pe_input作为大规模性能测试数据")
    print("  - CPU监控: 改进的并行监控（100ms间隔）")
    print()

    all_results = {}

    for task_id in args.tasks:
        result = tester.test_single_task(task_id, args.model, args.num_runs)
        all_results[task_id] = result

        # 显示结果
        print(f"\n{task_id} 结果:")
        if result['evalperf_canonical_results']:
            data = result['evalperf_canonical_results']
            print("  EvalPerf Canonical:")
            print(f"    执行时间: {data['avg_execution_time']:.6f}s")
            print(f"    内存使用: {data['avg_memory_usage']:.3f}MB")
            print(f"    CPU使用: {data['avg_cpu_usage']:.1f}%")

            # CPU监控成功率
            cpu_success = len([cpu for cpu in data['cpu_usages_list'] if cpu and any(x > 0 for x in cpu)])
            print(f"    CPU监控成功: {cpu_success}/{args.num_runs}")
        else:
            print("  无有效结果")

    # 保存结果
    output_file = f'./preprocessed_results/{args.model}_canonical_results.json'
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    print(f"\n结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
