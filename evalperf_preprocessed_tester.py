#!/usr/bin/env python3
"""
EvalPerf预处理测试器 - 完全按照原始方法的预处理流程
针对EvalPerf的pe_input不添加循环，只进行必要的预处理
"""

import os
import tempfile
import subprocess
import cProfile
import pstats
import psutil
from memory_profiler import memory_usage
import json
import argparse
import ast
from evalperf_config import EvalPerfConfig

class EvalPerfPreprocessedTester:
    """EvalPerf预处理测试器"""

    def __init__(self, evalperf_dataset_path, original_humaneval_path, original_mbpp_path):
        self.config = EvalPerfConfig()
        self.evalperf_dataset_path = evalperf_dataset_path
        self.original_humaneval_path = original_humaneval_path
        self.original_mbpp_path = original_mbpp_path

        # 加载数据集
        self.evalperf_tasks = self._load_evalperf_dataset()
        self.original_humaneval_tasks = self._load_original_dataset(original_humaneval_path)
        self.original_mbpp_tasks = self._load_original_dataset(original_mbpp_path)

    def _load_evalperf_dataset(self):
        """加载EvalPerf数据集"""
        tasks = {}
        with open(self.evalperf_dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    def _load_original_dataset(self, dataset_path):
        """加载原始数据集"""
        tasks = {}
        with open(dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    def preprocess_for_cprofile(self, code_content):
        """预处理：函数封装（模拟pre_for_crofile.py）"""
        try:
            # 将代码缩进并封装到函数中
            indented_code = '\n    '.join(code_content.splitlines())
            encapsulated_code = f"def encapsulated_function():\n    {indented_code}\n\nencapsulated_function()"
            return encapsulated_code
        except Exception:
            # 如果预处理失败，返回原代码
            return code_content

    def add_performance_loop(self, code_content, loop_count=30):
        """添加性能循环（模拟add_for_function.py）"""
        try:
            parsed_code = ast.parse(code_content)

            # 查找check函数并添加循环
            for node in parsed_code.body:
                if isinstance(node, ast.FunctionDef) and node.name == 'check':
                    # 创建for循环：for item in range(30):
                    loop = ast.For(
                        target=ast.Name(id='item', ctx=ast.Store()),
                        iter=ast.Call(
                            func=ast.Name(id='range', ctx=ast.Load()),
                            args=[ast.Constant(n=loop_count)],
                            keywords=[]
                        ),
                        body=node.body,
                        orelse=[]
                    )
                    node.body = [loop]

            modified_code = ast.fix_missing_locations(parsed_code)
            return ast.unparse(modified_code)
        except Exception:
            # 如果AST处理失败，返回原代码
            return code_content

    def create_test_script(self, code_content, test_type="generated", pe_input=None):
        """创建测试脚本"""
        if test_type == "evalperf_canonical" and pe_input is not None:
            # EvalPerf canonical: 使用pe_input，不添加循环，只进行函数封装
            # 处理pe_input格式
            if isinstance(pe_input, list):
                # 简化处理：只使用第一个测试用例
                if pe_input and isinstance(pe_input[0], list):
                    # 获取函数名（假设是rolling_max）
                    import re
                    func_match = re.search(r'def (\w+)\(', code_content)
                    func_name = func_match.group(1) if func_match else 'rolling_max'

                    test_code = f"{code_content}\n\n# Performance test\nfor test_case in {pe_input}:\n    {func_name}(*test_case)"
                else:
                    test_code = f"{code_content}\n\n# Performance test\nresult = {pe_input}"
            else:
                test_code = f"{code_content}\n\n# Performance test\nresult = {pe_input}"

            processed_code = self.preprocess_for_cprofile(test_code)
        else:
            # Generated code 和 original canonical: 添加循环+函数封装
            base_code = f"{code_content}\n\ncheck()"
            loop_code = self.add_performance_loop(base_code)
            processed_code = self.preprocess_for_cprofile(loop_code)

        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(processed_code)
            return f.name

    def run_cprofile_method(self, script_path):
        """运行cprofile方法"""
        try:
            with open(script_path, "rb") as file:
                code = compile(file.read(), script_path, 'exec')

            profile_file = tempfile.mktemp()
            cProfile.run(code, profile_file)

            # 提取cumtime
            p = pstats.Stats(profile_file)
            cumtime = p.stats[list(p.stats.keys())[0]][3]

            os.unlink(profile_file)
            return cumtime
        except Exception as e:
            print(f"cProfile测量失败: {e}")
            return 0.0

    def run_memory_method(self, script_path):
        """运行memory方法"""
        try:
            command = ["python3", script_path]
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            mem_usage = memory_usage(proc=process.pid, interval=0.1, timeout=None)
            max_mem = max(mem_usage)

            return max_mem
        except Exception as e:
            print(f"内存测量失败: {e}")
            return 0.0

    def run_cpu_method(self, script_path):
        """运行CPU方法（改进版，并行监控）"""
        try:
            command = ["python3", script_path]
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # 并行监控内存和CPU
            import threading

            mem_usage = []
            cpu_usages = []

            def memory_monitor():
                nonlocal mem_usage
                try:
                    mem_usage = memory_usage(proc=process.pid, interval=0.01, timeout=None)
                except:
                    pass

            def cpu_monitor():
                nonlocal cpu_usages
                try:
                    ps_process = psutil.Process(process.pid)
                    ps_process.cpu_percent()  # 初始化

                    while process.poll() is None:
                        try:
                            cpu_usage = ps_process.cpu_percent(interval=0.01)  # 10ms间隔
                            if cpu_usage > 0:
                                cpu_usages.append(cpu_usage)
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            break
                except:
                    pass

            # 启动监控线程
            mem_thread = threading.Thread(target=memory_monitor)
            cpu_thread = threading.Thread(target=cpu_monitor)

            mem_thread.start()
            cpu_thread.start()

            # 等待进程结束
            process.wait()

            # 等待监控线程结束
            mem_thread.join(timeout=1)
            cpu_thread.join(timeout=1)

            max_mem = max(mem_usage) if mem_usage else 0.0

            return max_mem, cpu_usages
        except Exception as e:
            print(f"CPU测量失败: {e}")
            return 0.0, []

    def test_single_task(self, task_id, model, num_runs=5):
        """测试单个任务"""
        print(f"Testing {task_id} with preprocessed method...")

        results = {
            'task_id': task_id,
            'generated_code_results': None,
            'evalperf_canonical_results': None,
            'original_canonical_results': None
        }

        # 1. 测试Generated Code
        generated_path = self.config.get_generated_code_path(model,
                                                           'humaneval' if 'HumanEval' in task_id else 'mbpp',
                                                           task_id)
        if generated_path and os.path.exists(generated_path):
            with open(generated_path, 'r') as f:
                generated_code = f.read()

            results['generated_code_results'] = self._run_multiple_times(
                generated_code, num_runs, test_type="generated", test_name="Generated Code"
            )

        # 2. 测试EvalPerf Canonical
        if task_id in self.evalperf_tasks:
            evalperf_code = self.evalperf_tasks[task_id]['reference'][0]
            pe_input = self.evalperf_tasks[task_id]['pe_input'][0]  # 使用第一个pe_input

            results['evalperf_canonical_results'] = self._run_multiple_times(
                evalperf_code, num_runs, test_type="evalperf_canonical",
                pe_input=pe_input, test_name="EvalPerf Canonical"
            )

        # 3. 测试Original Canonical
        if task_id in self.original_humaneval_tasks:
            original_code = self.original_humaneval_tasks[task_id]['canonical_solution']
        elif task_id in self.original_mbpp_tasks:
            original_code = self.original_mbpp_tasks[task_id]['code']
        else:
            original_code = None

        if original_code:
            results['original_canonical_results'] = self._run_multiple_times(
                original_code, num_runs, test_type="original", test_name="Original Canonical"
            )

        return results

    def _run_multiple_times(self, code_content, num_runs, test_type="generated", pe_input=None, test_name=""):
        """多次运行测试"""
        execution_times = []
        memory_usages = []
        cpu_usages_list = []

        for run in range(num_runs):
            print(f"  {test_name} - Run {run + 1}/{num_runs}")

            # 创建测试脚本
            script_path = self.create_test_script(code_content, test_type=test_type, pe_input=pe_input)

            try:
                # 1. 运行cprofile方法
                exec_time = self.run_cprofile_method(script_path)
                execution_times.append(exec_time)

                # 2. 运行改进的CPU方法（并行监控）
                memory_usage, cpu_usages = self.run_cpu_method(script_path)
                memory_usages.append(memory_usage)
                cpu_usages_list.append(cpu_usages)

            except Exception as e:
                print(f"    Run {run + 1} failed: {e}")
                execution_times.append(0.0)
                memory_usages.append(0.0)
                cpu_usages_list.append([])

            finally:
                # 清理临时文件
                if os.path.exists(script_path):
                    os.unlink(script_path)

        # 计算统计数据
        valid_times = [t for t in execution_times if t > 0]
        valid_memories = [m for m in memory_usages if m > 0]

        # 计算平均CPU使用率
        all_cpu_values = []
        for cpu_list in cpu_usages_list:
            if cpu_list:
                all_cpu_values.extend(cpu_list)
        avg_cpu = sum(all_cpu_values) / len(all_cpu_values) if all_cpu_values else 0.0

        return {
            'execution_times': execution_times,
            'memory_usages': memory_usages,
            'cpu_usages_list': cpu_usages_list,
            'avg_execution_time': sum(valid_times) / len(valid_times) if valid_times else 0.0,
            'avg_memory_usage': sum(valid_memories) / len(valid_memories) if valid_memories else 0.0,
            'avg_cpu_usage': avg_cpu,
            'success_count': len(valid_times)
        }

def main():
    parser = argparse.ArgumentParser(description='EvalPerf Preprocessed Performance Tester')
    parser.add_argument('--model', required=True, help='Model name (e.g., copilot)')
    parser.add_argument('--tasks', nargs='+', help='Specific tasks to test (e.g., HumanEval/0 Mbpp/3)')
    parser.add_argument('--num-runs', type=int, default=5, help='Number of runs per task')

    args = parser.parse_args()

    # 初始化测试器
    tester = EvalPerfPreprocessedTester(
        evalperf_dataset_path='/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl',
        original_humaneval_path='./json-prompt/human-eval-v2-20210705.jsonl',
        original_mbpp_path='./json-prompt/mbpp.jsonl'
    )

    print(f"=== EvalPerf预处理性能测试 ===")
    print(f"模型: {args.model}")
    print(f"任务: {args.tasks}")
    print(f"运行次数: {args.num_runs}")
    print(f"预处理策略:")
    print(f"  - EvalPerf canonical: 仅函数封装（不添加循环）")
    print(f"  - Generated/Original: 添加循环 + 函数封装")
    print(f"  - CPU监控: 改进的并行监控（10ms间隔）")
    print()

    all_results = {}

    for task_id in args.tasks:
        result = tester.test_single_task(task_id, args.model, args.num_runs)
        all_results[task_id] = result

        # 显示结果
        print(f"\n{task_id} 结果:")
        for test_type in ['generated_code_results', 'evalperf_canonical_results', 'original_canonical_results']:
            if result[test_type]:
                data = result[test_type]
                print(f"  {test_type}:")
                print(f"    执行时间: {data['avg_execution_time']:.6f}s")
                print(f"    内存使用: {data['avg_memory_usage']:.3f}MB")
                print(f"    CPU使用: {data['avg_cpu_usage']:.1f}%")

                # CPU监控成功率
                cpu_success = len([cpu for cpu in data['cpu_usages_list'] if cpu and any(x > 0 for x in cpu)])
                print(f"    CPU监控成功: {cpu_success}/{args.num_runs}")

    # 保存结果
    output_file = f'./preprocessed_results/{args.model}_preprocessed_results.json'
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    print(f"\n结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
