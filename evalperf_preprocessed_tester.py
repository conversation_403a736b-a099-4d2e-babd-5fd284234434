#!/usr/bin/env python3
"""
EvalPerf预处理测试器 - 完全按照原始脚本的方式工作
模拟原始的预处理和测试流程：
1. add_for_function.py (仅对非EvalPerf代码)
2. pre_for_crofile.py (函数封装)
3. <EMAIL> (@profile装饰器)
4. cprofile.py (执行时间)
5. memory.py (内存使用)
6. cpu.py (CPU使用，改进版)
"""

import os
import tempfile
import subprocess
import cProfile
import pstats
import psutil
from memory_profiler import memory_usage
import json
import argparse
import ast
import shutil
from evalperf_config import EvalPerfConfig

class EvalPerfPreprocessedTester:
    """EvalPerf预处理测试器 - 完全模拟原始脚本流程"""

    def __init__(self, evalperf_dataset_path, original_humaneval_path, original_mbpp_path):
        self.config = EvalPerfConfig()
        self.evalperf_dataset_path = evalperf_dataset_path
        self.original_humaneval_path = original_humaneval_path
        self.original_mbpp_path = original_mbpp_path

        # 创建临时工作目录
        self.temp_dir = tempfile.mkdtemp(prefix='evalperf_test_')

        # 加载数据集
        self.evalperf_tasks = self._load_evalperf_dataset()
        self.original_humaneval_tasks = self._load_original_dataset(original_humaneval_path)
        self.original_mbpp_tasks = self._load_original_dataset(original_mbpp_path)

    def _load_evalperf_dataset(self):
        """加载EvalPerf数据集"""
        tasks = {}
        with open(self.evalperf_dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    def _load_original_dataset(self, dataset_path):
        """加载原始数据集"""
        tasks = {}
        with open(dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    def step1_add_for_function(self, code_content, loop_count=30):
        """步骤1: 添加for循环（模拟add_for_function.py）"""
        try:
            parsed_code = ast.parse(code_content)

            # 查找check函数并添加循环
            for node in parsed_code.body:
                if isinstance(node, ast.FunctionDef) and node.name == 'check':
                    # 创建for循环：for item in range(30):
                    loop = ast.For(
                        target=ast.Name(id='item', ctx=ast.Store()),
                        iter=ast.Call(
                            func=ast.Name(id='range', ctx=ast.Load()),
                            args=[ast.Constant(n=loop_count)],
                            keywords=[]
                        ),
                        body=node.body,
                        orelse=[]
                    )
                    node.body = [loop]

            modified_code = ast.fix_missing_locations(parsed_code)
            return ast.unparse(modified_code)
        except Exception:
            return code_content

    def step2_pre_for_crofile(self, code_content):
        """步骤2: 函数封装（模拟pre_for_crofile.py）"""
        try:
            # 完全按照原始脚本的逻辑
            indented_code = '\n    '.join(code_content.splitlines())
            encapsulated_code = f"def encapsulated_function():\n    {indented_code}\n"
            return encapsulated_code
        except Exception:
            return code_content

    def step3_add_profile(self, code_content):
        """步骤3: 添加@profile装饰器（模拟***************）"""
        # 完全按照原始脚本的逻辑
        modified_code = "from memory_profiler import profile\n@profile\n" + code_content
        return modified_code

    def create_preprocessed_script(self, code_content, test_type="generated", pe_input=None):
        """创建预处理后的脚本 - 完全按照原始流程"""

        if test_type == "evalperf_canonical" and pe_input is not None:
            # EvalPerf canonical: 不添加循环，直接使用pe_input
            # 创建性能测试代码
            import re
            func_match = re.search(r'def (\w+)\(', code_content)
            func_name = func_match.group(1) if func_match else 'main_function'

            # 简化pe_input处理 - 只运行一次，不循环
            if isinstance(pe_input, list) and pe_input:
                # 取第一个测试用例
                first_test = pe_input[0] if isinstance(pe_input[0], list) else pe_input
                test_code = f"{code_content}\n\n# Performance test\nresult = {func_name}(*{first_test})"
            else:
                test_code = f"{code_content}\n\n# Performance test\npass"

            # 只进行步骤2和3的预处理（不添加循环）
            step2_code = self.step2_pre_for_crofile(test_code)
            final_code = self.step3_add_profile(step2_code)

        else:
            # Generated code 和 original canonical: 完整的预处理流程
            base_code = f"{code_content}\n\ncheck()"

            # 步骤1: 添加for循环
            step1_code = self.step1_add_for_function(base_code)

            # 步骤2: 函数封装
            step2_code = self.step2_pre_for_crofile(step1_code)

            # 步骤3: 添加@profile装饰器
            final_code = self.step3_add_profile(step2_code)

        return final_code

    def step4_run_cprofile(self, script_content):
        """步骤4: 运行cprofile（模拟cprofile.py）"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name

            try:
                # 完全按照原始cprofile.py的逻辑
                with open(script_path, "rb") as file:
                    code = compile(file.read(), script_path, 'exec')

                profile_file = tempfile.mktemp()
                cProfile.run(code, profile_file)

                # 提取cumtime（按照原始方法）
                p = pstats.Stats(profile_file)
                cumtime = p.stats[list(p.stats.keys())[0]][3]

                os.unlink(profile_file)
                return cumtime
            finally:
                os.unlink(script_path)

        except Exception as e:
            print(f"cProfile测量失败: {e}")
            return 0.0

    def step5_run_memory(self, script_content):
        """步骤5: 运行memory（模拟memory.py）"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name

            try:
                # 完全按照原始memory.py的逻辑
                command = ["python3", script_path]
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                mem_usage = memory_usage(proc=process.pid, interval=0.1, timeout=None)
                max_mem = max(mem_usage)

                return max_mem
            finally:
                os.unlink(script_path)

        except Exception as e:
            print(f"内存测量失败: {e}")
            return 0.0

    def step6_run_cpu(self, script_content):
        """步骤6: 运行CPU（改进的cpu.py，修复时序问题）"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name

            try:
                # 改进版CPU监控（修复原始cpu.py的时序问题）
                command = ["python3", script_path]
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # 并行监控内存和CPU
                import threading

                mem_usage = []
                cpu_usages = []

                def memory_monitor():
                    nonlocal mem_usage
                    try:
                        mem_usage = memory_usage(proc=process.pid, interval=0.01, timeout=None)
                    except:
                        pass

                def cpu_monitor():
                    nonlocal cpu_usages
                    try:
                        ps_process = psutil.Process(process.pid)
                        ps_process.cpu_percent()  # 初始化

                        while process.poll() is None:
                            try:
                                cpu_usage = ps_process.cpu_percent(interval=0.1)  # 使用原始间隔
                                if cpu_usage > 0:
                                    cpu_usages.append(cpu_usage)
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                break
                    except:
                        pass

                # 启动监控线程
                mem_thread = threading.Thread(target=memory_monitor)
                cpu_thread = threading.Thread(target=cpu_monitor)

                mem_thread.start()
                cpu_thread.start()

                # 等待进程结束
                process.wait()

                # 等待监控线程结束
                mem_thread.join(timeout=1)
                cpu_thread.join(timeout=1)

                max_mem = max(mem_usage) if mem_usage else 0.0

                return max_mem, cpu_usages
            finally:
                os.unlink(script_path)

        except Exception as e:
            print(f"CPU测量失败: {e}")
            return 0.0, []

    def test_single_task(self, task_id, model, num_runs=5):
        """测试单个任务"""
        print(f"Testing {task_id} with preprocessed method...")

        results = {
            'task_id': task_id,
            'generated_code_results': None,
            'evalperf_canonical_results': None,
            'original_canonical_results': None
        }

        # 1. 测试Generated Code
        generated_path = self.config.get_generated_code_path(model,
                                                           'humaneval' if 'HumanEval' in task_id else 'mbpp',
                                                           task_id)
        if generated_path and os.path.exists(generated_path):
            with open(generated_path, 'r') as f:
                generated_code = f.read()

            results['generated_code_results'] = self._run_complete_pipeline(
                generated_code, num_runs, test_type="generated", test_name="Generated Code"
            )

        # 2. 测试EvalPerf Canonical
        if task_id in self.evalperf_tasks:
            evalperf_code = self.evalperf_tasks[task_id]['reference'][0]
            pe_input = self.evalperf_tasks[task_id]['pe_input'][0]  # 使用第一个pe_input

            results['evalperf_canonical_results'] = self._run_complete_pipeline(
                evalperf_code, num_runs, test_type="evalperf_canonical",
                pe_input=pe_input, test_name="EvalPerf Canonical"
            )

        # 3. 测试Original Canonical
        if task_id in self.original_humaneval_tasks:
            original_code = self.original_humaneval_tasks[task_id]['canonical_solution']
        elif task_id in self.original_mbpp_tasks:
            original_code = self.original_mbpp_tasks[task_id]['code']
        else:
            original_code = None

        if original_code:
            results['original_canonical_results'] = self._run_complete_pipeline(
                original_code, num_runs, test_type="original", test_name="Original Canonical"
            )

        return results

    def _run_complete_pipeline(self, code_content, num_runs, test_type="generated", pe_input=None, test_name=""):
        """运行完整的原始脚本流水线"""
        execution_times = []
        memory_usages = []
        cpu_usages_list = []

        for run in range(num_runs):
            print(f"  {test_name} - Run {run + 1}/{num_runs}")

            try:
                # 完整的预处理流水线
                preprocessed_code = self.create_preprocessed_script(code_content, test_type=test_type, pe_input=pe_input)

                # 步骤4: 运行cprofile（模拟cprofile.py）
                exec_time = self.step4_run_cprofile(preprocessed_code)
                execution_times.append(exec_time)

                # 步骤5: 运行memory（模拟memory.py）
                memory_usage = self.step5_run_memory(preprocessed_code)
                memory_usages.append(memory_usage)

                # 步骤6: 运行CPU（改进的cpu.py）
                cpu_memory_usage, cpu_usages = self.step6_run_cpu(preprocessed_code)
                cpu_usages_list.append(cpu_usages)

                print(f"    执行时间: {exec_time:.6f}s, 内存: {memory_usage:.3f}MB, CPU数据点: {len(cpu_usages)}")

            except Exception as e:
                print(f"    Run {run + 1} failed: {e}")
                execution_times.append(0.0)
                memory_usages.append(0.0)
                cpu_usages_list.append([])

        # 计算统计数据
        valid_times = [t for t in execution_times if t > 0]
        valid_memories = [m for m in memory_usages if m > 0]

        # 计算平均CPU使用率
        all_cpu_values = []
        for cpu_list in cpu_usages_list:
            if cpu_list:
                all_cpu_values.extend(cpu_list)
        avg_cpu = sum(all_cpu_values) / len(all_cpu_values) if all_cpu_values else 0.0

        return {
            'execution_times': execution_times,
            'memory_usages': memory_usages,
            'cpu_usages_list': cpu_usages_list,
            'avg_execution_time': sum(valid_times) / len(valid_times) if valid_times else 0.0,
            'avg_memory_usage': sum(valid_memories) / len(valid_memories) if valid_memories else 0.0,
            'avg_cpu_usage': avg_cpu,
            'success_count': len(valid_times)
        }

def main():
    parser = argparse.ArgumentParser(description='EvalPerf Preprocessed Performance Tester')
    parser.add_argument('--model', required=True, help='Model name (e.g., copilot)')
    parser.add_argument('--tasks', nargs='+', help='Specific tasks to test (e.g., HumanEval/0 Mbpp/3)')
    parser.add_argument('--num-runs', type=int, default=5, help='Number of runs per task')

    args = parser.parse_args()

    # 初始化测试器
    tester = EvalPerfPreprocessedTester(
        evalperf_dataset_path='/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl',
        original_humaneval_path='./json-prompt/human-eval-v2-20210705.jsonl',
        original_mbpp_path='./json-prompt/mbpp.jsonl'
    )

    print(f"=== EvalPerf预处理性能测试 ===")
    print(f"模型: {args.model}")
    print(f"任务: {args.tasks}")
    print(f"运行次数: {args.num_runs}")
    print(f"预处理策略:")
    print(f"  - EvalPerf canonical: 仅函数封装（不添加循环）")
    print(f"  - Generated/Original: 添加循环 + 函数封装")
    print(f"  - CPU监控: 改进的并行监控（10ms间隔）")
    print()

    all_results = {}

    for task_id in args.tasks:
        result = tester.test_single_task(task_id, args.model, args.num_runs)
        all_results[task_id] = result

        # 显示结果
        print(f"\n{task_id} 结果:")
        for test_type in ['generated_code_results', 'evalperf_canonical_results', 'original_canonical_results']:
            if result[test_type]:
                data = result[test_type]
                print(f"  {test_type}:")
                print(f"    执行时间: {data['avg_execution_time']:.6f}s")
                print(f"    内存使用: {data['avg_memory_usage']:.3f}MB")
                print(f"    CPU使用: {data['avg_cpu_usage']:.1f}%")

                # CPU监控成功率
                cpu_success = len([cpu for cpu in data['cpu_usages_list'] if cpu and any(x > 0 for x in cpu)])
                print(f"    CPU监控成功: {cpu_success}/{args.num_runs}")

    # 保存结果
    output_file = f'./preprocessed_results/{args.model}_preprocessed_results.json'
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    print(f"\n结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
