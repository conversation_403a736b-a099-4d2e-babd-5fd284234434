#!/usr/bin/env python3
"""
对比原始方法和集成方法的CPU测量差异
"""

import subprocess
import psutil
import threading
import time
import tempfile
import os

def create_test_script():
    """创建测试脚本"""
    script_content = '''
import time
total = 0
for i in range(100000):  # CPU密集型任务
    total += i * i
print(f"Result: {total}")
'''
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(script_content)
        return f.name

def original_method_cpu_monitoring(script_path):
    """原始方法：主进程中直接监控CPU"""
    print("=== 原始方法CPU监控 ===")
    
    # 启动进程
    process = subprocess.Popen(['python', script_path],
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    
    try:
        ps_process = psutil.Process(process.pid)
        cpu_usages = []
        
        # 主进程中直接监控（原始方法的逻辑）
        ps_process.cpu_percent()  # 初始化
        
        iteration = 0
        while process.poll() is None and iteration < 50:  # 限制次数防止无限循环
            iteration += 1
            cpu_usage = ps_process.cpu_percent(interval=0.1)  # 原始间隔
            if cpu_usage > 0:
                cpu_usages.append(cpu_usage)
                print(f"  主进程监控 - 第{iteration}次: {cpu_usage:.1f}%")
            
            # 检查进程状态
            if not ps_process.is_running():
                break
        
        process.wait()
        return cpu_usages
        
    except Exception as e:
        print(f"  原始方法监控失败: {e}")
        process.wait()
        return []

def integrated_method_cpu_monitoring(script_path):
    """集成方法：线程中监控CPU"""
    print("=== 集成方法CPU监控 ===")
    
    cpu_usages = []
    
    # 启动进程
    process = subprocess.Popen(['python', script_path],
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    
    try:
        ps_process = psutil.Process(process.pid)
        ps_process.cpu_percent()  # 初始化
        
        # 线程中监控CPU（集成方法的逻辑）
        def cpu_monitor():
            nonlocal cpu_usages
            iteration = 0
            try:
                while process.poll() is None and iteration < 500:  # 更多次数，更短间隔
                    iteration += 1
                    try:
                        cpu_usage = ps_process.cpu_percent(interval=0.001)  # 集成方法间隔
                        if cpu_usage > 0:
                            cpu_usages.append(cpu_usage)
                            if iteration % 50 == 0:  # 每50次打印一次
                                print(f"  线程监控 - 第{iteration}次: {cpu_usage:.1f}%")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        break
            except Exception as e:
                print(f"  线程监控异常: {e}")
        
        # 启动CPU监控线程
        cpu_thread = threading.Thread(target=cpu_monitor)
        cpu_thread.start()
        
        # 等待进程结束
        process.wait()
        cpu_thread.join(timeout=1)
        
        return cpu_usages
        
    except Exception as e:
        print(f"  集成方法监控失败: {e}")
        process.wait()
        return []

def analyze_cpu_differences():
    """分析CPU测量差异的原因"""
    print("\n=== CPU测量差异分析 ===")
    
    reasons = [
        "1. 监控间隔差异:",
        "   - 原始方法: 0.1秒间隔 → 较少的采样点",
        "   - 集成方法: 0.001秒间隔 → 更多的采样点",
        "",
        "2. psutil.cpu_percent()的计算方式:",
        "   - 在多核系统上，CPU使用率可以超过100%",
        "   - 短间隔测量更容易捕获CPU突发",
        "",
        "3. 系统调度影响:",
        "   - 主进程监控: 与目标进程竞争CPU资源",
        "   - 线程监控: 可能在不同CPU核心上运行",
        "",
        "4. 测量精度:",
        "   - 长间隔(100ms): 平均化效应，接近理论值",
        "   - 短间隔(1ms): 捕获瞬时峰值，可能超过100%"
    ]
    
    for reason in reasons:
        print(reason)

def test_cpu_measurement_methods():
    """测试不同CPU测量方法"""
    script_path = create_test_script()
    
    try:
        print("测试脚本创建完成，开始对比测试...\n")
        
        # 测试原始方法
        original_cpu_data = original_method_cpu_monitoring(script_path)
        
        print(f"\n原始方法结果:")
        if original_cpu_data:
            print(f"  数据点数: {len(original_cpu_data)}")
            print(f"  CPU范围: {min(original_cpu_data):.1f}% - {max(original_cpu_data):.1f}%")
            print(f"  平均CPU: {sum(original_cpu_data)/len(original_cpu_data):.1f}%")
        else:
            print("  无CPU数据")
        
        print("\n" + "="*50 + "\n")
        
        # 测试集成方法
        integrated_cpu_data = integrated_method_cpu_monitoring(script_path)
        
        print(f"\n集成方法结果:")
        if integrated_cpu_data:
            print(f"  数据点数: {len(integrated_cpu_data)}")
            print(f"  CPU范围: {min(integrated_cpu_data):.1f}% - {max(integrated_cpu_data):.1f}%")
            print(f"  平均CPU: {sum(integrated_cpu_data)/len(integrated_cpu_data):.1f}%")
            
            # 分析超过100%的数据点
            over_100 = [x for x in integrated_cpu_data if x > 100]
            if over_100:
                print(f"  超过100%的数据点: {len(over_100)}/{len(integrated_cpu_data)} ({len(over_100)/len(integrated_cpu_data)*100:.1f}%)")
                print(f"  最高CPU使用率: {max(over_100):.1f}%")
        else:
            print("  无CPU数据")
        
        # 分析差异原因
        analyze_cpu_differences()
        
    finally:
        # 清理测试文件
        os.unlink(script_path)

def explain_cpu_over_100():
    """解释CPU使用率超过100%的原因"""
    print("\n=== 为什么CPU使用率会超过100%？ ===")
    
    explanations = [
        "1. 多核系统特性:",
        f"   - 系统CPU核心数: {psutil.cpu_count(logical=True)}",
        f"   - 物理核心数: {psutil.cpu_count(logical=False)}",
        "   - psutil.cpu_percent()在多核系统上可以返回>100%的值",
        "",
        "2. psutil的计算方式:",
        "   - cpu_percent()计算的是进程在所有CPU核心上的总使用率",
        "   - 如果进程使用多个核心，总使用率可以超过100%",
        "",
        "3. 短间隔测量的影响:",
        "   - 1ms间隔捕获瞬时CPU突发",
        "   - 100ms间隔进行时间平均，更接近理论值",
        "",
        "4. 这是正常现象:",
        "   - 在多核系统上，CPU使用率>100%是完全正常的",
        "   - 表示进程有效利用了多个CPU核心"
    ]
    
    for explanation in explanations:
        print(explanation)

if __name__ == "__main__":
    print("CPU测量方法对比实验")
    print("="*50)
    
    # 显示系统信息
    print(f"系统信息:")
    print(f"  CPU核心数: {psutil.cpu_count(logical=True)} (逻辑)")
    print(f"  物理核心数: {psutil.cpu_count(logical=False)}")
    print()
    
    # 运行对比测试
    test_cpu_measurement_methods()
    
    # 解释CPU>100%的现象
    explain_cpu_over_100()
