#!/usr/bin/env python3
"""
详细调试CPU监控
"""

import subprocess
import psutil
import time
import tempfile
import threading
from memory_profiler import memory_usage


def debug_cpu_monitoring_detailed():
    """详细调试CPU监控"""
    
    # 创建HumanEval/9的实际代码
    test_code = """
from typing import List, Dict, Any, Optional, Tuple
import json

def rolling_max(numbers: List[int]) -> List[int]:
    result = []
    max_so_far = float('-inf')
    for num in numbers:
        if num > max_so_far:
            max_so_far = num
        result.append(max_so_far)
    return result

def main():
    # 解析输入数据
    pe_input_data = [[[1, 2, 3, 2, 3, 4, 2]]]
    
    # 根据pe_input_data的结构调用函数，运行所有测试用例
    if isinstance(pe_input_data, list):
        # 对于EvalPerf，通常是三层嵌套的列表
        # 最外层是测试用例列表，每个测试用例包含函数的参数
        for test_case in pe_input_data:
            if isinstance(test_case, list):
                # test_case是参数列表，使用*解包传递给函数
                rolling_max(*test_case)
            else:
                # 如果test_case不是列表，直接传递
                rolling_max(test_case)
    else:
        # 单个参数
        rolling_max(pe_input_data)

if __name__ == "__main__":
    main()
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        temp_file = f.name
    
    print("=== 详细CPU监控调试 ===")
    print(f"测试文件: {temp_file}")
    
    # 测试1: 基础运行时间
    print("\n1. 基础运行时间测试:")
    start = time.perf_counter()
    result = subprocess.run(['python', temp_file], capture_output=True, text=True)
    end = time.perf_counter()
    print(f"   运行时间: {end - start:.6f}s")
    print(f"   返回码: {result.returncode}")
    print(f"   输出: {result.stdout}")
    if result.stderr:
        print(f"   错误: {result.stderr}")
    
    # 测试2: 详细的CPU监控
    print("\n2. 详细CPU监控:")
    
    def monitor_cpu_detailed(process, interval=0.05):
        """详细的CPU监控"""
        try:
            ps_process = psutil.Process(process.pid)
            cpu_usages = []
            
            print(f"     进程PID: {process.pid}")
            print(f"     监控间隔: {interval}s")
            
            # 第一次调用cpu_percent()来初始化
            ps_process.cpu_percent()
            print(f"     CPU监控已初始化")
            
            monitor_count = 0
            while True:
                try:
                    monitor_count += 1
                    
                    # 检查进程是否还在运行
                    if not ps_process.is_running():
                        print(f"     进程已结束 (监控次数: {monitor_count})")
                        break
                    
                    status = ps_process.status()
                    cpu_usage = ps_process.cpu_percent(interval=interval)
                    cpu_usages.append(cpu_usage)
                    
                    print(f"     监控 {monitor_count}: CPU={cpu_usage}%, 状态={status}")
                    
                    # 检查进程状态
                    if status == psutil.STATUS_ZOMBIE:
                        print(f"     进程变为僵尸进程")
                        break
                        
                except psutil.NoSuchProcess:
                    print(f"     进程不存在 (监控次数: {monitor_count})")
                    break
                except Exception as e:
                    print(f"     监控异常: {e}")
                    break
            
            print(f"     最终CPU数据: {cpu_usages}")
            return cpu_usages
        except Exception as e:
            print(f"     CPU监控失败: {e}")
            return []
    
    # 启动进程并监控
    process = subprocess.Popen(['python', temp_file],
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    
    cpu_usages = monitor_cpu_detailed(process, interval=0.05)
    stdout, stderr = process.communicate()
    
    print(f"   进程输出: {stdout.decode()}")
    if stderr:
        print(f"   进程错误: {stderr.decode()}")
    print(f"   最终CPU使用率: {cpu_usages}")
    print(f"   CPU数据点数量: {len(cpu_usages)}")
    
    # 测试3: 并行监控
    print("\n3. 并行监控测试:")
    
    cpu_data = []
    memory_data = []
    
    def cpu_monitor_thread(process_pid):
        try:
            ps_process = psutil.Process(process_pid)
            ps_process.cpu_percent()  # 初始化
            
            while ps_process.is_running():
                try:
                    cpu_usage = ps_process.cpu_percent(interval=0.05)
                    cpu_data.append(cpu_usage)
                    print(f"     并行CPU: {cpu_usage}%")
                except psutil.NoSuchProcess:
                    break
        except Exception as e:
            print(f"     并行CPU监控异常: {e}")
    
    def memory_monitor():
        try:
            mem_usage = memory_usage(proc=process.pid, interval=0.05, timeout=None)
            memory_data.extend(mem_usage)
            print(f"     内存使用: {mem_usage}")
        except Exception as e:
            print(f"     内存监控异常: {e}")
    
    process = subprocess.Popen(['python', temp_file],
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    
    # 启动监控线程
    cpu_thread = threading.Thread(target=cpu_monitor_thread, args=(process.pid,))
    memory_thread = threading.Thread(target=memory_monitor)
    
    cpu_thread.start()
    memory_thread.start()
    
    # 等待进程结束
    stdout, stderr = process.communicate()
    
    # 等待监控线程结束
    cpu_thread.join(timeout=1)
    memory_thread.join(timeout=1)
    
    print(f"   并行CPU数据: {cpu_data}")
    print(f"   并行内存数据: {memory_data}")
    
    # 清理
    import os
    if os.path.exists(temp_file):
        os.unlink(temp_file)


if __name__ == "__main__":
    debug_cpu_monitoring_detailed()
