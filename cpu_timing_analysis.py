#!/usr/bin/env python3
"""
分析CPU监控时机和成功率的关系
"""

import subprocess
import psutil
import time
import threading
import tempfile
import statistics

def test_cpu_monitoring_timing():
    """测试CPU监控时机"""
    
    # 创建不同执行时间的测试脚本
    test_scripts = {
        'ultra_fast': '''
# 0.5ms左右
total = sum(range(100))
print(f"Result: {total}")
''',
        'very_fast': '''
# 2-3ms左右  
total = sum(i*i for i in range(1000))
print(f"Result: {total}")
''',
        'fast': '''
# 8-10ms左右
total = sum(i*i for i in range(5000))
print(f"Result: {total}")
'''
    }
    
    results = {}
    
    for script_name, script_content in test_scripts.items():
        print(f"\n=== 测试 {script_name} ===")
        
        # 创建临时脚本
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script_content)
            script_path = f.name
        
        # 多次测试CPU监控
        cpu_success_count = 0
        execution_times = []
        
        for i in range(10):  # 测试10次
            cpu_data, exec_time = test_single_cpu_monitoring(script_path)
            execution_times.append(exec_time)
            
            if cpu_data and any(x > 0 for x in cpu_data):
                cpu_success_count += 1
                print(f"  Run {i+1}: ✅ CPU监控成功 ({exec_time:.6f}s, {len(cpu_data)}个数据点)")
            else:
                print(f"  Run {i+1}: ❌ CPU监控失败 ({exec_time:.6f}s)")
        
        avg_time = statistics.mean(execution_times)
        success_rate = cpu_success_count / 10 * 100
        
        results[script_name] = {
            'avg_time': avg_time,
            'success_rate': success_rate,
            'success_count': cpu_success_count
        }
        
        print(f"  平均执行时间: {avg_time:.6f}s")
        print(f"  CPU监控成功率: {success_rate:.1f}% ({cpu_success_count}/10)")
        
        # 清理
        import os
        os.unlink(script_path)
    
    return results

def test_single_cpu_monitoring(script_path):
    """单次CPU监控测试"""
    cpu_usages = []
    
    # 启动进程
    start_time = time.perf_counter()
    process = subprocess.Popen(['python', script_path],
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    
    try:
        # 立即获取进程对象并初始化
        ps_process = psutil.Process(process.pid)
        ps_process.cpu_percent()  # 初始化
        
        # CPU监控线程
        def cpu_monitor():
            nonlocal cpu_usages
            try:
                while process.poll() is None:
                    try:
                        cpu_usage = ps_process.cpu_percent(interval=0.001)
                        if cpu_usage > 0:
                            cpu_usages.append(cpu_usage)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        break
            except:
                pass
        
        # 启动CPU监控线程
        cpu_thread = threading.Thread(target=cpu_monitor)
        cpu_thread.start()
        
        # 等待进程结束
        process.wait()
        end_time = time.perf_counter()
        
        # 等待CPU线程结束
        cpu_thread.join(timeout=0.1)
        
        execution_time = end_time - start_time
        return cpu_usages, execution_time
        
    except Exception:
        process.wait()
        end_time = time.perf_counter()
        return [], end_time - start_time

def analyze_timing_factors():
    """分析时机因素"""
    print("=== CPU监控时机因素分析 ===")
    
    factors = [
        "1. 线程启动延迟: 1-5ms",
        "2. 进程启动延迟: 0.5-2ms", 
        "3. CPU监控间隔: 1ms",
        "4. 系统调度随机性: 0-10ms",
        "5. psutil初始化时间: 0.1-1ms"
    ]
    
    for factor in factors:
        print(f"  {factor}")
    
    print("\n关键洞察:")
    print("- 执行时间2.85ms的进程可能在线程启动的'幸运窗口'内")
    print("- 执行时间8.24ms的进程可能错过了监控时机")
    print("- CPU监控成功率与执行时间不是严格的线性关系")
    print("- 系统调度的随机性是主要影响因素")

if __name__ == "__main__":
    # 运行时机测试
    results = test_cpu_monitoring_timing()
    
    print("\n=== 总结分析 ===")
    for script_name, result in results.items():
        print(f"{script_name}: {result['avg_time']:.6f}s, {result['success_rate']:.1f}%成功率")
    
    # 分析时机因素
    analyze_timing_factors()
