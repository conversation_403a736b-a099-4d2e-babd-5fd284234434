# EvalPerf Performance Testing Report with Statistical Analysis

## Model: codellama

- Total tasks: 0
- Tested tasks: 0
- Successful tasks: 0
- Success rate: 0.0%

## Statistical Significance Analysis

### Significant Performance Changes (p < 0.05)

**Execution Time**:
- No successful tasks to analyze

**Memory Usage**:
- No successful tasks to analyze

**Cpu Usage**:
- No successful tasks to analyze

### Effect Size Analysis (<PERSON>'s d)

## Statistical Methods

- **Confidence Intervals**: 95% confidence intervals calculated using t-distribution
- **Statistical Tests**: Paired t-test (when sample sizes match), Independent t-test, <PERSON><PERSON> signed-rank test, Mann-Whitney U test
- **Effect Size**: <PERSON>'s d with interpretation (negligible: |d| < 0.2, small: 0.2 ≤ |d| < 0.5, medium: 0.5 ≤ |d| < 0.8, large: |d| ≥ 0.8)
- **Significance Level**: α = 0.05

