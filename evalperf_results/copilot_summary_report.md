# EvalPerf Performance Testing Report with Statistical Analysis

## Model: copilot

- Total tasks: 1
- Tested tasks: 1
- Successful tasks: 1
- Success rate: 100.0%

## Performance Ratios vs EvalPerf Canonical Solutions

- **Execution Time Ratio**: 0.758 ± 0.000
- **Memory Usage Ratio**: 1.117 ± 0.000
- **CPU Usage Ratio**: 1.000 ± 0.000

*Ratio < 1.0 means the generated code is better than canonical solution*

## Statistical Significance Analysis

### Significant Performance Changes (p < 0.05)

**Execution Time**:
- Significant improvements: 0/1 (0.0%)
- Significant degradations: 0/1 (0.0%)
- No significant change: 1/1 (100.0%)

**Memory Usage**:
- Significant improvements: 0/1 (0.0%)
- Significant degradations: 0/1 (0.0%)
- No significant change: 1/1 (100.0%)

**Cpu Usage**:
- Significant improvements: 0/1 (0.0%)
- Significant degradations: 0/1 (0.0%)
- No significant change: 1/1 (100.0%)

### Effect Size Analysis (<PERSON>'s d)

**Execution Time**:
- Mean effect size: -0.770
- Effect size interpretation: medium
- Direction: Generated code better

**Memory Usage**:
- Mean effect size: 1.412
- Effect size interpretation: large
- Direction: Canonical solution better

**Cpu Usage**:
- Mean effect size: 0.000
- Effect size interpretation: negligible
- Direction: Canonical solution better

## Statistical Methods

- **Confidence Intervals**: 95% confidence intervals calculated using t-distribution
- **Statistical Tests**: Paired t-test (when sample sizes match), Independent t-test, Wilcoxon signed-rank test, Mann-Whitney U test
- **Effect Size**: Cohen's d with interpretation (negligible: |d| < 0.2, small: 0.2 ≤ |d| < 0.5, medium: 0.5 ≤ |d| < 0.8, large: |d| ≥ 0.8)
- **Significance Level**: α = 0.05

