# EvalPerf Performance Testing Report with Statistical Analysis

## Model: copilot

- Total tasks: 6
- Tested tasks: 6
- Successful tasks: 6
- Success rate: 100.0%

## Performance Ratios vs EvalPerf Canonical Solutions

- **Execution Time Ratio**: 2864.192 ± 6729.619
- **Memory Usage Ratio**: 1.128 ± 0.370
- **CPU Usage Ratio**: 2.655 ± 2.164

*Ratio < 1.0 means the generated code is better than canonical solution*

## Statistical Significance Analysis

### Significant Performance Changes (p < 0.05)

**Execution Time**:
- Significant improvements: 0/6 (0.0%)
- Significant degradations: 4/6 (66.7%)
- No significant change: 2/6 (33.3%)

**Memory Usage**:
- Significant improvements: 0/6 (0.0%)
- Significant degradations: 0/6 (0.0%)
- No significant change: 6/6 (100.0%)

**Cpu Usage**:
- Significant improvements: 4/6 (66.7%)
- Significant degradations: 0/6 (0.0%)
- No significant change: 2/6 (33.3%)

### Effect Size Analysis (<PERSON>'s d)

**Execution Time**:
- Mean effect size: 59.070
- Effect size interpretation: large
- Direction: Canonical solution better

**Memory Usage**:
- Mean effect size: 0.260
- Effect size interpretation: small
- Direction: Canonical solution better

**Cpu Usage**:
- Mean effect size: 1.828
- Effect size interpretation: large
- Direction: Canonical solution better

## Statistical Methods

- **Confidence Intervals**: 95% confidence intervals calculated using t-distribution
- **Statistical Tests**: Paired t-test (when sample sizes match), Independent t-test, Wilcoxon signed-rank test, Mann-Whitney U test
- **Effect Size**: Cohen's d with interpretation (negligible: |d| < 0.2, small: 0.2 ≤ |d| < 0.5, medium: 0.5 ≤ |d| < 0.8, large: |d| ≥ 0.8)
- **Significance Level**: α = 0.05

