# EvalPerf Performance Testing Report with Statistical Analysis

## Model: copilot

- Total tasks: 2
- Tested tasks: 2
- Successful tasks: 2
- Success rate: 100.0%

## Performance Ratios vs EvalPerf Canonical Solutions

- **Execution Time Ratio**: 5576.705 ± 7883.654
- **Memory Usage Ratio**: 1.264 ± 0.363
- **CPU Usage Ratio**: N/A (all values infinite)

*Ratio < 1.0 means the generated code is better than canonical solution*

## Statistical Significance Analysis

### Significant Performance Changes (p < 0.05)

**Execution Time**:
- Significant improvements: 0/2 (0.0%)
- Significant degradations: 2/2 (100.0%)
- No significant change: 0/2 (0.0%)

**Memory Usage**:
- Significant improvements: 0/2 (0.0%)
- Significant degradations: 0/2 (0.0%)
- No significant change: 2/2 (100.0%)

**Cpu Usage**:
- Significant improvements: 0/2 (0.0%)
- Significant degradations: 0/2 (0.0%)
- No significant change: 2/2 (100.0%)

### Effect Size Analysis (<PERSON>'s d)

**Execution Time**:
- Mean effect size: 95.306
- Effect size interpretation: large
- Direction: Canonical solution better

**Memory Usage**:
- Mean effect size: 0.438
- Effect size interpretation: small
- Direction: Canonical solution better

## Statistical Methods

- **Confidence Intervals**: 95% confidence intervals calculated using t-distribution
- **Statistical Tests**: Paired t-test (when sample sizes match), Independent t-test, Wilcoxon signed-rank test, Mann-Whitney U test
- **Effect Size**: Cohen's d with interpretation (negligible: |d| < 0.2, small: 0.2 ≤ |d| < 0.5, medium: 0.5 ≤ |d| < 0.8, large: |d| ≥ 0.8)
- **Significance Level**: α = 0.05

