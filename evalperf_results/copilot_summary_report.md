# EvalPerf Performance Testing Report with Statistical Analysis

## Model: copilot

- Total tasks: 20
- Tested tasks: 15
- Successful tasks: 14
- Success rate: 93.3%

## Performance Ratios vs EvalPerf Canonical Solutions

- **Execution Time Ratio**: 1.009 ± 0.073
- **Memory Usage Ratio**: 1.127 ± 0.223
- **CPU Usage Ratio**: 4.359 ± 7.057

*Ratio < 1.0 means the generated code is better than canonical solution*

## Statistical Significance Analysis

### Significant Performance Changes (p < 0.05)

**Execution Time**:
- Significant improvements: 1/14 (7.1%)
- Significant degradations: 1/14 (7.1%)
- No significant change: 12/14 (85.7%)

**Memory Usage**:
- Significant improvements: 0/14 (0.0%)
- Significant degradations: 0/14 (0.0%)
- No significant change: 14/14 (100.0%)

**Cpu Usage**:
- Significant improvements: 6/14 (42.9%)
- Significant degradations: 1/14 (7.1%)
- No significant change: 7/14 (50.0%)

### Effect Size Analysis (<PERSON>'s d)

**Execution Time**:
- Mean effect size: -0.013
- Effect size interpretation: negligible
- Direction: Generated code better

**Memory Usage**:
- Mean effect size: 0.189
- Effect size interpretation: negligible
- Direction: Canonical solution better

**Cpu Usage**:
- Mean effect size: nan
- Effect size interpretation: large
- Direction: Canonical solution better

## Statistical Methods

- **Confidence Intervals**: 95% confidence intervals calculated using t-distribution
- **Statistical Tests**: Paired t-test (when sample sizes match), Independent t-test, Wilcoxon signed-rank test, Mann-Whitney U test
- **Effect Size**: Cohen's d with interpretation (negligible: |d| < 0.2, small: 0.2 ≤ |d| < 0.5, medium: 0.5 ≤ |d| < 0.8, large: |d| ≥ 0.8)
- **Significance Level**: α = 0.05

