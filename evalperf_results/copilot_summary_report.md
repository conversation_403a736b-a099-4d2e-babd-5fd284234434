# EvalPerf Performance Testing Report with Statistical Analysis

## Model: copilot

- Total tasks: 5
- Tested tasks: 5
- Successful tasks: 5
- Success rate: 100.0%

## Performance Ratios vs EvalPerf Canonical Solutions

- **Execution Time Ratio**: 2425.636 ± 5121.842
- **Memory Usage Ratio**: 0.990 ± 0.075
- **CPU Usage Ratio**: 1.151 ± 0.370

*Ratio < 1.0 means the generated code is better than canonical solution*

## Statistical Significance Analysis

### Significant Performance Changes (p < 0.05)

**Execution Time**:
- Significant improvements: 0/5 (0.0%)
- Significant degradations: 3/5 (60.0%)
- No significant change: 2/5 (40.0%)

**Memory Usage**:
- Significant improvements: 1/5 (20.0%)
- Significant degradations: 0/5 (0.0%)
- No significant change: 4/5 (80.0%)

**Cpu Usage**:
- Significant improvements: 0/5 (0.0%)
- Significant degradations: 0/5 (0.0%)
- No significant change: 5/5 (100.0%)

### Effect Size Analysis (<PERSON>'s d)

**Execution Time**:
- Mean effect size: 196.771
- Effect size interpretation: large
- Direction: Canonical solution better

**Memory Usage**:
- Mean effect size: -0.095
- Effect size interpretation: negligible
- Direction: Generated code better

**Cpu Usage**:
- Mean effect size: 0.344
- Effect size interpretation: small
- Direction: Canonical solution better

## Statistical Methods

- **Confidence Intervals**: 95% confidence intervals calculated using t-distribution
- **Statistical Tests**: Paired t-test (when sample sizes match), Independent t-test, Wilcoxon signed-rank test, Mann-Whitney U test
- **Effect Size**: Cohen's d with interpretation (negligible: |d| < 0.2, small: 0.2 ≤ |d| < 0.5, medium: 0.5 ≤ |d| < 0.8, large: |d| ≥ 0.8)
- **Significance Level**: α = 0.05

