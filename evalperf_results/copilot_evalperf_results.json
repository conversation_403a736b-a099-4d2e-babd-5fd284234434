{"model": "copilot", "total_tasks": 5, "tested_tasks": 5, "successful_tasks": 5, "task_results": {"HumanEval/0": {"task_id": "HumanEval/0", "entry_point": "has_close_elements", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.018615004, 0.015987487, 0.017243714, 0.017195654, 0.017208424], "memory_usages": [3.12109375, 3.50390625, 3.2265625, 3.5859375, 3.57421875], "cpu_usages_list": [[938.3, 938.1, 850.8, 938.4, 945.2, 938.9, 938.9, 939.1, 945.3, 939.0, 939.3, 945.3, 939.4, 938.7, 938.5, 939.3, 939.3, 945.3, 939.4, 953.2, 945.4, 939.1, 945.6, 937.2, 939.3, 945.4, 937.8, 939.2, 945.5, 945.6, 939.4, 945.2, 938.9, 939.4, 939.3, 939.2, 938.4, 941.1, 945.3, 940.3, 945.3, 938.9, 939.3, 939.4, 945.5, 942.8, 945.4, 866.6, 933.7, 938.8, 939.4, 939.7, 945.4, 939.9, 939.3, 944.8, 932.0, 938.3, 935.6, 939.7, 878.0, 889.3, 942.3, 940.1, 931.8, 939.7, 939.4, 939.4, 945.5, 939.6, 945.7, 945.9, 939.7, 945.8, 943.9, 945.5, 945.5, 939.5, 907.1, 940.6, 945.7, 939.2, 945.5, 939.5, 934.0, 945.2, 945.2, 945.4, 945.2, 945.1, 945.0, 945.0, 945.7, 938.5, 945.4, 937.1, 945.1, 938.8, 945.7, 939.1, 939.4, 938.8, 939.3, 939.6, 945.7, 945.2, 939.4, 945.4, 961.3, 945.6, 963.6, 945.2, 945.6, 939.2, 939.1, 945.5, 939.7, 945.2, 866.0, 938.1, 944.8, 939.7, 939.4, 945.4, 945.7, 939.4, 939.5, 939.7, 939.6, 945.2, 945.1, 939.7], [945.6, 945.5, 945.8, 915.8, 945.6, 904.0, 945.7, 939.4, 901.3, 856.0, 852.8, 945.4, 939.9, 945.8, 939.5, 939.9, 904.4, 939.7, 939.3, 945.8, 945.2, 939.7, 940.0, 945.6, 939.5, 945.7, 945.4, 939.4, 945.6, 945.5, 945.9, 944.7, 939.9, 939.8, 945.9, 939.7, 945.7, 945.6, 939.5, 933.9, 939.0, 945.8, 945.8, 939.5, 945.5, 934.5, 945.9, 940.0, 946.0, 942.2, 945.9, 939.4, 923.9, 945.6, 945.6, 939.8, 939.7, 957.2, 940.0, 945.9, 939.8, 939.9, 945.7, 970.4, 945.5, 945.8, 937.3, 945.6, 938.7, 939.2, 956.6, 939.2, 945.9, 945.7, 945.7, 945.8, 945.8, 946.0, 938.6, 946.0, 945.9, 939.1, 937.8, 870.7, 944.8, 938.5, 945.8, 945.7, 945.7, 945.2, 939.5, 945.8, 945.3, 945.4, 939.8, 939.2, 944.5, 944.6, 909.5, 898.2, 943.1, 939.0, 939.0, 937.8, 934.0, 944.9, 945.2, 939.7, 945.6, 945.5, 945.6, 945.6, 977.9, 945.3, 945.4, 939.1, 941.7, 942.1, 939.3, 974.3, 945.6, 938.2, 945.6, 945.9, 939.7, 939.7, 945.6, 939.6, 946.0, 943.0, 945.5, 939.9, 943.7], [937.6, 933.2, 946.1, 945.9, 939.6, 939.5, 937.8, 945.6, 945.6, 945.9, 939.7, 939.7, 944.7, 954.8, 945.6, 938.8, 931.9, 939.8, 939.0, 945.9, 949.1, 938.5, 945.5, 945.8, 945.7, 945.6, 945.5, 939.6, 931.3, 982.5, 945.8, 945.8, 945.9, 939.2, 935.7, 936.2, 939.3, 915.4, 939.7, 939.6, 946.0, 945.6, 945.5, 939.5, 945.7, 939.5, 939.5, 945.3, 939.8, 939.5, 945.8, 939.6, 956.4, 937.8, 845.5, 906.2, 939.1, 945.9, 939.7, 945.7, 943.0, 945.7, 939.9, 939.4, 932.1, 939.8, 957.5, 945.7, 939.5, 939.5, 939.2, 945.3, 945.3, 939.5, 945.2, 939.3, 934.5, 939.3, 938.9, 945.5, 945.5, 938.0, 950.8, 938.8, 939.4, 945.2, 945.5, 944.9, 945.4, 937.4, 945.1, 939.2, 945.6, 928.0, 935.8, 939.1, 939.8, 939.4, 939.3, 916.0, 945.6, 939.3, 939.8, 933.9, 874.3, 925.7, 928.1, 927.8, 932.7, 927.0, 927.3, 926.8, 936.7, 898.2, 856.6, 938.8, 944.9, 935.6, 939.5, 945.4, 943.9, 939.7, 944.7, 940.2, 939.5, 945.7, 945.8, 945.4, 939.1, 945.8, 939.3, 958.3, 945.6, 939.8], [903.2, 938.9, 945.8, 945.6, 937.8, 945.2, 945.7, 945.5, 945.4, 945.8, 939.3, 945.9, 945.5, 945.6, 939.9, 945.8, 939.7, 918.4, 945.8, 939.1, 945.5, 945.8, 945.7, 945.3, 975.6, 945.6, 945.8, 943.9, 945.6, 945.5, 929.0, 945.4, 939.1, 945.3, 945.6, 945.4, 945.6, 945.8, 945.6, 939.1, 945.6, 945.6, 945.7, 945.7, 945.5, 945.3, 939.1, 939.2, 945.7, 863.8, 940.5, 927.5, 945.3, 945.5, 945.6, 939.6, 939.4, 939.7, 945.9, 939.0, 945.8, 945.5, 945.6, 945.6, 938.5, 945.6, 939.0, 945.7, 945.6, 945.6, 945.8, 855.1, 945.5, 945.2, 940.5, 938.8, 938.3, 939.0, 945.7, 939.0, 939.3, 945.9, 945.6, 945.5, 946.0, 945.8, 939.6, 939.6, 945.6, 939.4, 945.6, 945.5, 945.0, 945.7, 945.9, 946.0, 945.6, 854.1, 866.7, 940.6, 909.0, 898.9, 932.7, 939.6, 939.5, 939.3, 945.6, 941.5, 892.2, 937.9, 937.6, 896.7, 942.4, 938.6, 941.6, 939.0, 867.9, 865.9, 939.4, 945.4, 939.2, 945.1, 945.5, 939.6, 945.8, 939.5, 948.9, 944.5, 945.1, 945.1, 942.9, 939.2, 939.4, 945.0], [939.6, 945.7, 945.7, 938.8, 939.0, 945.6, 939.4, 945.6, 945.4, 945.7, 945.4, 945.4, 945.3, 944.8, 945.7, 939.4, 945.5, 939.5, 960.9, 945.5, 945.6, 939.4, 945.7, 945.6, 944.3, 939.4, 946.0, 939.6, 936.1, 945.8, 944.1, 945.6, 844.3, 926.3, 875.4, 873.0, 866.0, 939.7, 936.6, 920.6, 945.6, 945.6, 945.3, 945.7, 939.9, 939.5, 945.6, 945.8, 945.8, 938.9, 938.8, 944.5, 934.8, 986.9, 925.5, 945.4, 939.6, 939.6, 939.4, 945.9, 945.3, 945.4, 939.9, 933.9, 945.9, 939.8, 940.1, 939.7, 942.9, 939.5, 956.0, 946.0, 939.6, 940.0, 941.7, 939.9, 930.0, 945.4, 945.9, 945.3, 939.5, 939.2, 945.6, 896.9, 945.7, 944.7, 945.4, 938.2, 918.6, 919.5, 942.3, 939.8, 946.1, 939.8, 939.8, 945.7, 939.5, 939.6, 940.1, 945.8, 946.0, 945.8, 945.6, 945.5, 939.6, 962.0, 940.0, 945.7, 945.6, 945.7, 938.8, 945.8, 939.6, 945.7, 939.8, 945.9, 945.6, 945.8, 939.8, 945.7, 938.9, 962.9, 945.6, 939.9, 938.8, 939.5, 939.9, 939.9, 945.7, 939.4, 940.0, 854.4]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0172500566, "std_execution_time": 0.0009303086241209417, "avg_memory_usage": 3.40234375, "std_memory_usage": 0.21422139973744161, "avg_cpu_usage": 938.9274073752903, "std_cpu_usage": 0.7929056948599931}, "evalperf_canonical_results": {"execution_times": [0.0072708550000000005, 0.013368079000000001, 0.015612540000000001, 0.01602235, 0.015982297], "memory_usages": [3.85546875, 3.64453125, 1.58203125, 3.55859375, 3.2265625], "cpu_usages_list": [[901.2, 850.1, 944.9, 945.0, 926.4, 944.9, 938.8, 944.9, 868.7, 944.5, 944.8, 945.0, 938.6, 938.9, 945.1, 939.2, 944.9, 945.2, 944.9, 938.6, 938.4, 945.0, 945.3, 938.6, 945.0, 945.0, 938.7, 939.3, 945.2, 938.5, 895.9, 935.3, 936.5, 936.7, 924.3, 935.5, 922.4, 924.1, 935.5, 896.5, 935.6, 925.0, 935.3, 935.5, 936.6, 936.5, 936.7, 936.7, 936.7, 936.5, 936.7, 936.6, 926.8, 884.3, 928.6, 867.6, 935.9, 937.1, 937.2, 908.4, 937.4, 936.9, 937.3, 937.4, 937.2, 937.5, 937.0, 937.0, 929.7, 935.1, 929.3, 929.6, 936.4, 936.8, 935.8, 936.6, 937.1, 937.2, 925.9, 936.2, 936.2, 936.0, 937.0, 928.1, 902.5, 927.2, 936.2, 927.6, 937.2, 937.2, 928.0, 928.1, 933.5, 928.3, 938.1, 853.0, 843.4, 932.5, 945.6, 939.2, 939.9, 939.8, 939.0, 944.9, 957.4, 945.7, 939.5, 945.6, 945.9, 939.4, 919.1, 929.7, 927.2, 936.9, 924.6, 936.2, 936.4, 925.6, 936.0, 937.3, 937.4, 936.3, 925.8, 894.2, 867.4, 936.2, 937.2, 935.7, 927.3, 936.9, 910.6, 923.5, 833.2, 917.7, 832.7, 828.8, 828.7, 866.9, 937.3], [929.9, 941.1, 920.5, 899.7, 945.2, 939.8, 908.3, 940.1, 939.9, 939.8, 939.8, 939.9, 936.2, 870.0, 939.4, 941.3, 945.7, 945.8, 945.9, 959.7, 945.0, 944.9, 939.4, 937.7, 937.2, 872.2, 938.4, 974.0, 945.5, 939.6, 945.5, 945.6, 940.0, 945.1, 939.3, 945.6, 945.7, 939.4, 945.6, 945.4, 946.0, 939.5, 969.5, 945.8, 939.6, 945.8, 945.9, 939.4, 918.9, 872.9, 939.3, 945.4, 939.4, 939.7, 945.7, 939.6, 945.7, 939.4, 939.5, 939.5, 936.3, 945.4, 945.6, 837.5, 919.9, 945.4, 939.4, 938.9, 945.6, 878.9, 862.7, 939.3, 917.4, 939.0, 945.1, 945.5, 945.5, 945.8, 914.3, 945.7, 945.6, 944.5, 945.7, 945.5, 939.7, 939.9, 926.1, 944.2, 935.9, 945.4, 937.8, 903.2, 845.7, 944.7, 945.2, 939.1, 974.9, 945.5, 939.7, 945.8, 939.7, 939.7, 945.7, 945.8, 945.8, 945.6, 945.8, 945.6, 901.2, 865.8, 855.4, 939.2, 937.2, 939.6, 945.5, 939.1, 945.2, 938.7, 942.5, 945.3, 918.5, 944.8, 945.7, 938.9, 944.7, 939.1, 939.7, 939.3, 877.2], [934.0, 935.0, 936.3, 935.6, 936.1, 937.3, 934.9, 933.0, 936.8, 935.3, 940.3, 940.7, 940.8, 941.1, 941.4, 941.6, 940.9, 941.3, 941.6, 941.2, 939.0, 941.3, 941.3, 941.0, 941.5, 941.2, 940.6, 941.4, 940.9, 941.6, 941.4, 941.2, 981.2, 941.2, 940.9, 940.9, 941.6, 941.3, 939.3, 940.8, 941.0, 941.4, 947.2, 940.5, 941.1, 940.7, 940.1, 939.9, 939.2, 940.4, 940.8, 967.1, 941.2, 941.1, 941.5, 941.4, 941.1, 941.0, 941.1, 940.7, 975.0, 940.9, 941.1, 940.8, 941.1, 940.7, 940.9, 939.9, 939.3, 939.5, 953.2, 939.8, 953.8, 945.7, 945.6, 945.7, 939.3, 937.2, 936.0, 935.7, 935.3, 935.7, 936.4, 936.1, 936.1, 936.1, 931.4, 937.1, 937.3, 936.2, 936.5, 857.7, 838.8, 819.3, 937.5, 905.1, 945.6, 938.9, 945.7, 943.2, 945.8, 945.7, 935.5, 939.4, 945.6, 939.9, 939.2, 944.9, 938.8, 944.1, 945.6, 939.3, 945.2, 939.5, 939.8, 945.8, 945.4, 945.4, 945.7, 939.4, 945.0, 945.6, 932.9, 945.3, 945.5, 945.7, 939.3, 939.2, 944.8, 945.8, 945.4, 940.0], [897.4, 848.6, 945.2, 867.6, 938.1, 939.4, 945.6, 939.5, 945.4, 939.7, 945.5, 945.4, 945.2, 945.2, 939.3, 945.3, 945.5, 945.4, 939.2, 945.4, 939.3, 945.5, 945.3, 939.5, 945.7, 939.3, 939.0, 945.3, 945.5, 939.5, 945.3, 939.4, 945.5, 945.4, 975.7, 944.9, 945.1, 911.8, 945.2, 953.6, 982.6, 939.1, 944.9, 945.7, 945.5, 937.3, 933.2, 945.4, 945.8, 945.6, 944.8, 939.1, 945.6, 944.8, 939.1, 945.5, 939.1, 979.7, 939.2, 938.0, 938.9, 939.1, 939.1, 944.9, 939.1, 945.3, 938.6, 945.3, 945.2, 945.5, 938.4, 945.2, 939.4, 945.2, 920.6, 945.2, 945.1, 945.1, 945.5, 945.3, 937.5, 939.1, 953.4, 945.3, 945.4, 939.1, 944.8, 945.1, 939.4, 938.1, 939.4, 939.4, 944.8, 945.3, 939.3, 939.4, 939.3, 939.6, 939.1, 939.4, 945.5, 939.3, 942.7, 939.6, 945.1, 931.0, 945.4, 938.9, 939.3, 939.3, 939.1, 967.4, 945.4, 945.4, 939.0, 945.2, 945.4, 945.1, 944.6, 945.5, 935.5, 896.4, 943.3, 945.2, 938.9, 945.8], [853.3, 945.6, 945.6, 945.3, 945.6, 939.4, 939.3, 908.7, 945.6, 939.5, 945.5, 945.5, 938.0, 945.5, 939.3, 939.3, 945.6, 945.8, 939.2, 939.9, 945.7, 945.7, 939.8, 942.9, 945.7, 945.8, 945.9, 944.3, 945.9, 938.5, 945.1, 945.8, 939.5, 946.0, 944.5, 945.5, 945.8, 945.6, 945.6, 945.6, 939.4, 938.8, 939.5, 939.5, 933.8, 945.5, 928.4, 942.6, 945.7, 945.4, 945.5, 945.7, 945.7, 940.4, 889.0, 853.2, 898.7, 899.6, 938.9, 945.6, 939.8, 939.9, 939.0, 935.9, 939.5, 945.3, 945.0, 927.9, 939.3, 945.5, 939.6, 945.2, 945.1, 939.1, 939.2, 945.5, 939.2, 944.9, 945.3, 896.2, 857.4, 938.0, 965.1, 934.2, 933.6, 939.6, 965.9, 945.7, 939.2, 939.3, 945.4, 869.8, 865.6, 937.8, 945.3, 944.8, 945.2, 945.3, 938.9, 945.1, 945.5, 945.6, 939.7, 944.8, 944.4, 918.5, 938.4, 944.9, 943.4, 935.8, 945.3, 945.3, 945.1, 945.2, 938.4, 935.5, 945.2, 937.7, 945.4, 945.2, 945.2, 938.9, 944.7, 869.2, 945.1, 944.3, 945.3, 945.1, 945.4]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.013651224200000001, "std_execution_time": 0.00373135967035633, "avg_memory_usage": 3.1734375, "std_memory_usage": 0.91799202098163, "avg_cpu_usage": 935.7990152366177, "std_cpu_usage": 5.363332863916637}, "original_canonical_results": {"execution_times": [0.008665286004543304, 0.008752783061936498, 0.008410268928855658, 0.008310450008139014, 0.008211242966353893], "memory_usages": [3.5703125, 3.59375, 3.296875, 3.5, 3.65625], "cpu_usages_list": [[], [], [], [], []], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.008470006193965674, "std_execution_time": 0.00023134502735150053, "avg_memory_usage": 3.5234375, "std_memory_usage": 0.1384378527083543, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.2636270818847148, "memory_usage_ratio": 1.0721319547021173, "cpu_usage_ratio": 1.0033430171305338}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0172500566, "std": 0.0009303086241209417, "n": 5, "confidence_interval_95": [0.016094925874210547, 0.018405187325789454]}, "canonical": {"mean": 0.013651224200000001, "std": 0.0037313596703563304, "n": 5, "confidence_interval_95": [0.00901812923296907, 0.01828431916703093]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.8421211060831701, "p_value": 0.13925548435054982, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 2.092590648784289, "p_value": 0.06973656144127809, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 24.0, "p_value": 0.015873015873015872, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.3234705321055624, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.2636270818847148, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.40234375, "std": 0.21422139973744161, "n": 5, "confidence_interval_95": [3.136352750301201, 3.668334749698799]}, "canonical": {"mean": 3.1734375, "std": 0.91799202098163, "n": 5, "confidence_interval_95": [2.0335998569087934, 4.313275143091206]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.5792601289546001, "p_value": 0.5934702901506177, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.5429870345723227, "p_value": 0.6019347986713395, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 6.0, "p_value": 0.8125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 10.5, "p_value": 0.7532980334628383, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.3434151538378263, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0721319547021173, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 938.9274073752904, "std": 0.7929056948599846, "n": 5, "confidence_interval_95": [937.942884920012, 939.9119298305687]}, "canonical": {"mean": 935.7990152366177, "std": 5.36333286391669, "n": 5, "confidence_interval_95": [929.1395579091093, 942.4584725641262]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.2158009577129487, "p_value": 0.2909049770199711, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.2902579869676283, "p_value": 0.23300290532505463, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 4.0, "p_value": 0.4375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 18.0, "p_value": 0.30952380952380953, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.816030801608311, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0033430171305338, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 2.0366049569467304, "memory_usage_ratio": 0.9656319290465631, "cpu_usage_ratio": null}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0172500566, "std": 0.0009303086241209417, "n": 5, "confidence_interval_95": [0.016094925874210547, 0.018405187325789454]}, "canonical": {"mean": 0.008470006193965674, "std": 0.00023134502735150053, "n": 5, "confidence_interval_95": [0.00818275340233985, 0.008757258985591498]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 20.097195460367914, "p_value": 3.6180521698026836e-05, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 20.47979089076921, "p_value": 3.3811840466273506e-08, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 12.952557043759867, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.0366049569467304, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.40234375, "std": 0.21422139973744161, "n": 5, "confidence_interval_95": [3.136352750301201, 3.668334749698799]}, "canonical": {"mean": 3.5234375, "std": 0.1384378527083543, "n": 5, "confidence_interval_95": [3.3515442023034008, 3.6953307976965992]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.3728376711139787, "p_value": 0.2417388577605777, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.0616064838217212, "p_value": 0.3194087060178902, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 0.3125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 8.0, "p_value": 0.42063492063492064, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.6714188935358666, "interpretation": "medium", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9656319290465631, "better_performance": false}}}}}, "HumanEval/9": {"task_id": "HumanEval/9", "entry_point": "rolling_max", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.006156173, 0.006046856000000001, 0.006082765, 0.0057415420000000005, 0.005784588], "memory_usages": [3.140625, 3.55078125, 3.21875, 3.64453125, 3.12890625], "cpu_usages_list": [[938.0, 945.4], [897.7, 939.5, 945.6], [937.8, 842.0], [938.2, 824.1, 939.8], [900.6, 899.2, 923.3]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0059623848000000005, "std_execution_time": 0.00018679125183128885, "avg_memory_usage": 3.33671875, "std_memory_usage": 0.24296435385894316, "avg_cpu_usage": 913.52, "std_cpu_usage": 20.90746278246121}, "evalperf_canonical_results": {"execution_times": [0.022634724000000002, 0.0030203950000000004, 0.002965525, 0.002982405, 0.003005633], "memory_usages": [3.6015625, 3.5859375, 3.18359375, 3.65625, 3.59765625], "cpu_usages_list": [[854.3, 939.5], [863.1, 944.9], [945.2, 939.6], [937.9, 938.6, 939.7], [1881.5, 945.7]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0069217364000000005, "std_execution_time": 0.008783852378044545, "avg_memory_usage": 3.525, "std_memory_usage": 0.19276858925640933, "avg_cpu_usage": 1019.1266666666667, "std_cpu_usage": 221.44569738174835}, "original_canonical_results": {"execution_times": [0.008247083984315395, 0.008315971121191978, 0.008223487064242363, 0.007835768861696124, 0.007794421166181564], "memory_usages": [3.21875, 3.1953125, 3.23828125, 3.5546875, 3.578125], "cpu_usages_list": [[], [], [], [], []], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.008083346439525485, "std_execution_time": 0.00024765706703575746, "avg_memory_usage": 3.35703125, "std_memory_usage": 0.1919157758993838, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.8614001538689049, "memory_usage_ratio": 0.9465868794326242, "cpu_usage_ratio": 0.8963753278951259}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0059623848000000005, "std": 0.00018679125183128888, "n": 5, "confidence_interval_95": [0.005730452827421676, 0.006194316772578325]}, "canonical": {"mean": 0.0069217364000000005, "std": 0.008783852378044545, "n": 5, "confidence_interval_95": [-0.003984856863354453, 0.017828329663354455]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.24722890473064602, "p_value": 0.8169021669692489, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.24416285998454731, "p_value": 0.813250045625649, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 20.0, "p_value": 0.15079365079365079, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.1544221515143908, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.8614001538689049, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.33671875, "std": 0.24296435385894316, "n": 5, "confidence_interval_95": [3.03503865896651, 3.6383988410334904]}, "canonical": {"mean": 3.525, "std": 0.19276858925640933, "n": 5, "confidence_interval_95": [3.2856461827357872, 3.7643538172642126]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.6591713982146918, "p_value": 0.1724197901147603, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.3574503010412384, "p_value": 0.21168907958335006, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 2.5, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 6.0, "p_value": 0.2222222222222222, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.85852695235431, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9465868794326242, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 913.5200000000001, "std": 20.907462782461185, "n": 5, "confidence_interval_95": [887.5599561692329, 939.4800438307673]}, "canonical": {"mean": 1019.1266666666667, "std": 221.44569738174837, "n": 5, "confidence_interval_95": [744.1655166706237, 1294.0878166627097]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.0382130103526914, "p_value": 0.35780696160094244, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.06165164259831, "p_value": 0.3193894058137889, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 4.0, "p_value": 0.4375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 8.0, "p_value": 0.42063492063492064, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.6714474544539402, "interpretation": "medium", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.896375327895126, "better_performance": false}}}, "vs_original_canonical": {"execution_time_ratio": 0.7376134184779551, "memory_usage_ratio": 0.9939492669304166, "cpu_usage_ratio": null}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0059623848000000005, "std": 0.00018679125183128888, "n": 5, "confidence_interval_95": [0.005730452827421676, 0.006194316772578325]}, "canonical": {"mean": 0.008083346439525485, "std": 0.00024765706703575746, "n": 5, "confidence_interval_95": [0.007775839575486449, 0.008390853303564523]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -49.77956485817984, "p_value": 9.744944525359058e-07, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -15.288816501311604, "p_value": 3.3235199903953064e-07, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 0.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -9.669496574502274, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.7376134184779551, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.33671875, "std": 0.24296435385894316, "n": 5, "confidence_interval_95": [3.03503865896651, 3.6383988410334904]}, "canonical": {"mean": 3.35703125, "std": 0.1919157758993838, "n": 5, "confidence_interval_95": [3.118736340378393, 3.5953261596216066]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.15564119371064533, "p_value": 0.883854489154191, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.14669729207923998, "p_value": 0.8870003726108209, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 7.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.5, "p_value": 0.6004018480969686, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.09277951390987527, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9939492669304166, "better_performance": false}}}}}, "Mbpp/3": {"task_id": "Mbpp/3", "entry_point": "is_not_prime", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [5.978027626, 5.897460819, 5.95787682, 6.320216241000001, 5.9077905710000005], "memory_usages": [3.50390625, 3.578125, 3.50390625, 3.6015625, 3.58984375], "cpu_usages_list": [[901.3, 945.7, 945.9, 939.5, 945.8, 929.6, 939.6, 945.9, 939.9, 946.0, 945.8, 939.7, 947.3, 907.3, 931.0, 936.8, 936.2, 936.1, 937.0, 926.2, 937.3, 936.8, 936.0, 956.7, 927.8, 896.3, 864.8, 864.9, 939.8, 940.1, 946.0, 939.9, 949.6, 927.4, 845.1, 937.0, 925.2, 928.2, 937.2, 942.0, 937.3, 936.1, 937.2, 937.2, 937.2, 936.3, 927.2, 928.5, 929.8, 928.4, 937.3, 927.7, 936.7, 937.4, 928.1, 927.2, 928.3, 913.3, 869.6, 867.9, 819.7, 945.7, 939.7, 945.8, 939.8, 945.9, 945.7, 940.0, 945.4, 937.6, 943.7, 945.4, 945.6, 939.2, 945.1, 945.0, 939.4, 939.6, 945.6, 939.5, 945.6, 945.6, 939.6, 945.5, 939.5, 939.4, 945.4, 945.8, 865.1, 878.2, 924.9, 935.9, 937.4, 937.4, 927.9, 925.2, 936.3, 925.8, 936.2, 937.7, 868.4, 815.6, 939.5, 938.6, 936.4, 939.4, 937.6, 945.8, 945.8, 945.6, 945.8, 945.8, 945.6, 945.8, 939.6, 976.5, 939.7, 939.6, 945.9, 945.8, 945.9, 945.9, 945.8, 946.0, 945.8, 945.7, 939.5, 938.9, 939.3, 945.7, 945.7, 939.7, 887.8, 945.9, 945.8, 939.6, 932.3, 939.5, 939.9, 939.9, 945.8, 939.5, 945.7, 945.8, 945.8, 945.7, 939.2, 945.7, 945.8, 945.9, 945.5, 939.2, 939.3, 945.7, 945.8, 945.9, 945.9, 945.9, 945.7, 945.6, 945.8, 943.3, 939.3, 864.0, 929.1, 826.0, 939.2, 935.1, 935.6, 936.7, 827.7, 943.7, 939.5, 934.1, 924.5, 933.4, 950.9, 947.5, 936.7, 927.4, 937.3, 931.5, 928.1, 936.0, 925.1, 935.7, 935.7, 936.1, 924.8, 936.3, 937.4, 937.4, 945.8, 945.9, 931.9, 946.0, 945.6, 945.6, 939.0, 939.1, 912.5, 945.6, 938.6, 943.1, 939.8, 935.2, 939.6, 928.1, 945.9, 939.6, 945.9, 945.8, 928.6, 945.5, 945.7, 945.9, 939.6, 945.9, 939.8, 945.8, 945.7, 944.9, 945.8, 945.9, 945.7, 945.6, 945.8, 939.6, 940.0, 931.7, 945.9, 940.1, 945.8, 939.6, 972.9, 945.8, 939.5, 926.7, 945.7, 939.0, 939.9, 938.0, 939.9, 940.1, 939.8, 936.0, 851.6, 841.3, 939.8, 933.0, 939.5, 940.0, 945.6, 945.8, 939.8, 941.4, 945.8, 941.2, 945.5, 938.3, 939.5, 939.6, 939.1, 939.2, 935.6, 939.6, 939.3, 945.6, 944.3, 945.5, 945.4, 945.6, 944.3, 945.6, 939.1, 939.5, 945.2, 945.5, 945.5, 932.9, 981.2, 897.9, 939.3, 943.0, 939.6, 818.0, 938.2, 939.4, 945.4, 939.7, 936.0, 945.9, 940.1, 945.9, 937.3, 945.4, 945.5, 871.3, 938.0, 939.4, 939.9, 939.6, 945.7, 945.4, 939.6, 939.6, 945.7, 945.8, 940.0, 945.9, 945.8, 945.8, 940.2, 940.1, 945.7, 939.6, 945.8, 857.9, 920.0, 939.6, 945.8, 939.9, 945.9, 946.0, 946.0, 939.8, 945.3, 939.5, 939.5, 939.4, 939.2, 939.4, 945.4, 945.5, 945.7, 939.4, 945.7, 939.3, 945.5, 945.7, 939.4, 939.3, 939.2, 939.1, 945.4, 939.5, 945.4, 945.3, 939.6, 939.3, 939.2, 945.2, 945.6, 945.6, 918.3, 925.5, 945.5, 954.5, 939.4, 944.2, 939.5, 945.6, 945.5, 939.1, 945.3, 945.4, 939.5, 938.9, 899.9, 943.1, 943.3, 945.6, 939.2, 939.8, 939.7, 945.4, 939.4, 931.0, 925.6, 945.1, 945.0, 945.4, 945.4, 945.4, 945.4, 945.7, 945.3, 939.2, 939.1, 945.1, 839.0, 842.6, 920.4, 924.1, 935.5, 926.9, 821.4, 905.7, 893.6, 939.8, 945.8, 945.7, 945.7, 940.0, 895.4, 866.6, 939.3, 945.7, 940.0, 939.7, 945.8, 939.3, 945.8, 939.8, 939.9, 946.1, 939.3, 939.9, 939.8, 945.9, 945.8, 939.8, 939.9, 945.0, 939.7, 945.7, 945.8, 940.0, 942.9, 945.8, 939.5, 945.8, 945.8, 946.0, 945.8, 945.4, 939.9, 936.1, 945.6, 939.9, 939.8, 940.0, 945.6, 944.3, 945.9, 939.9, 945.8, 945.6, 939.9, 933.9, 945.6, 945.6, 939.7, 939.7, 940.0, 939.9, 945.8, 941.5, 945.7, 945.8, 954.3, 945.3, 939.2, 939.5, 896.1, 897.8, 933.3, 945.8, 868.3, 820.4, 938.3, 945.9, 939.9, 939.9, 939.6, 945.8, 938.9, 945.6, 940.0, 954.0, 939.9], [840.9, 939.6, 939.5, 946.0, 945.9, 945.8, 946.0, 945.7, 945.9, 945.9, 939.3, 940.2, 945.5, 945.8, 945.8, 945.7, 945.8, 931.5, 945.7, 945.8, 938.7, 945.7, 945.5, 945.7, 945.4, 945.7, 945.6, 939.7, 945.5, 945.5, 938.7, 939.1, 938.7, 945.6, 939.5, 945.5, 939.4, 945.7, 945.5, 945.6, 938.6, 939.6, 939.8, 945.7, 939.5, 945.3, 943.6, 945.7, 944.6, 945.6, 938.8, 939.3, 939.6, 939.6, 945.8, 939.4, 939.5, 943.2, 939.3, 939.6, 945.2, 945.6, 939.6, 945.5, 945.8, 945.8, 937.5, 939.5, 945.6, 945.5, 945.6, 927.0, 977.5, 945.3, 939.3, 939.6, 943.1, 939.4, 943.6, 945.5, 939.3, 939.4, 939.1, 945.3, 945.5, 939.5, 939.5, 945.6, 945.3, 939.5, 945.6, 939.7, 939.8, 939.2, 936.7, 939.5, 939.4, 945.5, 945.6, 945.5, 945.5, 942.2, 945.7, 945.7, 945.4, 946.0, 943.6, 939.4, 945.1, 945.6, 939.5, 917.5, 939.1, 939.9, 940.0, 939.6, 810.3, 938.3, 920.1, 938.0, 939.3, 930.3, 932.5, 945.5, 945.5, 945.7, 926.6, 939.7, 945.1, 945.3, 945.6, 939.6, 945.6, 945.7, 945.6, 944.8, 929.7, 945.6, 945.1, 935.1, 939.7, 912.9, 903.3, 938.3, 939.6, 933.0, 939.8, 939.6, 939.4, 851.1, 919.7, 919.9, 945.7, 939.7, 940.1, 923.9, 940.0, 939.7, 944.6, 945.5, 939.4, 945.5, 939.4, 945.7, 941.4, 939.9, 939.7, 945.3, 934.0, 939.2, 945.6, 945.5, 904.4, 900.9, 854.5, 939.7, 877.7, 938.7, 945.7, 939.4, 945.9, 939.9, 945.8, 945.8, 939.8, 939.9, 946.0, 939.5, 945.9, 945.7, 945.7, 940.2, 945.9, 939.8, 907.5, 938.3, 946.0, 939.8, 945.9, 945.9, 946.0, 946.0, 946.2, 946.0, 945.9, 939.9, 945.8, 945.8, 945.9, 945.0, 939.9, 940.2, 940.0, 945.9, 945.9, 939.1, 866.3, 861.1, 939.6, 945.9, 939.8, 945.8, 946.0, 945.7, 945.7, 939.6, 946.2, 939.6, 945.7, 939.9, 937.2, 939.9, 939.8, 945.9, 945.8, 946.0, 946.0, 939.9, 945.7, 950.9, 940.0, 940.0, 946.1, 946.1, 945.9, 939.5, 939.3, 945.9, 939.9, 945.5, 939.8, 940.1, 945.7, 939.6, 944.1, 844.8, 939.0, 896.5, 898.8, 939.7, 945.9, 945.9, 946.0, 945.7, 946.0, 939.9, 939.6, 940.0, 939.8, 945.4, 939.8, 948.1, 945.6, 945.7, 945.9, 965.8, 939.1, 945.8, 939.9, 939.9, 945.9, 945.9, 946.0, 939.9, 945.8, 945.8, 945.9, 939.9, 945.8, 939.3, 945.9, 940.1, 939.4, 897.1, 906.4, 939.9, 939.3, 945.1, 939.1, 939.0, 934.9, 944.8, 939.2, 945.6, 939.2, 863.7, 939.6, 930.7, 937.3, 936.9, 945.3, 896.9, 945.4, 939.1, 949.5, 939.3, 973.6, 939.2, 939.3, 939.3, 939.3, 939.1, 939.4, 939.4, 939.1, 945.7, 945.5, 945.6, 945.5, 943.7, 943.7, 939.3, 927.1, 860.9, 930.0, 936.8, 927.9, 937.2, 927.0, 922.2, 937.0, 954.5, 937.0, 928.0, 936.1, 936.2, 925.1, 936.1, 957.4, 925.4, 936.1, 936.0, 936.2, 937.0, 937.2, 937.1, 927.8, 928.1, 927.5, 927.6, 937.1, 937.0, 935.9, 937.2, 937.1, 937.1, 936.7, 937.4, 928.5, 936.3, 925.2, 936.1, 936.2, 935.7, 925.9, 953.0, 924.9, 936.0, 936.0, 939.2, 942.2, 939.7, 945.7, 945.6, 939.9, 940.0, 919.5, 938.1, 938.5, 945.8, 939.5, 945.8, 939.9, 939.6, 945.4, 938.4, 939.7, 939.9, 973.5, 939.7, 926.4, 939.6, 939.8, 939.5, 945.9, 945.8, 946.0, 945.7, 946.0, 939.7, 940.1, 938.1, 938.4, 945.8, 938.4, 939.6, 939.6, 932.5, 945.6, 965.1, 945.6, 939.2, 921.1, 939.6, 965.4, 945.2, 939.6, 945.6, 939.2, 939.2, 863.8, 938.1, 940.2, 939.8, 946.0, 939.5, 946.0, 938.0, 946.1, 945.7, 945.9, 939.6, 866.8, 895.3, 938.6, 940.0, 944.8, 940.0, 939.7, 945.6, 939.8, 939.7, 939.8, 939.1, 945.5, 847.9, 873.5, 850.2, 873.5, 936.0, 964.4, 941.3, 945.4, 945.6, 945.4, 939.3, 939.6, 937.9, 945.4, 945.6, 945.5, 939.4, 952.8, 939.5, 938.0, 945.9, 945.6, 945.5, 945.7, 939.8, 939.6, 939.1, 945.7, 968.6, 945.5, 945.7, 920.7, 939.1, 888.3], [932.1, 945.7, 939.7, 945.8, 935.2, 939.6, 935.8, 945.9, 929.7, 931.1, 936.7, 854.5, 856.1, 919.1, 939.6, 940.0, 940.0, 940.0, 940.0, 937.9, 894.4, 945.5, 942.3, 939.9, 939.9, 946.0, 945.8, 946.0, 945.7, 939.6, 938.3, 898.6, 898.2, 938.8, 944.7, 938.4, 939.3, 939.8, 938.5, 935.5, 938.8, 945.5, 930.9, 930.0, 937.1, 937.2, 931.3, 933.8, 937.2, 936.3, 936.2, 919.6, 936.5, 936.2, 939.1, 933.0, 939.5, 938.8, 862.8, 929.5, 945.9, 939.0, 971.6, 939.5, 939.8, 939.5, 939.7, 945.9, 942.3, 946.0, 939.6, 945.7, 939.7, 928.4, 939.9, 939.8, 945.9, 940.0, 946.1, 940.1, 945.8, 946.1, 939.8, 939.9, 929.8, 939.9, 946.1, 939.5, 940.1, 946.0, 939.7, 940.0, 946.0, 945.4, 945.9, 945.6, 939.2, 939.3, 939.6, 939.7, 945.9, 939.7, 939.4, 939.7, 945.7, 945.8, 945.9, 898.5, 829.0, 895.5, 918.5, 936.2, 936.2, 936.3, 936.2, 936.4, 936.1, 936.2, 936.2, 947.7, 936.1, 936.4, 936.3, 936.2, 936.3, 949.1, 939.9, 939.6, 945.9, 939.8, 945.9, 945.8, 940.1, 945.7, 928.9, 913.6, 918.6, 936.0, 936.3, 936.2, 945.3, 852.6, 938.1, 939.2, 945.7, 939.8, 945.9, 945.8, 939.5, 939.9, 940.2, 945.9, 945.7, 940.2, 939.6, 939.5, 973.5, 940.0, 953.9, 945.9, 945.7, 944.9, 945.8, 936.4, 939.2, 939.3, 939.3, 939.5, 939.5, 939.8, 939.6, 939.2, 945.4, 939.2, 945.0, 850.9, 924.0, 874.3, 845.2, 945.6, 959.7, 945.5, 945.6, 945.5, 945.4, 938.8, 939.3, 939.2, 939.3, 939.5, 939.3, 939.5, 939.5, 945.5, 939.4, 939.1, 945.5, 939.5, 945.4, 939.6, 945.8, 945.6, 945.6, 945.4, 939.5, 939.0, 945.7, 945.5, 939.5, 939.3, 939.4, 945.6, 939.5, 945.7, 939.4, 945.7, 945.9, 945.6, 939.7, 945.6, 945.8, 952.3, 939.4, 945.5, 938.9, 945.2, 939.4, 945.5, 939.2, 939.0, 945.7, 939.7, 939.4, 945.5, 937.9, 851.8, 938.9, 945.4, 922.3, 936.7, 936.3, 936.7, 927.2, 937.3, 927.5, 935.9, 936.2, 926.1, 936.3, 936.0, 938.5, 945.7, 938.8, 945.6, 938.6, 939.3, 939.1, 939.6, 913.6, 927.1, 934.1, 902.8, 937.3, 937.3, 933.3, 936.2, 936.0, 936.1, 936.2, 936.2, 936.2, 936.1, 936.2, 936.2, 925.4, 936.3, 925.2, 945.3, 945.8, 938.9, 939.8, 939.9, 925.7, 939.8, 935.7, 937.2, 936.1, 936.9, 936.8, 927.4, 937.4, 927.8, 937.3, 937.2, 927.5, 927.9, 921.6, 927.8, 935.9, 936.1, 936.3, 936.1, 936.2, 935.9, 935.7, 935.9, 936.0, 936.2, 936.4, 925.8, 936.1, 924.9, 935.8, 935.6, 932.6, 934.7, 945.8, 945.7, 939.8, 951.9, 939.4, 939.4, 945.5, 945.7, 945.5, 939.5, 939.2, 860.2, 894.2, 838.5, 939.3, 939.8, 939.7, 945.7, 940.0, 939.7, 945.7, 939.7, 940.0, 939.7, 945.9, 945.4, 939.5, 919.1, 965.0, 928.1, 935.9, 939.3, 945.8, 945.7, 939.7, 939.6, 945.9, 939.9, 939.3, 945.6, 945.9, 939.1, 945.7, 945.8, 946.0, 945.9, 939.5, 945.9, 945.9, 945.8, 927.2, 944.2, 945.7, 940.2, 944.1, 941.3, 853.6, 937.8, 938.0, 945.2, 938.5, 937.1, 945.3, 945.9, 943.7, 945.6, 939.6, 939.8, 939.6, 945.5, 945.8, 945.2, 939.3, 943.7, 939.4, 924.6, 860.2, 945.4, 869.4, 941.3, 934.5, 935.6, 936.0, 937.0, 935.9, 868.6, 805.2, 937.4, 945.9, 945.7, 945.5, 939.7, 938.8, 945.4, 939.7, 953.8, 945.7, 938.4, 945.9, 938.6, 945.9, 939.9, 945.5, 939.8, 939.2, 945.9, 945.9, 939.7, 945.8, 940.0, 939.9, 945.7, 939.5, 919.8, 939.5, 939.8, 939.7, 939.7, 945.2, 937.4, 910.0, 939.1, 948.5, 941.5, 945.7, 939.8, 945.7, 945.0, 939.6, 935.5, 939.8, 939.6, 939.4, 939.2, 949.5, 929.1, 940.9, 945.6, 939.1, 945.4, 945.6, 945.6, 875.1, 930.3, 933.4, 925.6, 936.1, 964.4, 936.3, 936.0, 935.5, 934.7, 936.2, 935.8, 935.5, 935.7, 935.6, 936.2, 936.1, 948.9, 936.1, 936.3, 936.1, 852.5, 938.1, 945.7, 945.6, 946.0, 936.9], [938.7, 945.7, 939.1, 901.2, 945.2, 939.6, 945.9, 945.5, 939.9, 937.7, 905.8, 851.8, 945.8, 945.9, 860.1, 945.8, 939.4, 939.8, 945.9, 939.6, 945.9, 945.7, 922.1, 945.3, 977.6, 931.6, 945.7, 945.6, 945.6, 945.8, 931.9, 945.7, 945.9, 939.4, 945.8, 936.6, 945.8, 945.6, 945.8, 945.7, 941.3, 945.7, 939.4, 939.4, 945.8, 945.8, 945.7, 945.8, 899.4, 891.9, 939.3, 939.1, 929.2, 945.8, 851.4, 932.7, 938.1, 938.1, 945.4, 945.7, 939.7, 945.6, 939.6, 939.9, 945.7, 939.3, 946.0, 945.9, 945.9, 945.8, 939.4, 945.6, 939.5, 946.1, 940.3, 937.1, 945.2, 945.5, 944.8, 928.2, 945.6, 945.7, 928.5, 944.1, 945.5, 945.1, 945.9, 939.8, 849.1, 937.8, 925.6, 939.8, 954.2, 945.7, 939.8, 940.2, 906.7, 945.8, 944.3, 939.5, 925.6, 939.9, 921.4, 939.9, 939.8, 945.8, 939.7, 945.8, 939.1, 945.6, 938.2, 939.4, 939.9, 945.9, 939.8, 946.0, 945.7, 939.5, 945.3, 945.7, 945.9, 936.2, 945.8, 940.0, 897.1, 937.3, 939.9, 938.7, 945.6, 939.4, 945.8, 945.8, 940.0, 940.2, 939.5, 939.8, 945.6, 945.9, 937.9, 939.9, 945.7, 927.4, 945.7, 939.9, 945.8, 945.8, 939.6, 946.0, 939.7, 880.8, 851.1, 846.0, 939.9, 938.7, 944.9, 939.0, 939.5, 939.5, 939.3, 939.5, 945.6, 945.6, 945.6, 926.9, 933.1, 930.3, 939.1, 944.2, 937.8, 936.0, 936.4, 826.7, 938.2, 938.3, 851.5, 926.8, 945.8, 939.6, 945.8, 876.1, 937.1, 945.1, 937.0, 945.7, 945.1, 939.9, 945.5, 945.6, 924.9, 940.0, 945.8, 946.0, 939.7, 946.0, 942.4, 945.9, 939.7, 945.5, 939.7, 939.7, 939.2, 939.6, 939.4, 943.0, 939.1, 944.0, 939.3, 945.6, 945.1, 945.5, 945.5, 939.5, 939.9, 930.0, 936.0, 924.9, 925.5, 936.9, 936.5, 937.3, 927.4, 923.2, 936.3, 924.7, 935.2, 936.3, 932.7, 862.6, 902.2, 934.4, 945.7, 939.7, 946.0, 938.9, 945.9, 945.7, 939.5, 945.9, 945.8, 945.8, 939.8, 939.8, 945.5, 944.7, 923.7, 939.8, 967.2, 940.1, 945.9, 939.6, 933.7, 945.9, 934.3, 945.8, 946.0, 915.0, 945.6, 928.9, 940.1, 945.9, 939.5, 945.9, 946.0, 940.2, 945.8, 940.1, 940.0, 945.8, 945.7, 940.1, 945.8, 939.7, 947.9, 945.6, 940.1, 945.7, 945.1, 946.0, 940.1, 939.8, 945.6, 945.9, 944.7, 945.7, 939.6, 946.0, 939.9, 945.8, 939.7, 945.8, 946.0, 924.1, 945.6, 945.8, 940.0, 961.5, 945.7, 939.6, 951.4, 939.9, 940.1, 939.6, 895.9, 838.1, 939.3, 945.1, 945.9, 945.8, 945.1, 939.7, 939.5, 944.5, 940.2, 970.3, 939.9, 939.4, 937.1, 939.8, 939.9, 945.8, 945.9, 945.9, 945.8, 940.1, 939.8, 945.8, 944.1, 940.0, 939.5, 945.6, 939.4, 940.1, 945.8, 929.6, 929.7, 940.1, 937.5, 939.4, 945.7, 939.8, 939.8, 940.0, 938.5, 939.9, 945.8, 945.9, 939.5, 939.8, 939.7, 939.9, 940.1, 940.0, 939.9, 940.0, 940.2, 939.7, 945.5, 945.8, 939.7, 939.8, 945.6, 940.0, 865.1, 936.5, 937.1, 935.4, 936.9, 937.2, 936.9, 936.2, 936.4, 936.2, 936.4, 936.3, 936.2, 942.6, 937.0, 937.6, 937.4, 937.4, 936.3, 930.1, 937.3, 937.3, 936.8, 937.4, 937.4, 937.4, 937.4, 937.5, 937.4, 936.9, 937.4, 937.5, 937.3, 924.0, 936.4, 938.1, 936.3, 936.3, 936.4, 936.1, 936.3, 937.6, 936.3, 936.3, 936.4, 936.5, 925.5, 936.2, 925.9, 936.1, 936.4, 936.3, 935.7, 936.1, 937.4, 937.1, 936.1, 936.2, 936.5, 951.2, 937.2, 927.7, 937.3, 936.2, 935.6, 936.2, 937.1, 937.5, 932.8, 852.9, 939.9, 939.8, 939.9, 934.9, 945.8, 945.7, 940.0, 945.9, 939.3, 945.7, 939.6, 939.9, 940.1, 939.9, 933.1, 939.7, 945.9, 928.4, 946.1, 945.8, 945.9, 945.9, 939.9, 939.5, 945.9, 945.7, 939.9, 945.6, 946.1, 939.9, 945.6, 939.8, 939.9, 945.7, 939.8, 946.0, 945.5, 945.7, 940.0, 939.8, 938.1, 945.9, 939.6, 945.2, 948.0, 945.2, 945.9, 945.1, 945.8, 945.8, 945.7], [938.4, 904.5, 939.9, 945.9, 945.8, 945.9, 933.5, 891.6, 941.4, 945.7, 939.6, 945.8, 939.1, 939.5, 939.3, 934.1, 939.5, 939.3, 945.6, 945.5, 945.8, 939.5, 945.8, 945.5, 939.8, 939.4, 939.8, 938.6, 945.5, 933.8, 939.8, 940.3, 938.9, 938.8, 939.0, 930.7, 945.7, 939.9, 945.9, 935.3, 945.8, 945.8, 945.7, 926.1, 934.5, 936.3, 924.7, 936.4, 936.4, 936.4, 936.4, 936.1, 937.3, 937.0, 935.0, 934.9, 900.2, 895.1, 938.1, 945.7, 945.7, 945.8, 945.9, 939.2, 945.3, 945.1, 939.0, 979.8, 945.5, 939.2, 945.8, 940.2, 945.7, 946.1, 945.6, 945.8, 918.7, 945.4, 945.0, 939.3, 945.7, 945.6, 943.9, 945.8, 943.1, 946.0, 939.5, 939.1, 945.8, 945.8, 945.8, 945.7, 945.8, 945.8, 942.9, 907.9, 939.3, 939.4, 945.7, 946.0, 946.1, 945.7, 946.0, 945.9, 860.7, 937.8, 901.6, 945.8, 945.4, 945.6, 932.3, 939.8, 971.4, 945.6, 939.5, 939.8, 939.7, 939.4, 939.0, 896.4, 870.4, 945.7, 929.9, 939.7, 945.8, 939.6, 939.3, 945.5, 939.2, 945.7, 939.5, 945.7, 939.4, 945.1, 945.8, 945.9, 946.0, 945.9, 939.5, 939.8, 945.6, 932.6, 945.3, 940.0, 936.8, 945.3, 939.7, 938.2, 945.6, 939.7, 934.7, 945.8, 939.8, 945.5, 929.3, 939.9, 939.9, 939.9, 945.6, 939.7, 945.7, 946.0, 939.9, 937.5, 939.8, 945.6, 939.7, 939.8, 945.7, 939.5, 939.8, 939.9, 939.0, 945.7, 939.9, 946.1, 919.5, 862.6, 871.2, 944.2, 946.2, 945.6, 945.8, 871.3, 899.5, 939.2, 939.6, 945.8, 945.8, 946.0, 939.6, 940.0, 939.9, 940.0, 939.1, 945.6, 939.9, 930.8, 939.7, 945.7, 939.7, 938.8, 939.9, 939.9, 939.8, 939.3, 939.3, 939.5, 945.4, 945.5, 945.6, 945.5, 945.8, 939.2, 939.3, 945.7, 945.0, 945.7, 945.5, 939.4, 957.6, 945.6, 939.7, 939.7, 939.9, 939.5, 943.0, 868.3, 899.0, 867.7, 873.6, 939.4, 939.5, 945.7, 945.6, 939.7, 939.8, 867.1, 939.7, 939.8, 945.8, 945.8, 945.8, 945.0, 945.8, 927.3, 939.9, 945.8, 945.9, 939.7, 953.9, 939.9, 944.1, 939.9, 929.5, 939.7, 940.1, 938.1, 940.1, 940.0, 939.6, 940.0, 901.7, 862.4, 930.6, 939.4, 939.6, 939.1, 939.5, 938.2, 939.4, 938.7, 892.1, 933.1, 903.0, 938.8, 938.4, 939.1, 943.3, 945.6, 939.1, 943.3, 941.5, 939.9, 945.1, 939.1, 945.5, 945.5, 945.6, 939.4, 939.5, 945.6, 939.2, 945.1, 938.1, 945.2, 939.1, 945.2, 939.7, 945.2, 939.2, 939.2, 938.9, 939.4, 945.0, 939.1, 945.7, 939.6, 945.6, 939.4, 945.5, 945.5, 945.5, 945.6, 944.9, 945.4, 898.6, 870.0, 938.9, 946.3, 945.6, 939.9, 945.8, 939.4, 939.9, 945.8, 946.0, 945.8, 937.8, 945.7, 943.4, 939.6, 953.4, 945.4, 939.7, 940.1, 940.1, 946.0, 940.1, 945.7, 945.9, 945.5, 945.5, 937.5, 945.6, 945.5, 945.4, 945.3, 936.1, 945.7, 938.6, 945.5, 939.6, 945.7, 939.5, 945.0, 910.3, 945.4, 939.4, 924.3, 939.0, 945.6, 939.5, 945.3, 962.0, 945.2, 939.5, 940.9, 939.3, 939.1, 945.3, 933.8, 945.6, 945.3, 945.3, 945.2, 945.4, 939.4, 945.2, 945.4, 945.5, 838.6, 861.3, 939.3, 938.4, 939.0, 939.9, 939.5, 882.1, 941.9, 939.0, 954.1, 937.5, 939.6, 939.5, 945.7, 939.8, 945.9, 946.0, 945.7, 945.9, 945.8, 939.8, 902.8, 945.8, 946.0, 944.6, 939.7, 939.8, 939.0, 945.8, 939.6, 945.6, 939.8, 940.0, 938.1, 939.9, 945.5, 939.5, 939.7, 939.2, 945.4, 939.6, 939.6, 945.2, 945.9, 945.7, 944.5, 938.9, 939.8, 945.6, 939.7, 939.9, 939.9, 945.8, 946.0, 941.0, 938.6, 945.2, 945.4, 938.4, 945.5, 939.4, 939.4, 939.2, 939.4, 939.2, 945.6, 939.3, 945.3, 939.3, 939.4, 939.5, 939.2, 945.5, 937.6, 945.6, 945.4, 939.2, 945.4, 945.4, 939.5, 945.1, 938.7, 945.6, 940.2, 944.4, 939.2, 945.4, 945.6, 870.6, 944.6, 939.0, 945.2, 944.7, 895.7, 929.7, 936.1, 935.9, 936.4, 923.2, 937.6, 845.4]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 6.0122744154, "std_execution_time": 0.17539893427717618, "avg_memory_usage": 3.55546875, "std_memory_usage": 0.0477937309974462, "avg_cpu_usage": 936.8923429235924, "std_cpu_usage": 1.18073922465826}, "evalperf_canonical_results": {"execution_times": [0.0011289820000000002, 0.00036041000000000003, 0.00037135600000000004, 0.000363525, 0.00037081500000000003], "memory_usages": [3.140625, 3.53515625, 3.2109375, 3.5625, 3.1953125], "cpu_usages_list": [[], [], [], [], []], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0005190176000000001, "std_execution_time": 0.0003410127737567906, "avg_memory_usage": 3.32890625, "std_memory_usage": 0.20268130627678146, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "original_canonical_results": {"execution_times": [0.00037408, 0.00040088000000000005, 0.00038358600000000005, 0.00038165400000000004, 0.000370775], "memory_usages": [3.53515625, 3.19140625, 3.625, 3.55859375, 3.13671875], "cpu_usages_list": [[], [852.4], [919.3], [], []], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.00038219500000000005, "std_execution_time": 1.1700709508401639e-05, "avg_memory_usage": 3.409375, "std_memory_usage": 0.22717455043905666, "avg_cpu_usage": 354.34, "std_cpu_usage": 485.77620155787787}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 11583.950940006658, "memory_usage_ratio": 1.0680591410467026, "cpu_usage_ratio": null}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 6.0122744154, "std": 0.17539893427717618, "n": 5, "confidence_interval_95": [5.794487873384722, 6.2300609574152785]}, "canonical": {"mean": 0.0005190176000000001, "std": 0.0003410127737567906, "n": 5, "confidence_interval_95": [9.559431589858703e-05, 0.0009424408841014131]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 76.62369841375795, "p_value": 1.7386246542922298e-07, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 76.64053594520578, "p_value": 9.363145461376103e-13, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 48.471730936571184, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 11583.950940006658, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.55546875, "std": 0.0477937309974462, "n": 5, "confidence_interval_95": [3.4961249948806357, 3.6148125051193647]}, "canonical": {"mean": 3.32890625, "std": 0.20268130627678146, "n": 5, "confidence_interval_95": [3.0772441688872094, 3.580568331112791]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.9227396930546234, "p_value": 0.04312525646070522, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 2.432812282283701, "p_value": 0.041021879251831636, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 21.0, "p_value": 0.0936926194932482, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.5386455863297996, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0680591410467026, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 15730.90808461649, "memory_usage_ratio": 1.0428505957836849, "cpu_usage_ratio": 2.6440490571868613}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 6.0122744154, "std": 0.17539893427717618, "n": 5, "confidence_interval_95": [5.794487873384722, 6.2300609574152785]}, "canonical": {"mean": 0.00038219500000000005, "std": 1.1700709508401637e-05, "n": 5, "confidence_interval_95": [0.00036766665024997724, 0.00039672334975002286]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 76.64185414271815, "p_value": 1.736978718507042e-07, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 76.64242490243238, "p_value": 9.361301740045823e-13, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 48.472925618018934, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 15730.90808461649, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.55546875, "std": 0.0477937309974462, "n": 5, "confidence_interval_95": [3.4961249948806357, 3.6148125051193647]}, "canonical": {"mean": 3.409375, "std": 0.22717455043905666, "n": 5, "confidence_interval_95": [3.1273005394116975, 3.691449460588302]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.2676436718681188, "p_value": 0.27369426405719954, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.4071889138690508, "p_value": 0.19701480806274088, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 4.0, "p_value": 0.4375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 16.0, "p_value": 0.5296192990034517, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.889984413192941, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0428505957836849, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 936.8923429235923, "std": 1.1807392246582769, "n": 5, "confidence_interval_95": [935.4262615370671, 938.3584243101175]}, "canonical": {"mean": 885.8499999999999, "std": 47.30544366138001, "n": 2, "confidence_interval_95": [460.82745156634655, 1310.8725484336533]}}, "statistical_tests": {"paired_t_test": null, "independent_t_test": {"t_statistic": 2.88014934736667, "p_value": 0.03458205797630399, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": null, "mann_whitney_u": {"u_statistic": 10.0, "p_value": 0.09523809523809523, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.4097058293898983, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0576196228747445, "better_performance": true}}}}}, "Mbpp/4": {"task_id": "Mbpp/4", "entry_point": "heap_queue_largest", "evalperf_canonical_score": 100.00000000000001, "generated_code_results": {"execution_times": [0.08485077, 0.08460840800000001, 0.08466730800000001, 0.084667563, 0.08496547900000001], "memory_usages": [1.67578125, 3.53515625, 3.65625, 3.21875, 3.59765625], "cpu_usages_list": [[968.3, 945.2, 945.7, 939.6, 945.8, 945.7, 945.7, 938.0, 884.0], [855.3, 939.6, 940.1, 944.8, 939.2, 942.9, 934.6, 945.5], [848.9, 938.3, 945.6, 939.8, 946.0, 939.6, 894.5, 899.3], [945.3, 945.8, 945.7, 945.8, 940.0, 932.4, 939.7, 946.1, 939.4], [866.8, 816.0, 939.6, 944.5, 939.1, 939.0, 945.5, 945.5, 939.3]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.08475190560000001, "std_execution_time": 0.00015020798587392147, "avg_memory_usage": 3.13671875, "std_memory_usage": 0.8339958100796843, "avg_cpu_usage": 930.15, "std_cpu_usage": 10.92243698217709}, "evalperf_canonical_results": {"execution_times": [0.000439944, 0.00047671400000000004, 0.00047177400000000005, 0.000500164, 0.00047204], "memory_usages": [3.640625, 3.2109375, 3.19140625, 3.55859375, 3.5859375], "cpu_usages_list": [[937.7], [], [868.9], [927.4], []], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.00047212720000000004, "std_execution_time": 2.1465039044921395e-05, "avg_memory_usage": 3.4375, "std_memory_usage": 0.21785881524259296, "avg_cpu_usage": 546.8, "std_cpu_usage": 499.846991588426}, "original_canonical_results": {"execution_times": [0.000983722, 0.000995859, 0.000994871, 0.000987238, 0.000989394], "memory_usages": [3.58984375, 1.57421875, 3.13671875, 3.2421875, 3.2265625], "cpu_usages_list": [[953.1], [944.4], [], [937.6], [938.6]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0009902168, "std_execution_time": 5.129051442518374e-06, "avg_memory_usage": 2.95390625, "std_memory_usage": 0.7903831383393081, "avg_cpu_usage": 754.74, "std_cpu_usage": 421.95744809162926}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 179.510745409288, "memory_usage_ratio": 0.9125, "cpu_usage_ratio": 1.7010790051207023}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.08475190560000001, "std": 0.00015020798587392147, "n": 5, "confidence_interval_95": [0.08456539775169766, 0.08493841344830236]}, "canonical": {"mean": 0.00047212720000000004, "std": 2.1465039044921395e-05, "n": 5, "confidence_interval_95": [0.000445474833797883, 0.0004987795662021171]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1166.064860928262, "p_value": 3.2453256944908687e-12, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1242.0116441155355, "p_value": 1.9779011127397124e-22, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 785.5171351711116, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 179.510745409288, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.13671875, "std": 0.8339958100796843, "n": 5, "confidence_interval_95": [2.1011761779712703, 4.17226132202873]}, "canonical": {"mean": 3.4375, "std": 0.21785881524259296, "n": 5, "confidence_interval_95": [3.1669925524219464, 3.7080074475780536]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.6859814095887776, "p_value": 0.5304190196831186, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.7802575893760636, "p_value": 0.4577013449987305, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 7.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 13.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.49347822881215175, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9125, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 930.15, "std": 10.922436982177148, "n": 5, "confidence_interval_95": [916.5880032265812, 943.7119967734187]}, "canonical": {"mean": 911.3333333333334, "std": 37.10745657321902, "n": 3, "confidence_interval_95": [819.1533010738046, 1003.5133655928621]}}, "statistical_tests": {"paired_t_test": null, "independent_t_test": {"t_statistic": 1.1103043991547341, "p_value": 0.30937221365891854, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": null, "mann_whitney_u": {"u_statistic": 10.0, "p_value": 0.5714285714285714, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.8108516868190236, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0206474030724213, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 85.58924227502504, "memory_usage_ratio": 1.061888389314996, "cpu_usage_ratio": 1.2324111614595754}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.08475190560000001, "std": 0.00015020798587392147, "n": 5, "confidence_interval_95": [0.08456539775169766, 0.08493841344830236]}, "canonical": {"mean": 0.0009902168000000002, "std": 5.1290514425183736e-06, "n": 5, "confidence_interval_95": [0.0009838482414788901, 0.0009965853585211103]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1222.4732565999118, "p_value": 2.6865309285816755e-12, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1246.1902937807065, "p_value": 1.9254624524401445e-22, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 788.1599452682794, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 85.58924227502501, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.13671875, "std": 0.8339958100796843, "n": 5, "confidence_interval_95": [2.1011761779712703, 4.17226132202873]}, "canonical": {"mean": 2.95390625, "std": 0.7903831383393081, "n": 5, "confidence_interval_95": [1.9725159623367743, 3.935296537663226]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.29367741405882986, "p_value": 0.783611961819339, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.3557640487964273, "p_value": 0.7312160653041362, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 17.0, "p_value": 0.42063492063492064, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.22500494075999905, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.061888389314996, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 930.15, "std": 10.922436982177148, "n": 5, "confidence_interval_95": [916.5880032265812, 943.7119967734187]}, "canonical": {"mean": 943.425, "std": 7.1126061796409505, "n": 4, "confidence_interval_95": [932.1072563713298, 954.7427436286702]}}, "statistical_tests": {"paired_t_test": null, "independent_t_test": {"t_statistic": -2.087678899542473, "p_value": 0.07522739616364656, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": null, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.19047619047619047, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -1.4004575803706774, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9859289291676604, "better_performance": false}}}}}, "Mbpp/12": {"task_id": "Mbpp/12", "entry_point": "sort_matrix", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.2662945530000003, 2.3156335390000002, 2.291954757, 2.300558361, 2.2662219340000003], "memory_usages": [3.5546875, 3.23828125, 3.27734375, 3.12890625, 3.5078125], "cpu_usages_list": [[903.7, 941.2, 938.8, 937.6, 945.5, 945.6, 945.8, 980.4, 945.6, 939.3, 945.4, 945.7, 939.7, 938.9, 938.0, 939.7, 935.5, 939.9, 945.7, 939.9, 939.3, 939.6, 939.4, 935.4, 939.7, 939.7, 939.9, 939.6, 944.9, 939.5, 941.6, 939.8, 939.9, 939.7, 939.8, 938.0, 939.4, 939.7, 939.5, 944.4, 930.3, 945.5, 945.5, 939.3, 945.5, 945.8, 945.5, 939.6, 939.9, 944.9, 939.7, 945.0, 939.4, 945.4, 945.7, 939.4, 912.1, 945.6, 945.5, 945.5, 945.9, 942.2, 945.7, 945.6, 945.7, 945.8, 945.6, 939.1, 945.7, 945.9, 945.6, 939.5, 945.8, 945.8, 944.6, 945.7, 945.6, 945.0, 918.6, 939.7, 939.5, 945.8, 945.8, 945.7, 945.8, 945.8, 945.6, 945.8, 945.8, 945.6, 945.8, 974.6, 945.8, 939.6, 939.6, 945.9, 945.6, 946.0, 945.3, 945.5, 945.7, 946.0, 939.3, 886.9, 945.6, 945.8, 939.7, 945.6, 939.1, 933.4, 945.6, 945.8, 945.5, 939.3, 945.7, 945.7, 945.7, 945.5, 939.3, 945.8, 939.2, 939.3, 939.3, 945.8, 939.4, 945.7, 945.6, 945.6, 945.6, 945.6, 945.8, 945.5, 900.2, 869.8, 871.1, 860.8, 944.0, 941.7, 937.4, 936.7, 936.0, 925.8, 937.2, 934.1, 935.7, 935.3, 935.7, 936.1, 936.2, 936.2, 934.1, 936.2, 936.2, 936.4, 933.4, 937.2, 925.0, 937.3, 937.0, 936.8, 856.5, 937.7, 814.3, 899.9, 899.4, 856.5, 932.3, 944.6, 945.8, 939.6, 945.6, 945.4, 945.8, 951.9, 945.6, 944.6, 939.8, 939.7, 945.9, 945.7, 945.7, 945.7, 945.7, 939.0, 939.8, 945.3, 945.7, 939.0, 957.7, 940.2, 945.6, 939.6, 938.5, 939.5, 939.8, 936.7, 939.6, 945.7, 945.5, 945.6, 936.8, 945.8, 970.6, 945.4, 944.2, 936.0, 944.4, 939.7, 939.2, 945.7, 939.4, 944.2, 945.2, 939.4, 939.4, 939.1, 963.2, 945.3, 945.5, 939.4, 945.5, 945.3, 945.0, 944.5, 939.1, 933.4, 945.2, 939.7, 935.6, 945.7, 939.4, 945.6, 939.8, 945.9, 939.9, 945.5, 945.7, 939.5, 937.0, 938.3, 940.6, 939.4, 935.2, 935.5, 940.0, 939.7, 939.3, 939.5, 939.9, 939.8, 945.7, 939.7, 939.5, 945.9, 945.8, 938.5, 939.5, 939.7, 960.0, 944.7, 939.8, 939.6, 939.7, 939.8, 939.7, 945.8, 939.5, 944.8, 945.8, 945.7, 945.8, 945.7, 945.6, 935.3, 945.8, 939.6, 945.5, 939.9, 939.3, 939.4, 939.9, 939.9, 940.1, 939.8, 899.0, 862.3, 937.9, 945.3, 939.2, 945.6, 945.9, 939.8, 942.0, 945.8, 939.9, 945.7, 939.7, 939.9, 939.8, 945.7, 865.9, 937.2, 897.9, 945.7, 945.2, 929.9], [945.6, 939.5, 939.5, 945.4, 939.3, 934.1, 945.3, 939.6, 941.0, 939.9, 945.7, 939.3, 945.7, 936.5, 939.8, 945.5, 939.2, 945.8, 945.5, 863.4, 837.3, 939.5, 939.4, 939.7, 939.5, 945.9, 945.7, 945.7, 945.7, 939.7, 939.6, 939.2, 945.8, 939.3, 939.7, 945.6, 939.7, 945.5, 939.4, 939.9, 945.7, 939.9, 939.6, 945.3, 939.6, 846.0, 856.9, 939.5, 936.6, 939.8, 939.9, 945.6, 944.9, 939.4, 945.9, 938.6, 945.8, 939.8, 939.9, 939.2, 943.0, 945.6, 939.2, 944.9, 939.4, 945.3, 939.4, 945.7, 938.9, 924.0, 945.5, 939.5, 939.5, 939.2, 945.5, 939.3, 939.8, 939.5, 945.6, 939.4, 939.2, 939.6, 939.8, 939.2, 945.6, 945.5, 926.1, 945.4, 939.5, 935.9, 938.9, 939.5, 937.6, 938.6, 945.5, 946.0, 939.5, 939.7, 961.6, 945.8, 945.7, 945.9, 945.8, 946.1, 945.9, 940.1, 945.8, 945.9, 940.0, 939.4, 948.9, 945.8, 939.9, 945.6, 923.1, 946.1, 939.3, 945.8, 924.1, 939.3, 946.0, 945.3, 946.1, 939.6, 933.1, 939.8, 939.5, 945.7, 945.2, 940.0, 945.8, 939.9, 939.7, 928.1, 945.7, 945.8, 945.9, 945.7, 945.8, 975.3, 939.8, 939.5, 945.2, 945.8, 945.8, 944.0, 939.5, 945.7, 938.7, 945.6, 944.2, 938.1, 945.5, 938.8, 945.6, 945.9, 945.9, 942.5, 945.6, 939.1, 945.4, 943.9, 944.4, 945.4, 938.7, 939.7, 939.5, 939.3, 945.7, 939.2, 939.8, 939.4, 945.4, 945.6, 945.8, 936.1, 945.7, 939.8, 945.4, 945.7, 939.4, 937.4, 895.1, 940.5, 918.1, 939.4, 944.5, 940.3, 939.7, 946.0, 883.7, 851.1, 851.5, 937.8, 944.7, 944.6, 945.8, 961.2, 945.7, 939.4, 939.9, 945.8, 939.6, 945.5, 945.7, 945.7, 940.1, 940.1, 939.3, 939.7, 945.7, 945.6, 945.7, 944.2, 945.9, 945.5, 939.9, 939.4, 945.7, 939.5, 945.7, 945.7, 945.8, 945.8, 939.9, 945.7, 945.7, 945.8, 939.7, 945.9, 939.6, 945.8, 939.6, 939.6, 945.7, 945.9, 945.8, 945.7, 945.0, 939.8, 945.7, 945.6, 945.7, 939.6, 959.4, 939.3, 939.4, 939.7, 939.7, 939.6, 939.6, 939.7, 945.7, 945.7, 945.9, 945.9, 945.7, 939.7, 939.2, 945.8, 945.7, 939.2, 939.9, 945.5, 944.8, 939.7, 939.7, 939.7, 945.9, 940.0, 939.8, 939.7, 945.6, 945.8, 945.7, 933.0, 945.7, 939.8, 936.3, 945.8, 939.4, 925.0, 939.5, 955.6, 939.8, 939.8, 939.5, 939.8, 939.7, 946.1, 939.7, 940.0, 945.9, 939.7, 940.2, 940.0, 945.7, 946.0, 945.6, 939.9, 945.5, 939.9, 939.5, 939.9, 939.6, 945.5, 945.3], [907.3, 939.1, 903.5, 919.0, 893.4, 945.6, 947.3, 945.0, 945.6, 955.8, 939.3, 945.5, 939.6, 939.5, 945.8, 945.6, 939.5, 945.3, 936.8, 939.1, 939.5, 945.5, 939.4, 939.3, 945.6, 939.7, 939.5, 944.6, 945.8, 939.9, 939.8, 939.8, 945.7, 945.4, 939.3, 939.4, 940.0, 939.9, 939.5, 945.4, 945.8, 945.8, 939.7, 939.6, 939.2, 945.4, 939.8, 945.9, 945.6, 899.1, 866.5, 945.3, 945.4, 937.7, 938.1, 936.0, 939.4, 944.7, 945.7, 939.7, 945.7, 939.8, 939.5, 939.8, 945.8, 945.9, 945.8, 945.7, 937.8, 945.8, 939.7, 864.5, 939.5, 864.2, 940.0, 945.7, 945.5, 939.8, 940.1, 874.7, 938.2, 938.9, 939.7, 945.7, 945.5, 939.2, 945.0, 939.2, 945.2, 939.5, 918.6, 945.5, 939.3, 945.4, 939.3, 939.4, 945.6, 945.6, 939.6, 939.3, 945.6, 939.5, 939.2, 939.6, 940.5, 940.9, 939.4, 896.2, 941.0, 939.6, 939.8, 939.6, 945.9, 939.4, 945.8, 948.0, 945.7, 945.7, 939.7, 939.8, 945.9, 945.8, 951.9, 945.8, 945.8, 938.2, 945.9, 939.9, 947.1, 945.8, 939.8, 954.1, 939.9, 939.8, 928.5, 939.7, 945.8, 945.8, 945.7, 940.0, 924.7, 936.5, 917.1, 939.5, 945.8, 939.6, 932.1, 945.7, 939.7, 945.0, 934.5, 946.0, 945.8, 945.8, 939.6, 939.5, 859.5, 938.2, 932.9, 945.6, 939.5, 945.8, 945.6, 855.5, 898.0, 853.5, 945.3, 945.2, 939.5, 937.4, 945.4, 939.2, 978.0, 945.3, 938.5, 945.4, 938.8, 939.5, 945.6, 939.6, 846.8, 919.9, 940.9, 958.1, 944.4, 939.9, 939.9, 939.7, 939.4, 939.7, 939.7, 939.1, 945.9, 933.0, 945.8, 939.8, 938.1, 939.9, 851.4, 938.3, 938.3, 945.5, 945.4, 945.6, 935.8, 945.9, 939.7, 939.8, 939.7, 945.9, 945.9, 945.9, 945.6, 945.7, 938.2, 825.2, 849.6, 941.0, 940.0, 939.0, 940.1, 962.1, 945.8, 939.6, 939.9, 939.9, 945.9, 945.9, 939.9, 945.7, 939.9, 945.9, 945.8, 946.0, 939.7, 945.3, 939.8, 918.0, 945.6, 945.3, 937.8, 939.3, 938.3, 854.9, 939.2, 938.8, 938.9, 939.2, 945.5, 945.0, 939.7, 939.5, 945.4, 939.7, 939.8, 939.2, 945.6, 945.5, 945.7, 936.4, 939.6, 939.4, 939.7, 945.6, 938.8, 937.6, 936.7, 926.8, 939.6, 939.6, 939.4, 945.4, 945.6, 939.7, 939.5, 939.4, 945.2, 945.6, 939.4, 945.5, 945.4, 941.7, 939.1, 945.9, 939.6, 939.9, 945.7, 939.7, 945.6, 939.9, 930.4, 939.7, 945.7, 939.6, 939.4, 939.6, 940.0, 939.4, 945.8, 945.6, 945.4, 945.9], [1793.4, 933.8, 940.0, 939.4, 945.8, 945.4, 939.3, 939.6, 939.9, 945.9, 939.7, 945.7, 931.5, 940.0, 939.5, 939.0, 972.0, 945.7, 939.9, 939.8, 945.7, 939.4, 956.0, 945.7, 945.9, 945.9, 939.5, 945.6, 946.0, 945.9, 939.5, 928.3, 939.6, 939.4, 939.8, 945.7, 939.5, 939.8, 945.8, 945.7, 939.5, 945.8, 945.6, 930.5, 942.8, 945.3, 945.6, 945.1, 945.7, 945.6, 945.9, 946.0, 945.6, 945.6, 945.7, 945.7, 925.4, 939.6, 940.0, 946.0, 946.1, 939.8, 946.0, 945.8, 940.0, 945.7, 945.9, 945.7, 977.5, 945.8, 939.8, 939.9, 941.2, 939.8, 945.8, 945.8, 966.8, 945.5, 945.8, 943.6, 945.8, 945.6, 945.7, 945.9, 945.8, 945.8, 939.7, 945.9, 945.9, 945.8, 945.8, 964.1, 945.7, 945.2, 902.8, 855.3, 945.8, 939.6, 939.9, 945.9, 939.8, 930.3, 939.7, 945.9, 939.9, 937.8, 935.1, 911.4, 945.4, 939.5, 939.3, 939.7, 944.9, 938.9, 945.4, 945.3, 945.6, 945.3, 939.6, 939.6, 945.2, 945.5, 945.3, 945.4, 927.2, 945.5, 939.4, 975.2, 939.1, 939.5, 939.1, 939.1, 945.3, 939.4, 939.2, 945.2, 939.5, 945.6, 939.3, 945.5, 939.4, 939.9, 945.6, 937.9, 945.8, 945.6, 945.9, 939.7, 945.8, 935.8, 938.2, 939.4, 945.7, 939.7, 939.9, 945.8, 939.9, 945.5, 945.6, 939.9, 939.7, 945.9, 939.8, 945.8, 939.3, 939.5, 940.2, 938.9, 939.7, 928.4, 939.1, 945.4, 940.2, 939.1, 945.0, 945.7, 956.8, 945.7, 939.1, 957.7, 939.2, 945.6, 945.6, 945.6, 945.5, 939.4, 945.6, 945.6, 945.6, 929.1, 939.4, 939.6, 945.4, 939.3, 939.6, 945.4, 938.9, 945.4, 945.5, 939.5, 945.1, 939.1, 945.1, 939.2, 939.5, 945.4, 940.6, 938.0, 939.1, 918.0, 939.9, 938.2, 938.0, 872.2, 945.8, 939.8, 939.9, 939.9, 945.6, 945.7, 939.7, 945.7, 945.8, 939.8, 945.6, 945.9, 945.8, 939.7, 946.1, 868.4, 919.4, 939.4, 945.3, 939.8, 939.4, 945.3, 945.4, 945.6, 945.3, 939.0, 945.4, 945.4, 945.5, 945.4, 945.5, 945.6, 939.3, 945.2, 945.4, 939.7, 939.6, 945.5, 939.1, 939.4, 945.5, 939.6, 939.4, 939.4, 939.5, 939.7, 945.5, 939.4, 899.1, 891.0, 945.2, 945.7, 939.2, 944.3, 939.9, 939.7, 939.5, 926.7, 939.4, 945.8, 957.7, 945.7, 939.7, 939.9, 945.7, 945.4, 939.6, 945.5, 945.6, 941.9, 945.5, 945.3, 935.1, 945.6, 939.2, 951.9, 939.0, 939.5, 945.5, 939.3, 945.4, 939.6, 945.4, 945.5, 939.2, 939.5, 939.5, 939.6, 953.7, 896.8, 938.1, 939.6], [870.0, 945.5, 939.1, 945.5, 898.6, 937.7, 937.9, 945.7, 945.5, 945.6, 939.7, 945.8, 939.8, 939.8, 944.7, 940.0, 939.8, 945.8, 946.1, 939.9, 945.7, 945.9, 945.7, 945.6, 945.5, 945.8, 945.7, 959.8, 939.5, 939.5, 939.8, 945.9, 939.8, 945.7, 940.0, 945.9, 945.7, 945.8, 939.9, 945.8, 939.6, 945.5, 944.1, 946.1, 945.6, 939.5, 939.0, 945.7, 945.9, 940.1, 945.5, 939.3, 946.0, 945.8, 939.7, 974.1, 945.8, 945.4, 945.7, 945.7, 945.5, 939.2, 979.0, 938.7, 939.9, 945.8, 939.4, 939.9, 939.9, 939.5, 945.9, 939.7, 945.8, 945.7, 945.7, 939.2, 939.8, 939.9, 945.9, 939.8, 921.6, 939.9, 940.1, 939.8, 939.8, 929.3, 939.8, 946.0, 938.2, 939.9, 946.1, 945.9, 939.7, 939.6, 939.6, 945.5, 939.6, 984.3, 945.5, 938.9, 961.4, 945.6, 945.4, 939.4, 939.4, 945.5, 945.5, 945.6, 945.5, 945.5, 945.6, 933.6, 939.4, 937.1, 939.2, 959.7, 939.5, 936.9, 939.5, 939.4, 945.5, 945.6, 939.3, 945.5, 939.1, 939.5, 939.4, 945.6, 939.7, 925.1, 936.9, 939.1, 945.5, 945.5, 950.6, 939.6, 934.2, 939.4, 944.6, 939.6, 926.6, 935.4, 939.2, 946.8, 939.6, 939.5, 939.2, 941.6, 945.8, 945.7, 945.5, 939.5, 938.9, 945.0, 859.4, 899.3, 938.8, 945.5, 939.9, 945.9, 946.0, 945.8, 939.3, 939.9, 945.8, 945.8, 939.9, 945.5, 939.4, 937.9, 945.3, 940.0, 944.4, 945.0, 945.5, 942.0, 945.7, 945.7, 939.3, 939.4, 938.8, 948.0, 944.8, 938.9, 964.5, 939.3, 954.5, 945.7, 938.1, 945.8, 945.7, 939.7, 945.7, 939.9, 945.8, 945.5, 939.0, 945.8, 945.6, 945.7, 957.9, 939.9, 945.5, 945.8, 945.7, 939.7, 939.8, 939.7, 945.9, 945.8, 939.9, 939.7, 946.0, 945.8, 945.8, 963.9, 945.8, 939.9, 956.3, 945.9, 945.9, 939.9, 939.9, 945.7, 945.9, 945.4, 938.8, 938.7, 945.9, 939.5, 946.0, 945.7, 939.5, 954.0, 945.7, 942.5, 939.9, 939.3, 940.0, 883.3, 944.6, 938.4, 976.2, 939.5, 945.6, 945.7, 945.8, 939.7, 939.7, 945.8, 945.8, 944.6, 945.7, 945.7, 935.6, 945.9, 939.8, 945.7, 939.5, 939.9, 945.7, 936.6, 945.8, 938.8, 938.8, 945.8, 939.7, 939.8, 939.7, 945.9, 939.8, 945.7, 939.9, 945.8, 945.6, 945.7, 945.9, 896.9, 898.8, 945.6, 938.9, 939.9, 945.9, 939.6, 946.0, 945.8, 945.8, 945.6, 945.1, 936.9, 945.8, 939.5, 982.8, 938.9, 919.8, 945.7, 939.1, 945.5, 944.9, 944.8, 938.4, 964.4, 939.3, 945.1, 945.1, 945.0, 943.6, 945.5, 943.1, 919.8, 844.0, 926.0, 933.9]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 2.2881326288, "std_execution_time": 0.021692669543979047, "avg_memory_usage": 3.34140625, "std_memory_usage": 0.18239719787586856, "avg_cpu_usage": 940.4470188821986, "std_cpu_usage": 2.5625937685571705}, "evalperf_canonical_results": {"execution_times": [0.005346888, 0.00646153, 0.006475901, 0.007101487, 0.006166471000000001], "memory_usages": [3.234375, 3.5546875, 3.5625, 3.56640625, 3.66015625], "cpu_usages_list": [[945.5, 938.6, 939.2, 945.5, 945.8, 939.9, 945.5, 945.6, 939.6, 945.7, 945.6, 945.8, 940.0, 939.7, 946.0, 939.6, 939.5, 939.9, 939.7, 939.5, 939.8, 939.2, 939.8, 978.9, 945.9, 940.0, 945.6, 939.7, 939.6, 946.0, 945.8, 945.9, 939.9, 939.8, 945.9, 939.2, 939.8, 945.6, 939.8, 940.0, 945.7, 939.8, 945.5, 945.6, 939.5, 945.5, 939.8, 945.8, 939.7, 939.8, 958.4, 937.8, 945.8, 937.5, 944.9, 945.7, 939.7, 945.6, 939.7, 939.3, 939.7, 939.0], [872.3, 939.3, 946.0, 945.7, 945.4, 939.0, 939.8, 939.3, 945.6, 939.7, 945.7, 945.8, 945.8, 938.2, 938.3, 809.0, 939.7, 939.7, 936.8, 945.8, 938.3, 945.6, 943.9, 945.9, 938.3, 939.1, 928.7, 934.3, 935.9, 924.9, 937.0, 935.7, 945.4, 937.0, 945.7, 939.4, 945.8, 939.7, 945.0, 939.1, 945.8, 929.6, 929.4, 937.0, 936.9, 936.9, 937.2, 925.1, 937.1, 936.7, 936.1, 924.8, 925.4, 936.1, 935.8, 926.0, 925.7, 936.4, 936.4, 936.5, 936.6, 936.1, 936.2], [919.0, 945.3, 970.7, 939.7, 945.8, 945.5, 939.3, 939.7, 939.7, 945.9, 945.6, 939.6, 945.7, 939.8, 945.7, 945.8, 939.7, 963.4, 939.8, 939.7, 948.1, 939.4, 939.9, 945.2, 939.2, 945.8, 945.6, 945.9, 945.7, 945.1, 938.0, 940.5, 945.5, 945.0, 939.5, 939.6, 937.4, 939.2, 945.4, 939.3, 939.4, 939.1, 936.5, 945.4, 946.5, 945.4, 939.0, 945.2, 945.4, 939.2, 939.0, 939.0, 945.4, 939.0, 938.8, 939.7, 910.9, 939.4, 932.0, 945.3], [938.3, 939.5, 945.2, 939.1, 945.7, 930.4, 938.7, 889.7, 945.4, 871.5, 871.5, 945.7, 945.5, 939.4, 939.3, 939.7, 942.7, 852.2, 859.4, 939.2, 945.6, 977.4, 945.5, 939.6, 939.5, 945.6, 939.7, 945.6, 944.3, 945.5, 939.5, 939.7, 938.9, 943.7, 945.5, 939.9, 945.7, 945.8, 945.7, 939.8, 945.7, 945.6, 945.8, 939.8, 945.1, 939.6, 945.4, 955.4, 937.9, 931.7, 945.6, 945.8, 945.8, 945.8, 945.8, 945.5, 945.9, 922.7, 945.2, 945.8, 967.5, 946.0, 945.7], [945.6, 939.6, 945.3, 939.5, 945.2, 938.9, 963.4, 939.8, 939.5, 871.9, 863.1, 939.2, 929.1, 945.7, 920.5, 945.0, 945.3, 929.0, 945.3, 928.0, 918.5, 938.8, 939.3, 945.7, 939.8, 937.6, 939.3, 945.8, 945.6, 945.9, 939.4, 945.6, 939.6, 939.3, 945.8, 945.4, 939.7, 945.7, 939.1, 945.5, 940.0, 939.7, 925.2, 945.8, 939.9, 945.7, 945.7, 940.1, 939.8, 940.0, 940.3, 945.6, 940.1, 939.5, 939.6, 945.7, 945.6, 945.0, 945.3, 945.6, 941.5]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0063104554000000005, "std_execution_time": 0.0006374010541466807, "avg_memory_usage": 3.515625, "std_memory_usage": 0.1630128251161354, "avg_cpu_usage": 939.2238082898945, "std_cpu_usage": 3.239209441194967}, "original_canonical_results": {"execution_times": [0.006744252, 0.006054935, 0.006634976000000001, 0.006603918, 0.0069446740000000005], "memory_usages": [3.1640625, 3.1875, 3.1875, 3.57421875, 3.578125], "cpu_usages_list": [[847.7, 941.3, 836.4, 938.3, 945.4, 945.7, 939.4, 945.5, 936.4, 945.8, 939.3, 939.9, 939.5, 945.6, 945.6, 939.5, 945.7, 939.2, 945.8, 932.5, 945.7, 945.7, 945.6, 939.5, 945.8, 945.6, 939.6, 941.2, 946.0, 945.5, 939.0, 945.2, 939.7, 945.6, 945.8, 931.4, 841.5, 859.8, 874.0, 944.4, 939.7, 939.3, 939.4, 945.6, 945.8, 939.7, 939.4, 939.7, 945.6, 939.8, 939.9, 945.5, 935.5, 939.7, 939.9, 939.6, 939.9, 939.5, 945.6, 939.9, 942.1, 939.7, 938.6, 944.4, 938.0], [902.7, 945.2, 939.4, 939.5, 939.4, 939.8, 937.7, 939.9, 939.9, 939.6, 939.8, 945.6, 946.0, 930.2, 945.8, 945.6, 940.0, 945.6, 939.9, 945.5, 939.9, 945.7, 945.7, 935.4, 945.9, 945.7, 939.4, 941.5, 945.9, 940.7, 945.8, 945.8, 945.7, 960.6, 939.7, 939.4, 939.9, 945.6, 939.8, 938.5, 939.8, 945.6, 945.8, 938.5, 940.0, 945.4, 939.5, 939.5, 939.6, 939.7, 944.4, 945.1, 938.4, 940.9, 941.2, 933.5, 945.4, 944.7, 939.1, 945.2, 939.6, 939.6], [924.2, 945.0, 945.2, 945.4, 939.2, 945.2, 945.5, 939.3, 945.2, 926.7, 945.0, 928.9, 939.2, 888.7, 957.3, 936.8, 934.1, 936.1, 936.0, 937.3, 937.4, 978.8, 937.3, 956.1, 937.1, 937.3, 937.3, 936.3, 935.6, 934.1, 936.5, 954.3, 915.2, 936.2, 938.7, 899.9, 937.7, 945.4, 945.4, 945.7, 945.8, 945.7, 939.4, 945.3, 945.6, 939.3, 945.5, 935.4, 926.4, 931.1, 931.3, 926.7, 905.0, 937.7, 858.1, 941.5, 939.1, 939.4, 945.6], [853.4, 941.1, 945.4, 939.2, 947.9, 945.7, 937.9, 939.3, 939.9, 939.6, 945.1, 936.4, 942.5, 934.5, 935.8, 935.4, 924.4, 935.3, 939.0, 937.1, 937.1, 844.6, 921.3, 942.0, 945.5, 939.3, 945.7, 945.8, 945.6, 945.7, 939.9, 981.9, 945.5, 939.7, 863.9, 896.4, 930.9, 937.0, 937.0, 937.3, 937.3, 937.3, 937.3, 937.1, 936.1, 937.3, 932.2, 861.9, 938.2, 945.5, 939.4, 945.6, 945.7, 943.1, 938.8, 901.5, 945.3, 944.7, 945.6, 945.8, 945.3, 939.1, 939.7], [857.0, 899.3, 933.0, 945.9, 940.9, 860.0, 938.1, 903.8, 945.1, 939.7, 939.4, 939.8, 945.9, 945.7, 939.5, 939.4, 945.5, 939.8, 948.1, 945.6, 945.6, 945.5, 945.5, 939.6, 936.4, 933.3, 939.6, 945.6, 939.7, 939.3, 945.6, 948.9, 945.6, 939.6, 939.1, 939.8, 939.8, 939.1, 944.0, 945.9, 945.6, 945.9, 945.8, 945.8, 968.6, 945.9, 894.9, 877.3, 939.8, 945.4, 868.6, 852.8, 851.3, 945.6, 945.4, 937.9, 933.9, 938.9, 945.3, 939.6, 939.3, 945.2, 895.5]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.006596551, "std_execution_time": 0.00033086396498410053, "avg_memory_usage": 3.33828125, "std_memory_usage": 0.2173785131257227, "avg_cpu_usage": 935.7173199871024, "std_cpu_usage": 3.5101681819568995}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 362.5938991344428, "memory_usage_ratio": 0.9504444444444444, "cpu_usage_ratio": 1.0013023632722122}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.2881326288000006, "std": 0.021692669543979047, "n": 5, "confidence_interval_95": [2.2611976220023107, 2.3150676355976905]}, "canonical": {"mean": 0.0063104554000000005, "std": 0.0006374010541466807, "n": 5, "confidence_interval_95": [0.005519017458648938, 0.0071018933413510635]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 240.10629751440308, "p_value": 1.8050399876149984e-09, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 235.10745431858223, "p_value": 1.1991142586609976e-16, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 148.69501010614206, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 362.5938991344429, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.34140625, "std": 0.18239719787586856, "n": 5, "confidence_interval_95": [3.1149302160242853, 3.5678822839757145]}, "canonical": {"mean": 3.515625, "std": 0.1630128251161354, "n": 5, "confidence_interval_95": [3.313217843807808, 3.718032156192192]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.3228773181413231, "p_value": 0.2564344612432057, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.5924915617568969, "p_value": 0.14993947968472998, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 4.0, "p_value": 0.4375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.5, "p_value": 0.11607394330990008, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -1.0071800979500978, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9504444444444444, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 940.4470188821984, "std": 2.562593768557251, "n": 5, "confidence_interval_95": [937.2651384577596, 943.6288993066373]}, "canonical": {"mean": 939.2238082898945, "std": 3.2392094411950083, "n": 5, "confidence_interval_95": [935.2017985441208, 943.2458180356681]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.5322321955497965, "p_value": 0.6227549106471353, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.6622238371319439, "p_value": 0.5264406849036534, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 15.0, "p_value": 0.6904761904761905, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.4188271292386659, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0013023632722122, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 346.8680267612575, "memory_usage_ratio": 1.0009361104610344, "cpu_usage_ratio": 1.0050546236497593}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.2881326288000006, "std": 0.021692669543979047, "n": 5, "confidence_interval_95": [2.2611976220023107, 2.3150676355976905]}, "canonical": {"mean": 0.006596551000000001, "std": 0.0003308639649841006, "n": 5, "confidence_interval_95": [0.006185729126376291, 0.00700737287362371]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 232.0292603117951, "p_value": 2.069790797887499e-09, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 235.15208424078753, "p_value": 1.1972950485365027e-16, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 148.72323654733506, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 346.86802676125757, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.34140625, "std": 0.18239719787586856, "n": 5, "confidence_interval_95": [3.1149302160242853, 3.5678822839757145]}, "canonical": {"mean": 3.33828125, "std": 0.2173785131257227, "n": 5, "confidence_interval_95": [3.068370176268725, 3.608192323731275]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.023086893405875705, "p_value": 0.9826867523987216, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.02462506168898163, "p_value": 0.9809571906276944, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 7.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 12.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.015574256491866965, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0009361104610344, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 940.4470188821984, "std": 2.562593768557251, "n": 5, "confidence_interval_95": [937.2651384577596, 943.6288993066373]}, "canonical": {"mean": 935.7173199871024, "std": 3.5101681819568173, "n": 5, "confidence_interval_95": [931.3588705279262, 940.0757694462786]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.087493862331567, "p_value": 0.1051281087819081, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 2.4334564754592587, "p_value": 0.04098067637641966, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 2.0, "p_value": 0.1875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 22.0, "p_value": 0.05555555555555555, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.539053009867379, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.005054623649759, "better_performance": true}}}}}}}