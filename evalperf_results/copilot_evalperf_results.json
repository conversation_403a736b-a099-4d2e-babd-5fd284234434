{"model": "copilot", "total_tasks": 20, "tested_tasks": 15, "successful_tasks": 14, "task_results": {"HumanEval/9": {"task_id": "HumanEval/9", "entry_point": "rolling_max", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.4050000000000002e-05, 2.4421000000000002e-05, 2.5803e-05, 2.3419e-05, 2.3737e-05, 2.343e-05, 2.2153000000000003e-05, 2.2466e-05, 2.4169000000000002e-05, 2.1941000000000002e-05], "memory_usages": [0.52734375, 0.0078125, 0.5390625, 0.54296875, 0.5625, 0.54296875, 0.53515625, 0.5390625, 0.5390625, 0.5234375], "cpu_usages_list": [[29.9], [20.0], [29.9], [29.9], [20.0], [29.9], [30.0], [20.0], [29.9], [29.9]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3558900000000002e-05, "std_execution_time": 1.1666235658705182e-06, "avg_memory_usage": 0.4859375, "std_memory_usage": 0.16831863590952859, "avg_cpu_usage": 26.939999999999998, "std_cpu_usage": 4.789154413881431}, "evalperf_canonical_results": {"execution_times": [2.4969e-05, 2.2133000000000003e-05, 2.2989000000000003e-05, 2.4056e-05, 2.5757e-05, 2.4304000000000003e-05, 2.3006000000000003e-05, 2.3449000000000002e-05, 2.3348e-05, 2.2581000000000003e-05], "memory_usages": [0.63671875, 0.5546875, 0.0078125, 0.56640625, 0.54296875, 0.5390625, 0.5390625, 0.0078125, 0.5234375, 0.53125], "cpu_usages_list": [[20.0], [30.0], [20.0], [29.9], [30.0], [20.0], [20.0], [29.9], [29.9], [20.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3659200000000002e-05, "std_execution_time": 1.1154584309202695e-06, "avg_memory_usage": 0.444921875, "std_memory_usage": 0.23254092860052047, "avg_cpu_usage": 24.97, "std_cpu_usage": 5.238967243434318}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.9957606343409752, "memory_usage_ratio": 1.0921861281826164, "cpu_usage_ratio": 1.07889467360833}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.35589e-05, "std": 1.1666235658705182e-06, "n": 10, "confidence_interval_95": [2.2724347775465782e-05, 2.4393452224534216e-05]}, "canonical": {"mean": 2.3659200000000005e-05, "std": 1.1154584309202695e-06, "n": 10, "confidence_interval_95": [2.286124910809827e-05, 2.445715089190174e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.20321303110576916, "p_value": 0.8434886547598853, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.19650588412130074, "p_value": 0.8464158356100556, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 22.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 49.0, "p_value": 0.9698499769931556, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.08788010297478502, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.995760634340975, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.4859375, "std": 0.16831863590952859, "n": 10, "confidence_interval_95": [0.36552960139558177, 0.6063453986044183]}, "canonical": {"mean": 0.444921875, "std": 0.23254092860052045, "n": 10, "confidence_interval_95": [0.2785721158006762, 0.6112716341993238]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.4196029637026726, "p_value": 0.6846142661628681, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.45182342126689773, "p_value": 0.6567932503986817, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 27.5, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 46.5, "p_value": 0.8186417657296253, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.2020615767558615, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0921861281826164, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 26.939999999999998, "std": 4.789154413881431, "n": 10, "confidence_interval_95": [23.514045316185374, 30.36595468381462]}, "canonical": {"mean": 24.970000000000002, "std": 5.238967243434318, "n": 10, "confidence_interval_95": [21.222268602162565, 28.71773139783744]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.681864122433305, "p_value": 0.5124884582306217, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.87765770963219, "p_value": 0.3916938125759082, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 17.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 55.5, "p_value": 0.6806986249804461, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.3925004599428698, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0788946736083298, "better_performance": true}}}}}, "HumanEval/0": {"task_id": "HumanEval/0", "entry_point": "has_close_elements", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.5354e-05, 2.3656000000000002e-05, 2.4233e-05, 2.4203000000000002e-05, 2.3789000000000002e-05, 2.4370000000000002e-05, 2.8521e-05, 2.3798e-05, 2.3522000000000003e-05, 2.4351e-05], "memory_usages": [0.53125, 0.53125, 0.52734375, 0.0078125, 0.0078125, 0.5390625, 0.52734375, 0.0078125, 1.5625, 0.5546875], "cpu_usages_list": [[99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 29.9], [99.8, 99.7, 99.8, 99.8, 89.8, 109.7, 99.8, 99.8, 99.8, 99.8, 109.7, 89.8, 99.8, 10.0], [89.8, 109.7, 89.8, 109.9, 99.8, 99.8, 99.9, 99.8, 99.9, 99.9, 99.8, 109.8, 89.8, 20.0], [99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 109.8, 89.8, 109.8, 89.8, 109.7, 89.8, 39.9], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 89.8, 109.7, 99.8, 99.8, 99.9, 109.8, 89.8, 10.0], [99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 10.0], [89.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 10.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 109.7, 89.9, 10.0], [99.7, 99.8, 99.8, 99.8, 99.8, 99.7, 109.8, 89.8, 99.8, 99.8, 109.7, 89.8, 99.8, 49.9], [99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 59.9]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.45797e-05, "std_execution_time": 1.479488654156488e-06, "avg_memory_usage": 0.4796875, "std_memory_usage": 0.4545438454339416, "avg_cpu_usage": 94.455, "std_cpu_usage": 1.395132654881444}, "evalperf_canonical_results": {"execution_times": [2.7324e-05, 2.3930000000000003e-05, 2.618e-05, 2.3820000000000002e-05, 2.4309e-05, 2.411e-05, 2.5947e-05, 2.4239000000000003e-05, 2.3868000000000003e-05, 2.5018e-05], "memory_usages": [0.54296875, 0.52734375, 0.53125, 0.5234375, 0.52734375, 0.52734375, 0.0078125, 0.52734375, 0.53125, 0.0078125], "cpu_usages_list": [[99.8, 99.8, 89.8, 99.8, 109.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 49.9], [99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 20.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 10.0], [99.7, 99.8, 99.9, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.7, 10.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.7, 89.9, 99.8, 99.8, 109.8, 89.8, 99.8, 30.0], [99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.7, 49.9], [99.8, 99.8, 99.8, 89.8, 99.8, 109.7, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 10.0], [99.8, 99.8, 99.9, 89.8, 109.9, 99.8, 89.8, 99.8, 109.7, 109.9, 89.8, 109.8, 89.8, 20.0], [89.8, 109.7, 89.8, 109.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 20.0], [99.8, 99.8, 89.8, 109.7, 89.8, 99.8, 109.8, 99.8, 99.9, 109.9, 89.8, 99.8, 99.8, 10.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.48745e-05, "std_execution_time": 1.2110799817609986e-06, "avg_memory_usage": 0.425390625, "std_memory_usage": 0.22014413446968387, "avg_cpu_usage": 94.39142857142858, "std_cpu_usage": 1.0721732861601958}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.9881485054975979, "memory_usage_ratio": 1.1276400367309458, "cpu_usage_ratio": 1.0006734873021157}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.45797e-05, "std": 1.479488654156488e-06, "n": 10, "confidence_interval_95": [2.3521337573917764e-05, 2.563806242608224e-05]}, "canonical": {"mean": 2.4874500000000004e-05, "std": 1.2110799817609986e-06, "n": 10, "confidence_interval_95": [2.400814557134304e-05, 2.5740854428656968e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.7282783512126697, "p_value": 0.4849595910415445, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.4875824459895342, "p_value": 0.6317311403956551, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 15.0, "p_value": 0.232421875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 37.0, "p_value": 0.3447042220069576, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.21805349877364363, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9881485054975978, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.4796875, "std": 0.4545438454339416, "n": 10, "confidence_interval_95": [0.1545264210943333, 0.8048485789056667]}, "canonical": {"mean": 0.425390625, "std": 0.22014413446968387, "n": 10, "confidence_interval_95": [0.2679089980942859, 0.5828722519057141]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.3318787997142727, "p_value": 0.7475789314053066, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.33997119666298803, "p_value": 0.7378132403212903, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 24.0, "p_value": 0.759765625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 58.0, "p_value": 0.561018010120568, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.15203974122607816, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.1276400367309458, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 94.45499999999998, "std": 1.395132654881449, "n": 10, "confidence_interval_95": [93.45698222056069, 95.45301777943928]}, "canonical": {"mean": 94.39142857142858, "std": 1.0721732861602005, "n": 10, "confidence_interval_95": [93.62444200675766, 95.15841513609949]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.11009856209096411, "p_value": 0.9147471665233073, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.11425230068744907, "p_value": 0.9103025638876774, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 26.0, "p_value": 0.921875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 44.0, "p_value": 0.6772412568365533, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.05109518218457642, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0006734873021157, "better_performance": true}}}}}, "HumanEval/4": {"task_id": "HumanEval/4", "entry_point": "mean_absolute_deviation", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.3922000000000002e-05, 2.4934000000000002e-05, 2.3553000000000003e-05, 2.4027000000000002e-05, 2.2294e-05, 2.4772000000000003e-05, 2.3428e-05, 2.2987000000000002e-05, 2.3903e-05, 4.8209000000000005e-05], "memory_usages": [0.5390625, 0.5390625, 0.52734375, 0.5390625, 0.5234375, 0.5390625, 0.0078125, 0.52734375, 0.52734375, 0.52734375], "cpu_usages_list": [[79.9], [89.8], [79.8], [89.8], [89.8], [89.8], [79.8], [79.8], [79.8], [89.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.6202900000000003e-05, "std_execution_time": 7.771001343313345e-06, "avg_memory_usage": 0.4796875, "std_memory_usage": 0.1659203668702583, "avg_cpu_usage": 84.81, "std_cpu_usage": 5.260006337131797}, "evalperf_canonical_results": {"execution_times": [2.3120000000000002e-05, 2.1824e-05, 2.3983e-05, 2.2761e-05, 2.2291e-05, 2.3459e-05, 2.1553000000000002e-05, 2.2550000000000003e-05, 2.2919000000000002e-05, 2.23e-05], "memory_usages": [0.5390625, 0.52734375, 0.0078125, 0.0078125, 0.5390625, 0.5234375, 0.5390625, 0.5546875, 0.52734375, 0.52734375], "cpu_usages_list": [[89.8], [89.8], [89.8], [79.8], [79.9], [89.8], [79.8], [89.8], [79.8], [79.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.2676e-05, "std_execution_time": 7.361504978980559e-07, "avg_memory_usage": 0.429296875, "std_memory_usage": 0.22232820314877863, "avg_cpu_usage": 84.81, "std_cpu_usage": 5.260006337131797}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.1555344857999648, "memory_usage_ratio": 1.1173794358507734, "cpu_usage_ratio": 1.0}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.62029e-05, "std": 7.771001343313345e-06, "n": 10, "confidence_interval_95": [2.064386052261613e-05, 3.1761939477383864e-05]}, "canonical": {"mean": 2.2676e-05, "std": 7.361504978980559e-07, "n": 10, "confidence_interval_95": [2.2149389657481873e-05, 2.320261034251813e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.4071525163595426, "p_value": 0.19296543529290056, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.4288156707333184, "p_value": 0.17018166185701583, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 2.0, "p_value": 0.005859375, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 85.0, "p_value": 0.009108496398030965, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.6389857934153313, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.1555344857999645, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.4796875, "std": 0.16592036687025832, "n": 10, "confidence_interval_95": [0.36099521971524173, 0.5983797802847582]}, "canonical": {"mean": 0.429296875, "std": 0.22232820314877863, "n": 10, "confidence_interval_95": [0.27025285948154265, 0.5883408905184574]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.5310500932533879, "p_value": 0.6082380393754454, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.5744059288199428, "p_value": 0.5728024975508291, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 13.0, "p_value": 0.921875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 51.5, "p_value": 0.93690962080932, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.2568821407040595, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.1173794358507734, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 84.80999999999999, "std": 5.260006337131796, "n": 10, "confidence_interval_95": [81.04721814119033, 88.57278185880965]}, "canonical": {"mean": 84.80999999999999, "std": 5.260006337131796, "n": 10, "confidence_interval_95": [81.04721814119033, 88.57278185880965]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 10.5, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 50.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.0, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0, "better_performance": false}}}}}, "HumanEval/16": {"task_id": "HumanEval/16", "entry_point": "count_distinct_characters", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.1106e-05, 2.1497e-05, 2.1843e-05, 2.3172e-05, 2.1286000000000003e-05, 2.0674000000000002e-05, 2.0962e-05, 2.1777000000000002e-05, 2.2451000000000002e-05, 2.1400000000000002e-05], "memory_usages": [0.53125, 0.0078125, 0.0078125, 0.52734375, 0.55859375, 0.54296875, 0.55078125, 0.0078125, 0.5546875, 0.52734375], "cpu_usages_list": [[20.0], [20.0], [20.0], [20.0], [10.0], [20.0], [29.9], [20.0], [20.0], [20.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.1616800000000003e-05, "std_execution_time": 7.409229679558088e-07, "avg_memory_usage": 0.381640625, "std_memory_usage": 0.2581939649472006, "avg_cpu_usage": 19.99, "std_cpu_usage": 4.690522358970267}, "evalperf_canonical_results": {"execution_times": [2.1715e-05, 2.3024000000000002e-05, 2.1472e-05, 2.162e-05, 2.0828e-05, 2.099e-05, 2.0566000000000003e-05, 2.2521e-05, 2.5052e-05, 2.1023000000000002e-05], "memory_usages": [0.5546875, 0.0078125, 0.54296875, 0.54296875, 0.5703125, 0.53515625, 0.5390625, 0.52734375, 0.52734375, 0.5546875], "cpu_usages_list": [[10.0], [10.0], [10.0], [], [], [10.0], [10.0], [10.0], [], [10.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.18811e-05, "std_execution_time": 1.3499928765244154e-06, "avg_memory_usage": 0.490234375, "std_memory_usage": 0.1700228686939091, "avg_cpu_usage": 7.0, "std_cpu_usage": 4.83045891539648}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.9879210825781155, "memory_usage_ratio": 0.7784860557768924, "cpu_usage_ratio": 2.8557142857142854}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.16168e-05, "std": 7.409229679558088e-07, "n": 10, "confidence_interval_95": [2.1086775638067433e-05, 2.2146824361932566e-05]}, "canonical": {"mean": 2.18811e-05, "std": 1.3499928765244154e-06, "n": 10, "confidence_interval_95": [2.0915373272743195e-05, 2.2846826727256804e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.70982442190942, "p_value": 0.4957911665250929, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.542738207158691, "p_value": 0.5939673031259269, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 23.0, "p_value": 0.6953125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 50.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.24271990503863922, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9879210825781154, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.381640625, "std": 0.25819396494720054, "n": 10, "confidence_interval_95": [0.19693978909050872, 0.5663414609094913]}, "canonical": {"mean": 0.490234375, "std": 0.1700228686939091, "n": 10, "confidence_interval_95": [0.3686073417038634, 0.6118614082961366]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.5513332739247425, "p_value": 0.1552360636620396, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.1108098494205723, "p_value": 0.281268698430193, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 10.0, "p_value": 0.16015625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 38.5, "p_value": 0.4006755313537319, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.4967692666761411, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.7784860557768924, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 19.990000000000002, "std": 4.690522358970267, "n": 10, "confidence_interval_95": [16.6346024378179, 23.345397562182104]}, "canonical": {"mean": 10.0, "std": 0.0, "n": 7, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": null, "independent_t_test": {"t_statistic": 5.579473405159245, "p_value": 5.2627532949568764e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": null, "mann_whitney_u": {"u_statistic": 66.5, "p_value": 0.0006865830827751258, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.7495945133287987, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.999, "better_performance": true}}}}}, "HumanEval/20": {"task_id": "HumanEval/20", "entry_point": "find_closest_elements", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.1848e-05, 2.1481e-05, 2.2635000000000003e-05, 2.2153000000000003e-05, 2.0573e-05, 2.254e-05, 2.1421e-05, 2.2777000000000002e-05, 2.2026000000000002e-05, 2.2661000000000002e-05], "memory_usages": [0.55078125, 0.5546875, 0.5546875, 0.5390625, 0.56640625, 0.54296875, 0.56640625, 0.53515625, 0.0078125, 0.53125], "cpu_usages_list": [[89.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.7, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.7, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 109.7, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 59.9], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.9, 99.8, 99.8, 99.8, 99.8, 29.9], [99.9, 99.8, 99.8, 109.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 109.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 79.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 109.7, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.9, 99.8, 99.8, 99.8, 99.8, 99.7, 99.9, 99.7, 99.9, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 109.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.9, 89.9], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.9, 99.9, 99.9, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.9, 99.7, 99.9, 99.8, 99.8, 99.8, 99.8, 109.7, 99.9, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.7, 99.9, 99.9, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.9, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.9, 99.8, 99.9, 99.8, 109.9, 99.8, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 69.9], [99.8, 99.8, 99.9, 100.0, 99.8, 99.9, 99.9, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 109.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.9, 99.9, 99.9, 99.7, 99.8, 99.9, 99.8, 99.7, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.8, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 20.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.9, 99.9, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 109.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 10.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 109.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.9, 109.8, 99.9, 99.8, 99.8, 99.7, 99.7, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.7, 99.7, 99.9, 59.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 109.8, 99.9, 99.9, 99.9, 99.8, 99.9, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.7, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 49.9], [99.8, 99.8, 99.9, 99.9, 99.9, 99.8, 99.9, 99.9, 99.8, 99.8, 99.7, 99.8, 99.8, 99.7, 99.7, 99.7, 99.7, 109.7, 99.7, 99.8, 99.8, 99.8, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.8, 99.9, 99.7, 99.8, 99.8, 99.8, 99.7, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.7, 99.7, 99.8, 109.7, 99.8, 99.8, 99.7, 99.7, 99.7, 99.8, 99.7, 99.8, 99.7, 99.8, 99.7, 99.9, 99.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.8, 109.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.20115e-05, "std_execution_time": 7.009733312410052e-07, "avg_memory_usage": 0.494921875, "std_memory_usage": 0.1715793108352054, "avg_cpu_usage": 99.66918799749638, "std_cpu_usage": 0.2604389463014535}, "evalperf_canonical_results": {"execution_times": [2.2295000000000003e-05, 2.6317000000000002e-05, 2.5806e-05, 2.6130000000000002e-05, 2.3632e-05, 2.3983e-05, 2.3547e-05, 2.2102000000000003e-05, 2.2017000000000003e-05, 2.2868000000000002e-05], "memory_usages": [0.5390625, 0.0078125, 0.5390625, 0.0078125, 0.6328125, 0.56640625, 0.5234375, 0.53125, 0.5390625, 0.52734375], "cpu_usages_list": [[20.0], [29.9], [20.0], [29.9], [20.0], [29.9], [20.0], [20.0], [20.0], [10.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3869700000000003e-05, "std_execution_time": 1.6687090186395254e-06, "avg_memory_usage": 0.44140625, "std_memory_usage": 0.23071505788315547, "avg_cpu_usage": 21.97, "std_cpu_usage": 6.2824358333372565}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.9221523521451882, "memory_usage_ratio": 1.1212389380530974, "cpu_usage_ratio": 4.53660391431481}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.20115e-05, "std": 7.009733312410052e-07, "n": 10, "confidence_interval_95": [2.1510053886583095e-05, 2.2512946113416907e-05]}, "canonical": {"mean": 2.38697e-05, "std": 1.6687090186395252e-06, "n": 10, "confidence_interval_95": [2.267597747943119e-05, 2.506342252056881e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -3.1465519586538413, "p_value": 0.011802869486535522, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -3.2465612300315843, "p_value": 0.00447921318761359, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.01953125, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 15.0, "p_value": 0.009108496398030965, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -1.4519063206931908, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9221523521451883, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.494921875, "std": 0.1715793108352054, "n": 10, "confidence_interval_95": [0.3721814300693158, 0.6176623199306842]}, "canonical": {"mean": 0.44140625, "std": 0.23071505788315547, "n": 10, "confidence_interval_95": [0.2763626400277851, 0.6064498599722149]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.5532470419975803, "p_value": 0.5935688932836083, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.5885854582013978, "p_value": 0.5634530701544216, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 20.5, "p_value": 0.5078125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 66.0, "p_value": 0.23776159060431246, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.26322341902123736, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.1212389380530974, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 99.66918799749635, "std": 0.26043894630145115, "n": 10, "confidence_interval_95": [99.48288119867128, 99.85549479632142]}, "canonical": {"mean": 21.97, "std": 6.2824358333372565, "n": 10, "confidence_interval_95": [17.47581614019358, 26.46418385980642]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 38.404521352106855, "p_value": 2.7328814298658218e-11, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 39.076491732962296, "p_value": 7.374467582427836e-19, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.001953125, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 100.0, "p_value": 0.00014589964277689183, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 17.47553836742245, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 4.536603914314809, "better_performance": true}}}}}, "HumanEval/24": {"task_id": "HumanEval/24", "entry_point": "largest_divisor", "evalperf_canonical_score": 100.00000000000001, "generated_code_results": {"execution_times": [2.1285e-05, 2.1229000000000003e-05, 2.2205000000000002e-05, 2.1503e-05, 2.0366e-05, 2.0213e-05, 2.1849e-05, 2.1854e-05, 2.1706000000000002e-05, 2.0264e-05], "memory_usages": [0.546875, 0.5625, 0.5703125, 0.5546875, 0.55859375, 0.0078125, 0.6328125, 0.5625, 0.5390625, 0.0078125], "cpu_usages_list": [[99.9, 99.9, 99.8, 99.8, 99.8, 99.7, 99.7, 79.8], [99.8, 99.7, 99.7, 99.7, 99.8, 99.7, 99.7, 89.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 10.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 89.8], [99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 89.8], [99.8, 99.8, 109.7, 99.7, 99.8, 99.8, 99.8, 89.8], [99.9, 109.7, 99.9, 99.9, 99.8, 99.9, 99.8, 79.8], [99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.1247400000000003e-05, "std_execution_time": 7.255807176171221e-07, "avg_memory_usage": 0.454296875, "std_memory_usage": 0.23667885332565125, "avg_cpu_usage": 98.05222222222223, "std_cpu_usage": 3.0101107912016536}, "evalperf_canonical_results": {"execution_times": [2.0497e-05, 2.1916e-05, 2.1972000000000002e-05, 2.0541000000000002e-05, 2.0546000000000003e-05, 2.0348000000000002e-05, 2.142e-05, 2.1114e-05, 2.1000000000000002e-05, 3.0386000000000004e-05], "memory_usages": [0.54296875, 0.0078125, 0.54296875, 0.5546875, 0.53125, 0.0078125, 0.0078125, 0.0078125, 0.52734375, 0.0078125], "cpu_usages_list": [[], [], [], [], [10.0], [], [10.0], [], [10.0], [10.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.1974000000000003e-05, "std_execution_time": 3.012536435925345e-06, "avg_memory_usage": 0.273828125, "std_memory_usage": 0.28049818738660354, "avg_cpu_usage": 4.0, "std_cpu_usage": 5.163977794943222}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.9669336488577409, "memory_usage_ratio": 1.6590584878744652, "cpu_usage_ratio": 24.513055555555557}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.1247400000000003e-05, "std": 7.255807176171221e-07, "n": 10, "confidence_interval_95": [2.0728350822800618e-05, 2.1766449177199387e-05]}, "canonical": {"mean": 2.1974e-05, "std": 3.0125364359253455e-06, "n": 10, "confidence_interval_95": [1.9818961256019304e-05, 2.4129038743980696e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.6874980856310333, "p_value": 0.509096464149959, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.7415118396238569, "p_value": 0.46794830237893315, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 18.0, "p_value": 0.375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 50.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.3316141759039732, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9669336488577411, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.454296875, "std": 0.23667885332565125, "n": 10, "confidence_interval_95": [0.284987022772094, 0.623606727227906]}, "canonical": {"mean": 0.273828125, "std": 0.28049818738660354, "n": 10, "confidence_interval_95": [0.07317180953578165, 0.47448444046421834]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.073243338998257, "p_value": 0.06800575772029419, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.5549788238342783, "p_value": 0.13735767318720382, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.015625, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 80.5, "p_value": 0.020350331290676295, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.6954076707332234, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.6590584878744652, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 98.05222222222221, "std": 3.0101107912016527, "n": 10, "confidence_interval_95": [95.89891867994608, 100.20552576449835]}, "canonical": {"mean": 10.0, "std": 0.0, "n": 4, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": null, "independent_t_test": {"t_statistic": 57.09435701909385, "p_value": 5.500348134094784e-16, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": null, "mann_whitney_u": {"u_statistic": 40.0, "p_value": 0.005294786333970273, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 33.77747712897328, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 9.805222222222222, "better_performance": true}}}}}, "HumanEval/31": {"task_id": "HumanEval/31", "entry_point": "is_prime", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.1995000000000002e-05, 2.0839e-05, 2.0205e-05, 2.2087e-05, 2.1917e-05, 2.1343000000000002e-05, 2.1451000000000002e-05, 2.1343000000000002e-05, 2.0213e-05, 2.1442000000000002e-05], "memory_usages": [0.5234375, 0.52734375, 0.52734375, 0.5390625, 0.5390625, 0.52734375, 0.5234375, 0.54296875, 0.5546875, 0.52734375], "cpu_usages_list": [[99.8, 109.8, 99.9, 99.9, 99.9, 99.9, 69.9], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8], [99.8, 99.8, 109.7, 99.9, 99.9, 99.8, 69.9], [99.8, 99.9, 99.8, 99.8, 99.8, 99.9, 79.8], [99.9, 99.8, 99.8, 99.8, 99.7, 99.8, 79.8], [99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 69.9], [99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 69.8], [99.8, 99.8, 99.9, 99.8, 99.8, 109.9, 69.9], [89.9, 99.8, 99.8, 99.8, 99.8, 99.8, 89.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.12835e-05, "std_execution_time": 6.766041432526605e-07, "avg_memory_usage": 0.533203125, "std_memory_usage": 0.010293872591693943, "avg_cpu_usage": 97.24571428571429, "std_cpu_usage": 1.4734967742632379}, "evalperf_canonical_results": {"execution_times": [2.0794e-05, 2.1454e-05, 2.1699e-05, 2.0282e-05, 2.258e-05, 2.2325e-05, 2.1878000000000003e-05, 2.2731000000000003e-05, 2.2224000000000003e-05, 2.2760000000000002e-05], "memory_usages": [0.53515625, 0.5390625, 0.0078125, 0.5546875, 0.55078125, 0.5234375, 0.0078125, 0.0078125, 0.5546875, 0.55859375], "cpu_usages_list": [[10.0], [10.0], [], [], [10.0], [10.0], [], [], [10.0], [10.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.18727e-05, "std_execution_time": 8.334446658963701e-07, "avg_memory_usage": 0.383984375, "std_memory_usage": 0.25979776717392905, "avg_cpu_usage": 6.0, "std_cpu_usage": 5.163977794943222}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.9730623105515094, "memory_usage_ratio": 1.3886063072227874, "cpu_usage_ratio": 16.207619047619048}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.1283500000000004e-05, "std": 6.766041432526605e-07, "n": 10, "confidence_interval_95": [2.079948655350389e-05, 2.176751344649612e-05]}, "canonical": {"mean": 2.18727e-05, "std": 8.334446658963701e-07, "n": 10, "confidence_interval_95": [2.1276489602491888e-05, 2.2468910397508115e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.5424100446681885, "p_value": 0.15736656922976117, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.735628392636151, "p_value": 0.09971655527882829, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 14.0, "p_value": 0.193359375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.0639221346034546, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.7761966139226258, "interpretation": "medium", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9730623105515095, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.533203125, "std": 0.010293872591693943, "n": 10, "confidence_interval_95": [0.5258393321521676, 0.5405669178478324]}, "canonical": {"mean": 0.383984375, "std": 0.259797767173929, "n": 10, "confidence_interval_95": [0.19813624809177902, 0.569832501908221]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.825785418792654, "p_value": 0.1011702667672325, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.8148775968374384, "p_value": 0.08624348467041928, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 20.0, "p_value": 0.8203125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 48.0, "p_value": 0.908830208018479, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.811637935473994, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.3886063072227874, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 97.24571428571429, "std": 1.4734967742632354, "n": 10, "confidence_interval_95": [96.19163819229354, 98.29979037913503]}, "canonical": {"mean": 10.0, "std": 0.0, "n": 6, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": null, "independent_t_test": {"t_statistic": 143.00568747844244, "p_value": 1.469355323702336e-23, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": null, "mann_whitney_u": {"u_statistic": 60.0, "p_value": 0.0010001050404196995, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 73.84781946892667, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 9.724571428571428, "better_performance": true}}}}}, "HumanEval/34": {"task_id": "HumanEval/34", "entry_point": "unique", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.5268000000000003e-05, 2.2820000000000002e-05, 2.3314e-05, 2.3145000000000003e-05, 2.3347000000000002e-05, 2.3805e-05, 2.1980000000000003e-05, 2.3055000000000002e-05, 2.2883e-05, 2.3472e-05], "memory_usages": [0.52734375, 0.54296875, 0.5390625, 0.0078125, 0.0078125, 0.54296875, 0.5390625, 0.0078125, 0.5546875, 0.5234375], "cpu_usages_list": [[99.8], [99.8, 10.0], [89.8, 10.0], [99.8], [89.8, 10.0], [89.8, 20.0], [99.8], [99.8, 10.0], [89.8, 20.0], [99.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3308900000000003e-05, "std_execution_time": 8.42613981739101e-07, "avg_memory_usage": 0.379296875, "std_memory_usage": 0.2564902567162437, "avg_cpu_usage": 71.86, "std_cpu_usage": 24.123810275788156}, "evalperf_canonical_results": {"execution_times": [2.3148000000000003e-05, 2.4486000000000002e-05, 2.5341e-05, 5.0326000000000006e-05, 2.5028e-05, 2.3717e-05, 2.3494000000000002e-05, 2.5743e-05, 2.4056e-05, 2.205e-05], "memory_usages": [0.52734375, 0.0078125, 0.5234375, 0.52734375, 0.53515625, 0.52734375, 0.5234375, 0.0078125, 0.53515625, 0.52734375], "cpu_usages_list": [[99.8], [99.8, 10.0], [99.8], [99.8], [99.8], [89.9], [99.8, 10.0], [99.8, 10.0], [89.9, 10.0], [99.8, 10.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.6738900000000002e-05, "std_execution_time": 8.360220085486852e-06, "avg_memory_usage": 0.42421875, "std_memory_usage": 0.2195020554431097, "avg_cpu_usage": 75.865, "std_cpu_usage": 23.376698393438236}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.8717224717546347, "memory_usage_ratio": 0.8941068139963168, "cpu_usage_ratio": 0.9472088578395835}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.33089e-05, "std": 8.42613981739101e-07, "n": 10, "confidence_interval_95": [2.27061302690806e-05, 2.39116697309194e-05]}, "canonical": {"mean": 2.6738900000000002e-05, "std": 8.360220085486852e-06, "n": 10, "confidence_interval_95": [2.0758358826264553e-05, 3.271944117373545e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.2780040185896628, "p_value": 0.23322394361086807, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.2908675597289923, "p_value": 0.21308374628633908, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 12.0, "p_value": 0.130859375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 21.0, "p_value": 0.031209012771740218, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.5772935227006594, "interpretation": "medium", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.8717224717546346, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.379296875, "std": 0.2564902567162437, "n": 10, "confidence_interval_95": [0.19581479853931283, 0.5627789514606871]}, "canonical": {"mean": 0.42421875, "std": 0.2195020554431097, "n": 10, "confidence_interval_95": [0.26719643876013593, 0.581241061239864]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.4719632902374718, "p_value": 0.6481860314150875, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.4207902240062314, "p_value": 0.6788895364037955, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 14.0, "p_value": 0.640625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 62.0, "p_value": 0.3761038342138946, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.18818310902905946, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.8941068139963168, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 71.85999999999999, "std": 24.123810275788156, "n": 10, "confidence_interval_95": [54.60286572046245, 89.11713427953752]}, "canonical": {"mean": 75.865, "std": 23.376698393438236, "n": 10, "confidence_interval_95": [59.14231736504742, 92.58768263495257]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.37804067642518957, "p_value": 0.7141624994121804, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.37702100379042247, "p_value": 0.7105660530558227, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 8.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 44.0, "p_value": 0.6563246673603835, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.16860891868411812, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9472088578395833, "better_performance": false}}}}}, "HumanEval/37": {"task_id": "HumanEval/37", "entry_point": "sort_even", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.2506e-05, 2.2275000000000003e-05, 2.2668e-05, 2.2111000000000002e-05, 3.6019e-05, 2.2307000000000002e-05, 2.305e-05, 2.1846e-05, 2.2764e-05, 2.2134000000000002e-05], "memory_usages": [0.5546875, 0.54296875, 0.5234375, 0.0078125, 0.625, 0.5390625, 0.0078125, 0.5546875, 0.5390625, 0.5546875], "cpu_usages_list": [[20.0], [20.0], [10.0], [10.0], [10.0], [10.0], [20.0], [10.0], [10.0], [10.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3768e-05, "std_execution_time": 4.319033379511362e-06, "avg_memory_usage": 0.444921875, "std_memory_usage": 0.23194961795887348, "avg_cpu_usage": 13.0, "std_cpu_usage": 4.83045891539648}, "evalperf_canonical_results": {"execution_times": [2.1962e-05, 2.1607e-05, 2.3893000000000003e-05, 2.3092000000000002e-05, 2.2286e-05, 2.1149e-05, 2.0625e-05, 2.1987e-05, 2.1213000000000002e-05, 2.2954e-05], "memory_usages": [0.52734375, 0.55078125, 0.0078125, 0.5390625, 0.5390625, 0.52734375, 0.52734375, 0.0078125, 0.0078125, 0.56640625], "cpu_usages_list": [[10.0], [10.0], [10.0], [10.0], [20.0], [10.0], [20.0], [10.0], [10.0], [20.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.2076800000000002e-05, "std_execution_time": 1.00549046075369e-06, "avg_memory_usage": 0.380078125, "std_memory_usage": 0.2571675566260052, "avg_cpu_usage": 13.0, "std_cpu_usage": 4.83045891539648}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.0766053051166835, "memory_usage_ratio": 1.170606372045221, "cpu_usage_ratio": 1.0}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.3768e-05, "std": 4.319033379511362e-06, "n": 10, "confidence_interval_95": [2.0678349644772388e-05, 2.6857650355227612e-05]}, "canonical": {"mean": 2.2076800000000005e-05, "std": 1.00549046075369e-06, "n": 10, "confidence_interval_95": [2.1357515454994455e-05, 2.2796084545005555e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.2179894963665714, "p_value": 0.254194628871408, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.2060000550695407, "p_value": 0.2434310913887132, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 17.0, "p_value": 0.322265625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 68.0, "p_value": 0.18587673236587576, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.5393396208007964, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0766053051166833, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.444921875, "std": 0.23194961795887348, "n": 10, "confidence_interval_95": [0.2789951139517628, 0.6108486360482372]}, "canonical": {"mean": 0.380078125, "std": 0.2571675566260052, "n": 10, "confidence_interval_95": [0.19611153737143966, 0.5640447126285604]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.526389417869167, "p_value": 0.611342088695825, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.5920982062389764, "p_value": 0.561149291324877, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 19.0, "p_value": 0.41015625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 65.0, "p_value": 0.26609548612669087, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.2647943677012083, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.170606372045221, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 13.0, "std": 4.83045891539648, "n": 10, "confidence_interval_95": [9.544497855778182, 16.455502144221818]}, "canonical": {"mean": 13.0, "std": 4.83045891539648, "n": 10, "confidence_interval_95": [9.544497855778182, 16.455502144221818]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 50.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.0, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0, "better_performance": false}}}}}, "HumanEval/49": {"task_id": "HumanEval/49", "entry_point": "modp", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.1064e-05, 2.1131e-05, 2.1653e-05, 2.1791000000000002e-05, 2.1040000000000002e-05, 2.2545000000000002e-05, 2.1692000000000002e-05, 2.0297000000000002e-05, 2.0833000000000002e-05, 2.2013e-05], "memory_usages": [0.5703125, 0.53125, 0.55078125, 0.5390625, 0.53515625, 0.55078125, 0.625, 0.56640625, 0.0078125, 0.0078125], "cpu_usages_list": [[], [], [10.0], [10.0], [], [10.0], [], [10.0], [], []], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.14059e-05, "std_execution_time": 6.533294982880637e-07, "avg_memory_usage": 0.4484375, "std_memory_usage": 0.2337578680716794, "avg_cpu_usage": 4.0, "std_cpu_usage": 5.163977794943222}, "evalperf_canonical_results": {"execution_times": [2.0148e-05, 2.1115e-05, 2.0903000000000003e-05, 2.1385000000000003e-05, 2.1524000000000003e-05, 2.0205e-05, 2.0981e-05, 2.0791e-05, 2.1473000000000003e-05, 2.1040000000000002e-05], "memory_usages": [0.5546875, 0.52734375, 0.5546875, 0.5390625, 0.5546875, 0.0078125, 0.5546875, 0.56640625, 0.0078125, 0.52734375], "cpu_usages_list": [[10.0], [10.0], [10.0], [10.0], [], [], [], [], [10.0], []], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.0956500000000002e-05, "std_execution_time": 4.777042902140292e-07, "avg_memory_usage": 0.439453125, "std_memory_usage": 0.22784784164688143, "avg_cpu_usage": 5.0, "std_cpu_usage": 5.270462766947299}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.0214444205854984, "memory_usage_ratio": 1.0204444444444445, "cpu_usage_ratio": 0.8}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.1405900000000005e-05, "std": 6.533294982880637e-07, "n": 10, "confidence_interval_95": [2.093853623151374e-05, 2.187326376848627e-05]}, "canonical": {"mean": 2.0956500000000005e-05, "std": 4.777042902140293e-07, "n": 10, "confidence_interval_95": [2.061477093697514e-05, 2.129822906302487e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.571206337118791, "p_value": 0.15058320015870616, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.7558963862311279, "p_value": 0.09610758678975094, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 12.0, "p_value": 0.130859375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 73.5, "p_value": 0.08198366564857765, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.7852607362118057, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0214444205854984, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.4484375, "std": 0.2337578680716794, "n": 10, "confidence_interval_95": [0.28121719474581286, 0.6156578052541871]}, "canonical": {"mean": 0.439453125, "std": 0.22784784164688143, "n": 10, "confidence_interval_95": [0.27646059796336564, 0.6024456520366344]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.11294172551523358, "p_value": 0.9125558563380165, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.0870354349954187, "p_value": 0.9316042682617396, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 11.5, "p_value": 0.71875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 54.0, "p_value": 0.7894729523191806, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.038923429820204074, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0204444444444445, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 10.0, "std": 0.0, "n": 4, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 10.0, "std": 0.0, "n": 5, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": null, "independent_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": null, "mann_whitney_u": {"u_statistic": 10.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": NaN, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0, "better_performance": false}}}}}, "HumanEval/51": {"task_id": "HumanEval/51", "entry_point": "remove_vowels", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.247e-05, 2.1421e-05, 2.2749000000000002e-05, 2.0980000000000002e-05, 2.2067e-05, 4.0015e-05, 2.1778e-05, 2.2729000000000002e-05, 2.1901000000000003e-05, 2.2009000000000002e-05], "memory_usages": [0.5546875, 0.55078125, 0.52734375, 0.56640625, 0.52734375, 0.53515625, 0.0078125, 0.52734375, 0.5546875, 0.54296875], "cpu_usages_list": [[99.8, 20.0], [99.9, 10.0], [99.8, 20.0], [99.8, 10.0], [99.8, 10.0], [99.8, 20.0], [99.8, 20.0], [99.8, 10.0], [99.9, 20.0], [99.8, 20.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.38119e-05, "std_execution_time": 5.7199752029377035e-06, "avg_memory_usage": 0.489453125, "std_memory_usage": 0.16978138149446817, "avg_cpu_usage": 57.91, "std_cpu_usage": 2.5799224794555355}, "evalperf_canonical_results": {"execution_times": [2.0646000000000002e-05, 2.1875e-05, 2.1439000000000003e-05, 2.1510000000000002e-05, 2.143e-05, 2.2057000000000002e-05, 2.1214e-05, 2.2625e-05, 2.1312000000000002e-05, 2.0753000000000003e-05], "memory_usages": [0.5390625, 0.0078125, 0.53515625, 0.0078125, 0.5234375, 0.0078125, 0.54296875, 0.5546875, 0.54296875, 0.53125], "cpu_usages_list": [[20.0], [20.0], [20.0], [20.0], [20.0], [20.0], [10.0], [29.9], [20.0], [20.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.14861e-05, "std_execution_time": 5.895151397546967e-07, "avg_memory_usage": 0.379296875, "std_memory_usage": 0.2564770362163771, "avg_cpu_usage": 19.99, "std_cpu_usage": 4.690522358970267}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.108246726953705, "memory_usage_ratio": 1.290422245108136, "cpu_usage_ratio": 2.8969484742371185}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.38119e-05, "std": 5.7199752029377035e-06, "n": 10, "confidence_interval_95": [1.972007623649646e-05, 2.7903723763503543e-05]}, "canonical": {"mean": 2.1486100000000004e-05, "std": 5.895151397546967e-07, "n": 10, "confidence_interval_95": [2.10643862735918e-05, 2.1907813726408208e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.3267188118589615, "p_value": 0.21727712749233832, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.2790392461159474, "p_value": 0.21712716260817153, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.01953125, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 77.0, "p_value": 0.04515456962427901, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.5720037400410685, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.1082467269537049, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.489453125, "std": 0.16978138149446817, "n": 10, "confidence_interval_95": [0.36799884123969123, 0.6109074087603088]}, "canonical": {"mean": 0.379296875, "std": 0.2564770362163771, "n": 10, "confidence_interval_95": [0.19582425591519304, 0.5627694940848069]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.019206096402384, "p_value": 0.33471938946245033, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.1325291056877236, "p_value": 0.2722734860458104, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 16.0, "p_value": 0.265625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 65.0, "p_value": 0.2688567262531305, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.5064824133629587, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.290422245108136, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 57.910000000000004, "std": 2.579922479455536, "n": 10, "confidence_interval_95": [56.064434637406926, 59.75556536259308]}, "canonical": {"mean": 19.990000000000002, "std": 4.690522358970267, "n": 10, "confidence_interval_95": [16.6346024378179, 23.345397562182104]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 19.03120950415165, "p_value": 1.4062324759143958e-08, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 22.400255234115352, "p_value": 1.349874095355696e-14, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.001953125, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 100.0, "p_value": 9.473334623447104e-05, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 10.017698683365479, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.8969484742371185, "better_performance": true}}}}}, "HumanEval/54": {"task_id": "HumanEval/54", "entry_point": "same_chars", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.2343000000000003e-05, 2.2317e-05, 2.2642e-05, 2.1785000000000002e-05, 2.2433e-05, 2.2278000000000003e-05, 2.4357e-05, 2.1478e-05, 2.1149e-05, 2.1823e-05], "memory_usages": [0.0078125, 0.53125, 0.52734375, 0.5625, 0.53515625, 0.51953125, 0.5390625, 0.5390625, 0.5390625, 0.52734375], "cpu_usages_list": [[40.0], [39.9], [49.9], [39.9], [39.9], [49.9], [39.9], [39.9], [30.0], [39.9]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.22605e-05, "std_execution_time": 8.721722880257089e-07, "avg_memory_usage": 0.4828125, "std_memory_usage": 0.16728806684586853, "avg_cpu_usage": 40.92, "std_cpu_usage": 5.653081755408578}, "evalperf_canonical_results": {"execution_times": [2.2336e-05, 2.2278000000000003e-05, 2.2572e-05, 2.1956e-05, 2.1802000000000002e-05, 2.201e-05, 2.2674e-05, 2.3164e-05, 2.2575e-05, 2.2196000000000002e-05], "memory_usages": [0.53515625, 0.55859375, 0.5625, 0.5546875, 0.55078125, 0.52734375, 0.52734375, 0.5390625, 0.5390625, 0.53125], "cpu_usages_list": [[20.0], [20.0], [20.0], [10.0], [20.0], [10.0], [20.0], [20.0], [20.0], [20.0]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.23563e-05, "std_execution_time": 4.0377662966876394e-07, "avg_memory_usage": 0.542578125, "std_memory_usage": 0.013079295837341338, "avg_cpu_usage": 18.0, "std_cpu_usage": 4.216370213557839}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.9957148544258217, "memory_usage_ratio": 0.8898488120950323, "cpu_usage_ratio": 2.2733333333333334}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.22605e-05, "std": 8.721722880257089e-07, "n": 10, "confidence_interval_95": [2.163658553054916e-05, 2.2884414469450842e-05]}, "canonical": {"mean": 2.23563e-05, "std": 4.0377662966876394e-07, "n": 10, "confidence_interval_95": [2.2067455599489756e-05, 2.2645144400510247e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.3161945859681739, "p_value": 0.7590650205573666, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.31520661270922196, "p_value": 0.7562297574443541, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 27.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 40.5, "p_value": 0.4961297149428171, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.14096468259505388, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9957148544258217, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.4828125, "std": 0.16728806684586855, "n": 10, "confidence_interval_95": [0.36314182609236884, 0.6024831739076311]}, "canonical": {"mean": 0.542578125, "std": 0.013079295837341338, "n": 10, "confidence_interval_95": [0.5332217603972933, 0.5519344896027067]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.1457790433114163, "p_value": 0.28143794446168646, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.1263235637202231, "p_value": 0.27482151373928565, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 6.5, "p_value": 0.1171875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 32.5, "p_value": 0.19314727823197486, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.5037072106276469, "interpretation": "medium", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.8898488120950323, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 40.919999999999995, "std": 5.653081755408578, "n": 10, "confidence_interval_95": [36.87602892615178, 44.96397107384821]}, "canonical": {"mean": 18.0, "std": 4.216370213557839, "n": 10, "confidence_interval_95": [14.983790449527868, 21.016209550472134]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 8.826882720795744, "p_value": 1.0005550537109839e-05, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 10.27739543952178, "p_value": 5.852111780562122e-09, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.001953125, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 100.0, "p_value": 8.686273303412756e-05, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 4.596190966883406, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.273333333333333, "better_performance": true}}}}}, "HumanEval/58": {"task_id": "HumanEval/58", "entry_point": "common", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"], "timeout_due_to_poor_performance": true}, "evalperf_canonical_results": {"execution_times": [2.5621e-05, 2.2998000000000002e-05, 2.3906e-05, 2.3232e-05, 2.4801000000000002e-05, 2.6124000000000003e-05, 2.3234e-05, 2.3339e-05, 2.6352e-05, 2.5435000000000003e-05], "memory_usages": [0.5234375, 0.52734375, 0.51953125, 0.54296875, 0.0078125, 0.55859375, 0.5390625, 0.53125, 0.0078125, 0.5546875], "cpu_usages_list": [[89.9], [99.8], [99.8], [99.8], [99.9], [89.8, 10.0], [89.9], [89.8], [99.8], [99.9]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.45042e-05, "std_execution_time": 1.31046690576722e-06, "avg_memory_usage": 0.43125, "std_memory_usage": 0.2235279294474307, "avg_cpu_usage": 91.85, "std_cpu_usage": 15.470491624738727}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {}}, "HumanEval/74": {"task_id": "HumanEval/74", "entry_point": "total_match", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.4398000000000003e-05, 2.5878000000000002e-05, 2.4892e-05, 2.3824e-05, 2.4522e-05, 2.4453e-05, 2.7838e-05, 2.6971000000000003e-05, 2.4275e-05, 2.3136e-05], "memory_usages": [0.55078125, 0.52734375, 0.5234375, 0.55859375, 0.5390625, 0.53125, 0.52734375, 0.5390625, 0.5390625, 0.52734375], "cpu_usages_list": [[99.8, 29.9], [99.8, 29.9], [99.8, 29.9], [89.8, 29.9], [99.8, 29.9], [99.8, 29.9], [99.8, 29.9], [99.8, 30.0], [99.8, 20.0], [89.8, 49.9]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.50187e-05, "std_execution_time": 1.4526347747761272e-06, "avg_memory_usage": 0.536328125, "std_memory_usage": 0.01135876483727312, "avg_cpu_usage": 64.36, "std_cpu_usage": 2.830469140541114}, "evalperf_canonical_results": {"execution_times": [2.3337e-05, 2.4908000000000003e-05, 2.2062e-05, 2.4732000000000003e-05, 2.3457000000000003e-05, 2.5338e-05, 2.4625000000000002e-05, 2.3723000000000003e-05, 2.2389000000000002e-05, 2.2629000000000003e-05], "memory_usages": [0.0078125, 0.55859375, 0.52734375, 0.5546875, 0.5234375, 0.5390625, 0.5234375, 0.6328125, 0.0078125, 0.5234375], "cpu_usages_list": [[99.8, 39.9], [99.9, 49.9], [99.8, 39.9], [99.9, 40.0], [89.8, 49.9], [99.8, 39.9], [99.8, 39.9], [99.7, 39.9], [99.8, 39.9], [99.8, 39.9]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3720000000000003e-05, "std_execution_time": 1.144994420170781e-06, "avg_memory_usage": 0.43984375, "std_memory_usage": 0.23004684192391486, "avg_cpu_usage": 70.36, "std_cpu_usage": 1.5956190021430587}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.0547512647554804, "memory_usage_ratio": 1.219360568383659, "cpu_usage_ratio": 0.9147242751563388}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.50187e-05, "std": 1.4526347747761272e-06, "n": 10, "confidence_interval_95": [2.3979547681985083e-05, 2.605785231801492e-05]}, "canonical": {"mean": 2.372e-05, "std": 1.144994420170781e-06, "n": 10, "confidence_interval_95": [2.2900920334212717e-05, 2.4539079665787283e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.712032309792593, "p_value": 0.023917777925054045, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 2.2203536202077347, "p_value": 0.03947195526545318, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.01953125, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 71.0, "p_value": 0.12122450301291662, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.9929723257744493, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0547512647554806, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.536328125, "std": 0.01135876483727312, "n": 10, "confidence_interval_95": [0.5282025541301592, 0.5444536958698409]}, "canonical": {"mean": 0.43984375, "std": 0.23004684192391486, "n": 10, "confidence_interval_95": [0.2752781529289195, 0.6044093470710805]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.302204270183777, "p_value": 0.22518375685612946, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.3246829804303226, "p_value": 0.2018493292985175, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 22.5, "p_value": 0.642578125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 61.0, "p_value": 0.4217147987385874, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.592416238575845, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.219360568383659, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 64.35999999999999, "std": 2.830469140541114, "n": 10, "confidence_interval_95": [62.33520435312702, 66.38479564687296]}, "canonical": {"mean": 70.36, "std": 1.5956190021430587, "n": 10, "confidence_interval_95": [69.21856292749074, 71.50143707250926]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -5.966122431152328, "p_value": 0.00021110568586052571, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -5.839417978890518, "p_value": 1.568449333731845e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.00390625, "significant": true, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.5, "p_value": 0.0003639962350903039, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -2.611467109966726, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9147242751563386, "better_performance": false}}}}}, "HumanEval/85": {"task_id": "HumanEval/85", "entry_point": "add", "evalperf_canonical_score": 99.99999999999999, "generated_code_results": {"execution_times": [2.3298000000000003e-05, 2.4089000000000003e-05, 2.2818e-05, 2.4149000000000002e-05, 2.3839e-05, 2.3527e-05, 2.3519000000000003e-05, 2.3262000000000002e-05, 2.5023e-05, 2.4244e-05], "memory_usages": [0.54296875, 0.5546875, 0.5390625, 0.54296875, 0.53125, 0.55859375, 0.5234375, 0.5546875, 0.52734375, 0.0078125], "cpu_usages_list": [[99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 20.0], [89.8, 99.8, 99.8, 99.8, 99.8, 99.8, 79.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 89.9], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 10.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 89.8], [99.8, 99.8, 89.8, 99.8, 109.8, 99.8, 99.8, 49.9], [89.8, 99.8, 99.8, 99.8, 99.8, 99.8, 89.8], [99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.7], [99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 10.0], [89.8, 109.7, 99.8, 99.8, 89.8, 109.8, 89.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3776800000000002e-05, "std_execution_time": 6.283653926392401e-07, "avg_memory_usage": 0.48828125, "std_memory_usage": 0.16924078258894765, "avg_cpu_usage": 94.78839285714285, "std_cpu_usage": 4.377086824138067}, "evalperf_canonical_results": {"execution_times": [2.3254e-05, 2.3857000000000002e-05, 2.3214000000000002e-05, 2.4136e-05, 2.3352000000000003e-05, 2.3344000000000002e-05, 2.5829000000000003e-05, 2.1830000000000003e-05, 2.5604e-05, 2.2144e-05], "memory_usages": [0.52734375, 0.5234375, 0.52734375, 0.5390625, 0.0078125, 0.5390625, 0.53515625, 0.53125, 0.5390625, 0.5390625], "cpu_usages_list": [[89.8, 109.7, 99.9, 99.8, 99.8, 99.8, 79.8], [89.8, 109.7, 99.8, 99.8, 99.7, 99.8, 79.8], [99.8, 99.8, 89.8, 109.8, 99.8, 99.8, 79.8], [99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.9, 39.9], [99.8, 99.8, 89.8, 109.7, 99.9, 99.8, 89.9], [99.8, 99.8, 99.8, 99.8, 89.8, 109.7, 99.8, 10.0], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 10.0], [99.8, 99.8, 89.8, 109.7, 89.8, 109.7, 79.8], [99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 79.8], [99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 79.8]], "success_count": 10, "error_count": 0, "errors": [], "avg_execution_time": 2.3656400000000003e-05, "std_execution_time": 1.2890384185292712e-06, "avg_memory_usage": 0.480859375, "std_memory_usage": 0.16631483453437915, "avg_cpu_usage": 94.94357142857143, "std_cpu_usage": 3.7089092459622908}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 10, "errors": ["Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.0050895317968922, "memory_usage_ratio": 1.0154346060113728, "cpu_usage_ratio": 0.9983655705268543}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.37768e-05, "std": 6.283653926392401e-07, "n": 10, "confidence_interval_95": [2.3327294476891445e-05, 2.4226305523108553e-05]}, "canonical": {"mean": 2.3656400000000003e-05, "std": 1.2890384185292712e-06, "n": 10, "confidence_interval_95": [2.2734277465220802e-05, 2.4578522534779203e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.32342012730744185, "p_value": 0.7537655479137346, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.265500916846929, "p_value": 0.7936405131718971, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 22.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 59.0, "p_value": 0.5205228832757727, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.11873561963165048, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.005089531796892, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.48828125, "std": 0.16924078258894765, "n": 10, "confidence_interval_95": [0.3672136874001251, 0.609348812599875]}, "canonical": {"mean": 0.480859375, "std": 0.16631483453437917, "n": 10, "confidence_interval_95": [0.3618849095475238, 0.5998338404524762]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.09423753541492848, "p_value": 0.9269851557385214, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.09891168731350707, "p_value": 0.9223016073227607, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 16.0, "p_value": 0.2734375, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 67.5, "p_value": 0.1938331628161708, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.04423465132044107, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0154346060113728, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 94.78839285714285, "std": 4.377086824138067, "n": 10, "confidence_interval_95": [91.65721356938512, 97.91957214490058]}, "canonical": {"mean": 94.94357142857142, "std": 3.708909245962291, "n": 10, "confidence_interval_95": [92.29037758578829, 97.59676527135454]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.09426201442595235, "p_value": 0.926966251792422, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.08553335358768682, "p_value": 0.9327815992925969, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 26.0, "p_value": 0.921875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 54.5, "p_value": 0.7616726080437668, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.03825167859311865, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9983655705268544, "better_performance": false}}}}}}}