{"model": "copilot", "total_tasks": 6, "tested_tasks": 6, "successful_tasks": 6, "task_results": {"HumanEval/0": {"task_id": "HumanEval/0", "entry_point": "has_close_elements", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.015360107000000001, 0.015770021000000002, 0.017985409, 0.017339470000000003, 0.017572497], "memory_usages": [3.265625, 3.19921875, 1.62109375, 1.6015625, 1.6796875], "cpu_usages_list": [[0.0, 0.0, 0.0, 391.4, 0.0, 0.0, 197.5, 0.0, 193.3, 0.0, 0.0, 0.0, 193.6, 0.0, 193.7, 0.0, 197.5, 0.0, 193.4, 0.0, 193.0, 0.0, 193.5, 0.0, 197.5, 0.0, 195.5, 0.0, 193.5, 197.5, 0.0, 193.4, 0.0, 193.2, 0.0, 0.0, 195.8, 190.4, 0.0, 0.0, 193.4, 194.2, 196.0, 0.0, 0.0, 193.4, 0.0, 193.6, 0.0, 195.7, 0.0, 0.0, 394.9, 0.0, 193.4, 192.9, 0.0, 0.0, 0.0, 195.5, 0.0, 193.6, 0.0, 193.6, 193.6, 0.0, 193.5, 194.4, 0.0, 194.4, 0.0, 194.2, 0.0, 193.9, 0.0, 197.5, 0.0, 0.0, 193.8, 0.0, 194.0, 0.0, 195.6, 194.4, 0.0, 197.5, 0.0, 0.0, 197.5, 193.0, 0.0, 0.0, 195.5, 193.4, 0.0, 197.5, 193.6, 0.0, 192.9, 0.0, 0.0, 193.5, 192.8, 0.0, 0.0, 391.1, 0.0, 0.0, 0.0, 193.7, 0.0, 193.0, 193.7, 0.0, 193.5, 195.6, 0.0, 0.0, 197.5, 0.0, 193.3, 0.0, 193.6, 0.0, 193.5, 0.0, 193.6, 0.0, 195.6, 0.0, 194.5, 197.5, 194.2, 0.0, 0.0, 192.4, 0.0, 0.0, 195.8, 0.0, 194.2, 197.5, 193.6, 0.0, 193.6, 0.0, 0.0, 191.2, 194.1, 0.0, 194.4, 0.0, 194.3, 0.0, 197.4, 0.0, 194.4, 0.0, 194.1, 0.0, 194.3, 195.7, 0.0, 0.0, 197.4, 0.0, 194.5, 0.0, 194.2, 0.0, 0.0, 390.9, 0.0, 194.1, 0.0, 194.2, 0.0, 194.2, 0.0, 193.8, 0.0, 194.4, 0.0, 194.4, 195.5, 0.0, 0.0, 197.0, 194.6, 0.0, 0.0, 195.8, 0.0, 193.5, 0.0, 193.5, 0.0, 197.5, 0.0, 194.5, 194.2, 193.1, 0.0, 0.0, 195.8, 194.2, 0.0, 0.0, 0.0, 194.3, 0.0, 194.3, 0.0, 194.5, 195.8, 0.0, 0.0, 197.5, 193.5, 0.0, 193.2, 0.0, 193.6, 0.0, 195.8, 0.0, 194.3, 197.5, 193.6, 0.0, 192.3, 0.0, 193.8, 0.0, 193.2, 0.0, 193.5, 0.0, 193.2, 0.0, 197.4, 0.0, 0.0, 196.2, 194.4, 0.0, 0.0, 193.5, 194.4, 0.0, 0.0, 194.2, 0.0, 197.5], [0.0, 0.0, 194.2, 197.5, 0.0, 0.0, 194.0, 194.6, 0.0, 0.0, 0.0, 193.5, 193.4, 0.0, 193.4, 0.0, 193.4, 0.0, 193.6, 0.0, 193.3, 193.7, 0.0, 0.0, 193.4, 0.0, 197.5, 193.6, 0.0, 193.1, 0.0, 0.0, 193.5, 195.8, 0.0, 193.7, 195.7, 0.0, 0.0, 0.0, 194.4, 0.0, 193.6, 193.6, 195.8, 0.0, 0.0, 193.5, 0.0, 197.5, 0.0, 0.0, 194.3, 193.7, 193.0, 0.0, 195.8, 0.0, 0.0, 395.1, 0.0, 0.0, 193.3, 193.3, 0.0, 0.0, 0.0, 193.5, 0.0, 195.7, 0.0, 193.0, 197.5, 193.4, 0.0, 0.0, 193.4, 193.5, 0.0, 195.6, 0.0, 193.4, 0.0, 193.6, 0.0, 194.1, 0.0, 194.3, 0.0, 195.7, 0.0, 193.5, 197.6, 0.0, 0.0, 197.5, 0.0, 193.5, 193.5, 0.0, 193.5, 0.0, 195.9, 0.0, 193.3, 0.0, 0.0, 193.1, 0.0, 193.3, 0.0, 193.4, 0.0, 193.6, 193.1, 0.0, 193.4, 193.3, 0.0, 193.4, 0.0, 193.2, 0.0, 0.0, 193.2, 0.0, 193.3, 193.7, 0.0, 0.0, 193.4, 197.5, 0.0, 0.0, 193.1, 193.6, 193.3, 0.0, 195.8, 0.0, 197.5, 0.0, 0.0, 0.0, 194.0, 193.8, 0.0, 194.2, 0.0, 194.2, 0.0, 197.5, 0.0, 194.4, 194.3, 194.3, 0.0, 0.0, 195.0, 194.2, 0.0, 195.4, 0.0, 0.0, 197.4, 0.0, 0.0, 194.3, 193.7, 0.0, 0.0, 195.9, 194.3, 194.3, 0.0, 194.5, 0.0, 194.4, 0.0, 194.1, 0.0, 195.4, 0.0, 194.3, 197.3, 0.0, 0.0, 195.7, 0.0, 194.3, 0.0, 193.0, 194.0, 0.0, 195.8, 0.0, 194.1, 197.4, 0.0, 0.0, 194.3, 0.0, 194.1, 0.0, 0.0, 194.4, 194.1, 197.4, 0.0, 0.0, 194.4, 194.3, 0.0, 0.0, 197.4, 0.0, 194.1, 0.0, 194.3, 0.0, 197.5, 0.0, 194.0, 0.0, 195.6, 0.0, 194.2, 197.4, 0.0, 0.0, 194.5, 194.3, 0.0, 194.4, 0.0, 194.4, 0.0, 197.5, 0.0, 193.5, 0.0, 193.5, 0.0, 193.2, 195.5, 0.0, 0.0, 197.4, 193.6, 0.0, 194.3, 0.0, 194.0, 0.0, 192.8, 0.0, 194.2, 0.0, 193.9, 0.0, 197.4, 193.6, 0.0, 195.8, 0.0], [0.0, 0.0, 0.0, 194.4, 195.7, 0.0, 194.2, 0.0, 193.6, 0.0, 0.0, 193.2, 193.4, 0.0, 195.6, 195.8, 0.0, 197.5, 0.0, 193.4, 0.0, 0.0, 0.0, 193.6, 193.4, 193.5, 0.0, 195.2, 0.0, 193.6, 0.0, 193.6, 0.0, 0.0, 193.5, 0.0, 193.5, 0.0, 192.9, 0.0, 391.3, 0.0, 0.0, 197.5, 0.0, 194.2, 0.0, 194.3, 0.0, 194.5, 0.0, 194.0, 194.4, 197.4, 0.0, 0.0, 0.0, 195.6, 0.0, 194.2, 197.5, 0.0, 194.3, 0.0, 193.6, 0.0, 0.0, 193.6, 193.0, 0.0, 195.8, 0.0, 193.4, 0.0, 193.5, 194.5, 0.0, 193.2, 0.0, 193.3, 195.7, 0.0, 0.0, 197.5, 0.0, 193.7, 0.0, 0.0, 193.4, 0.0, 391.6, 0.0, 0.0, 195.9, 193.7, 0.0, 0.0, 193.5, 0.0, 193.6, 193.3, 0.0, 0.0, 195.5, 0.0, 193.5, 0.0, 193.4, 0.0, 192.0, 0.0, 193.5, 0.0, 195.7, 0.0, 193.4, 195.1, 193.6, 0.0, 197.5, 0.0, 0.0, 193.6, 193.0, 0.0, 192.8, 195.7, 0.0, 0.0, 0.0, 193.7, 0.0, 192.2, 0.0, 193.4, 0.0, 195.8, 0.0, 193.5, 195.6, 195.4, 0.0, 196.9, 0.0, 0.0, 0.0, 193.8, 194.1, 195.8, 195.8, 0.0, 0.0, 197.4, 194.3, 0.0, 0.0, 193.9, 0.0, 194.1, 0.0, 0.0, 194.4, 197.4, 0.0, 194.1, 194.3, 0.0, 0.0, 0.0, 195.9, 194.2, 0.0, 197.3, 0.0, 194.4, 0.0, 194.2, 0.0, 193.8, 0.0, 194.1, 0.0, 197.4, 0.0, 194.3, 193.8, 194.5, 0.0, 0.0, 195.4, 194.3, 0.0, 197.5, 0.0, 193.2, 0.0, 0.0, 192.9, 191.8, 195.2, 0.0, 192.1, 0.0, 192.5, 0.0, 195.3, 0.0, 191.1, 0.0, 0.0, 194.1, 194.4, 0.0, 0.0, 194.3, 193.4, 0.0, 195.4, 0.0, 193.6, 0.0, 193.5, 0.0, 194.4, 0.0, 0.0, 194.2, 195.5, 193.4, 0.0, 197.5, 0.0, 193.3, 0.0, 193.2, 0.0, 193.6, 0.0, 193.5, 0.0, 195.4, 0.0, 0.0, 197.3, 194.4, 0.0, 193.5, 0.0, 194.1, 0.0, 194.3, 193.5, 0.0, 0.0, 194.5], [0.0, 0.0, 197.5, 0.0, 195.7, 0.0, 194.2, 197.5, 0.0, 0.0, 193.2, 0.0, 0.0, 193.3, 0.0, 193.4, 0.0, 395.0, 0.0, 0.0, 197.7, 193.5, 0.0, 0.0, 193.6, 193.7, 0.0, 195.7, 0.0, 193.5, 0.0, 0.0, 193.5, 0.0, 0.0, 0.0, 193.6, 195.6, 0.0, 0.0, 395.0, 0.0, 0.0, 192.7, 0.0, 0.0, 193.3, 195.8, 193.5, 0.0, 0.0, 194.1, 0.0, 197.5, 0.0, 193.5, 192.9, 0.0, 193.6, 0.0, 0.0, 193.7, 0.0, 195.7, 0.0, 193.6, 197.5, 0.0, 0.0, 193.4, 193.1, 0.0, 0.0, 391.5, 0.0, 0.0, 197.5, 0.0, 193.5, 0.0, 193.6, 0.0, 193.5, 196.0, 193.4, 0.0, 197.5, 0.0, 0.0, 192.5, 0.0, 0.0, 193.5, 195.5, 192.8, 0.0, 0.0, 193.4, 0.0, 394.8, 0.0, 0.0, 193.6, 193.7, 0.0, 0.0, 195.1, 193.4, 0.0, 0.0, 193.5, 0.0, 192.7, 0.0, 193.7, 0.0, 193.6, 0.0, 193.6, 195.0, 0.0, 0.0, 197.5, 193.6, 0.0, 192.7, 0.0, 0.0, 193.5, 195.6, 0.0, 0.0, 195.5, 193.4, 0.0, 197.5, 0.0, 0.0, 194.1, 195.7, 0.0, 195.5, 0.0, 193.6, 0.0, 197.4, 0.0, 194.2, 0.0, 194.6, 0.0, 195.6, 196.0, 194.3, 0.0, 197.5, 0.0, 194.2, 0.0, 193.6, 0.0, 194.4, 0.0, 0.0, 0.0, 197.4, 194.4, 0.0, 0.0, 194.3, 194.4, 0.0, 195.8, 0.0, 194.3, 0.0, 194.3, 0.0, 192.7, 0.0, 197.4, 0.0, 195.8, 0.0, 194.2, 197.5, 0.0, 194.6, 193.7, 0.0, 0.0, 193.7, 195.6, 0.0, 0.0, 197.5, 0.0, 194.5, 193.4, 0.0, 194.0, 0.0, 195.8, 0.0, 0.0, 394.9, 0.0, 0.0, 195.0, 0.0, 0.0, 194.3, 195.6, 0.0, 0.0, 197.4, 193.7, 0.0, 195.7, 0.0, 0.0, 193.5, 193.4, 193.6, 0.0, 0.0, 194.4, 0.0, 197.5, 0.0, 193.6, 193.6, 0.0, 0.0, 193.5, 195.9, 0.0, 0.0, 195.7, 194.2, 0.0, 197.3, 194.2, 0.0, 193.1, 0.0, 194.1, 0.0, 193.3, 0.0, 0.0, 195.4, 194.2, 0.0], [0.0, 0.0, 194.3, 0.0, 194.4, 0.0, 0.0, 0.0, 391.6, 0.0, 193.2, 0.0, 0.0, 192.9, 193.4, 195.7, 0.0, 0.0, 197.5, 0.0, 0.0, 193.4, 0.0, 193.6, 0.0, 195.8, 194.5, 0.0, 197.5, 0.0, 194.4, 0.0, 194.6, 0.0, 194.4, 0.0, 194.2, 0.0, 197.5, 194.2, 0.0, 0.0, 194.4, 0.0, 194.2, 195.6, 0.0, 0.0, 394.9, 0.0, 195.5, 0.0, 193.4, 0.0, 0.0, 193.2, 0.0, 193.6, 195.9, 0.0, 0.0, 197.5, 0.0, 193.6, 0.0, 193.4, 0.0, 189.8, 0.0, 192.6, 0.0, 385.7, 0.0, 0.0, 193.8, 0.0, 193.9, 195.6, 0.0, 0.0, 192.8, 0.0, 193.0, 0.0, 195.5, 0.0, 196.9, 0.0, 192.9, 196.9, 0.0, 0.0, 393.7, 0.0, 0.0, 192.8, 192.6, 0.0, 194.0, 192.5, 0.0, 191.8, 0.0, 192.7, 0.0, 193.0, 0.0, 0.0, 192.2, 193.4, 0.0, 193.7, 0.0, 193.4, 0.0, 193.6, 0.0, 197.5, 0.0, 0.0, 193.6, 193.4, 193.5, 0.0, 195.6, 0.0, 193.6, 0.0, 0.0, 193.7, 193.5, 0.0, 0.0, 0.0, 195.7, 193.7, 192.8, 197.5, 0.0, 0.0, 194.4, 0.0, 194.3, 0.0, 194.1, 0.0, 0.0, 195.7, 0.0, 194.5, 197.4, 0.0, 0.0, 194.3, 194.2, 0.0, 0.0, 195.6, 194.3, 194.3, 197.4, 0.0, 0.0, 193.2, 0.0, 0.0, 194.1, 195.5, 0.0, 0.0, 394.9, 0.0, 193.5, 0.0, 0.0, 194.0, 0.0, 194.1, 0.0, 193.9, 195.8, 0.0, 0.0, 197.4, 0.0, 194.1, 0.0, 194.3, 0.0, 193.6, 0.0, 193.1, 0.0, 195.7, 0.0, 194.3, 197.5, 196.8, 0.0, 0.0, 195.9, 0.0, 195.8, 0.0, 194.0, 0.0, 194.1, 194.1, 0.0, 0.0, 195.6, 194.4, 0.0, 197.5, 0.0, 193.4, 0.0, 193.5, 0.0, 192.6, 0.0, 193.6, 0.0, 195.5, 0.0, 0.0, 394.9, 0.0, 193.6, 0.0, 193.0, 0.0, 193.1, 0.0, 193.6, 0.0, 195.5, 0.0, 193.6, 197.4, 0.0, 0.0, 193.6, 193.9, 0.0, 0.0, 195.6, 0.0, 193.6, 195.7, 0.0, 0.0, 197.4, 0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0168055008, "std_execution_time": 0.0011647930385992177, "avg_memory_usage": 2.2734375, "std_memory_usage": 0.8762154992204936, "avg_cpu_usage": 100.19479512702422, "std_cpu_usage": 0.4912539568323757}, "evalperf_canonical_results": {"execution_times": [0.006943091, 0.013317008, 0.015507885, 0.016622225, 0.016386226], "memory_usages": [0.6328125, 3.16796875, 3.64453125, 3.60546875, 1.59375], "cpu_usages_list": [[0.0, 0.0, 0.0, 197.5, 0.0, 194.3, 0.0, 194.2, 194.2, 0.0, 195.8, 0.0, 0.0, 197.5, 194.2, 0.0, 0.0, 193.5, 194.1, 0.0, 195.6, 0.0, 194.3, 197.5, 0.0, 0.0, 0.0, 194.0, 194.3, 0.0, 195.2, 194.0, 0.0, 197.5, 0.0, 193.4, 0.0, 194.1, 0.0, 0.0, 197.7, 194.0, 0.0, 0.0, 194.3, 0.0, 194.4, 0.0, 194.0, 0.0, 197.5, 0.0, 194.3, 193.8, 0.0, 0.0, 193.2, 197.5, 0.0, 197.3, 0.0, 193.7, 195.8, 0.0, 192.9, 0.0, 0.0, 194.9, 193.4, 0.0, 0.0, 193.6, 193.6, 0.0, 194.2, 0.0, 194.4, 0.0, 194.3, 0.0, 197.5, 0.0, 193.8, 0.0, 190.7, 0.0, 197.2, 193.4, 0.0, 193.7, 193.8, 0.0, 0.0, 195.6, 0.0, 194.1, 0.0, 0.0, 194.4, 0.0, 0.0, 0.0, 194.1, 195.9, 0.0, 0.0, 197.5, 193.7, 193.7, 0.0, 0.0, 193.4, 193.5, 0.0, 193.6, 0.0, 197.5, 0.0, 193.2, 0.0, 193.3, 0.0, 196.9, 197.6, 0.0, 0.0, 197.7, 0.0, 193.5, 0.0, 195.8, 0.0, 193.4, 197.5, 0.0, 0.0, 195.9, 193.2, 0.0, 0.0, 195.8, 0.0, 193.9, 197.5, 0.0, 0.0, 193.2, 194.3, 0.0, 194.4, 193.7, 0.0, 194.4, 0.0, 194.3, 0.0, 197.5, 0.0, 193.1, 0.0, 197.4, 0.0, 194.0, 0.0, 194.4, 0.0, 0.0, 197.3, 194.3, 0.0, 195.8, 195.7, 0.0, 197.4, 0.0, 194.6, 0.0, 0.0, 195.7, 0.0, 195.5, 0.0, 193.9, 197.3, 0.0, 0.0, 194.2, 193.7, 0.0, 0.0, 194.2, 0.0, 195.6, 195.6, 193.8, 0.0, 197.5, 0.0, 194.2, 0.0, 0.0, 196.9, 0.0, 197.7, 197.7, 0.0, 0.0, 197.7, 197.6, 0.0, 0.0, 197.4, 197.3, 0.0, 197.7, 0.0, 0.0, 197.3, 197.7, 0.0, 0.0, 197.7, 197.0, 0.0, 0.0, 197.7, 197.4, 0.0, 0.0, 197.4, 197.4, 0.0, 197.7, 0.0, 0.0, 197.4, 197.4, 0.0, 0.0, 197.3, 193.2, 0.0, 197.5, 0.0, 193.5, 0.0, 194.0, 193.9, 0.0, 195.8, 0.0, 194.2, 197.2, 0.0, 0.0, 197.4, 194.0, 0.0, 0.0, 192.7], [0.0, 0.0, 0.0, 197.4, 0.0, 193.7, 193.3, 0.0, 0.0, 193.7, 0.0, 193.6, 0.0, 195.7, 0.0, 194.1, 0.0, 193.9, 0.0, 193.8, 191.6, 0.0, 194.3, 0.0, 0.0, 194.4, 197.3, 0.0, 194.1, 193.6, 0.0, 0.0, 193.5, 0.0, 193.7, 0.0, 197.5, 0.0, 193.4, 0.0, 194.5, 193.3, 0.0, 195.4, 194.4, 0.0, 195.7, 0.0, 193.4, 0.0, 0.0, 193.5, 0.0, 197.4, 0.0, 0.0, 395.4, 0.0, 197.4, 0.0, 192.9, 0.0, 194.2, 0.0, 0.0, 194.1, 197.3, 0.0, 193.7, 0.0, 0.0, 193.6, 193.3, 0.0, 194.6, 194.4, 0.0, 194.1, 0.0, 0.0, 193.8, 0.0, 0.0, 197.5, 194.1, 0.0, 196.5, 0.0, 194.4, 0.0, 194.3, 0.0, 194.3, 0.0, 194.2, 0.0, 394.8, 0.0, 0.0, 194.4, 194.2, 0.0, 0.0, 195.6, 193.8, 0.0, 197.5, 0.0, 0.0, 193.2, 192.8, 0.0, 0.0, 195.6, 193.9, 0.0, 197.5, 0.0, 193.3, 0.0, 0.0, 193.6, 195.8, 0.0, 193.6, 0.0, 195.5, 193.7, 0.0, 197.5, 0.0, 0.0, 193.0, 193.9, 0.0, 0.0, 195.8, 0.0, 193.4, 195.9, 0.0, 193.3, 0.0, 193.1, 0.0, 0.0, 193.7, 193.2, 0.0, 195.9, 0.0, 194.4, 0.0, 194.4, 0.0, 193.9, 0.0, 194.2, 0.0, 195.7, 0.0, 194.3, 197.4, 0.0, 0.0, 194.2, 194.2, 0.0, 0.0, 194.2, 197.5, 0.0, 197.4, 194.2, 0.0, 197.2, 0.0, 194.3, 0.0, 193.3, 0.0, 194.3, 195.5, 0.0, 0.0, 197.4, 0.0, 0.0, 387.7, 0.0, 0.0, 193.4, 195.5, 0.0, 0.0, 197.4, 0.0, 194.0, 0.0, 193.0, 194.4, 0.0, 194.9, 0.0, 0.0, 195.6, 0.0, 194.4, 196.8, 0.0, 194.1, 0.0, 194.1, 0.0, 194.2, 195.6, 0.0, 0.0, 197.4, 193.8, 0.0, 0.0, 0.0, 194.0, 0.0, 195.8, 0.0, 0.0, 197.4, 197.5, 0.0, 0.0, 0.0, 194.2, 0.0, 195.4, 0.0, 193.4, 0.0, 193.6, 0.0, 197.4, 0.0, 0.0, 194.0, 194.1, 0.0, 0.0, 195.5, 194.3, 194.4, 197.4, 0.0, 0.0, 192.9], [0.0, 197.5, 0.0, 0.0, 194.3, 194.0, 0.0, 0.0, 195.6, 193.3, 0.0, 197.5, 0.0, 193.5, 0.0, 0.0, 193.6, 0.0, 195.7, 0.0, 0.0, 197.5, 193.4, 194.4, 0.0, 0.0, 193.4, 0.0, 191.3, 0.0, 0.0, 195.8, 193.7, 193.7, 0.0, 0.0, 193.2, 191.5, 0.0, 197.5, 0.0, 0.0, 193.6, 0.0, 394.9, 0.0, 0.0, 194.9, 0.0, 193.5, 193.4, 0.0, 0.0, 0.0, 391.5, 0.0, 193.3, 0.0, 193.0, 0.0, 193.2, 0.0, 194.1, 0.0, 195.4, 0.0, 0.0, 394.9, 0.0, 193.2, 0.0, 193.3, 0.0, 0.0, 195.4, 0.0, 193.7, 0.0, 193.6, 0.0, 194.6, 193.5, 0.0, 0.0, 193.4, 0.0, 193.5, 195.5, 0.0, 193.5, 197.5, 0.0, 0.0, 0.0, 193.3, 0.0, 0.0, 195.7, 0.0, 193.3, 197.5, 0.0, 0.0, 194.4, 193.6, 0.0, 0.0, 192.8, 0.0, 193.6, 195.8, 0.0, 0.0, 394.9, 0.0, 193.3, 0.0, 0.0, 193.6, 0.0, 195.7, 0.0, 193.6, 197.5, 0.0, 0.0, 386.4, 0.0, 0.0, 193.3, 0.0, 193.3, 0.0, 195.5, 0.0, 0.0, 197.5, 0.0, 193.4, 193.5, 194.5, 0.0, 0.0, 195.8, 0.0, 194.4, 197.4, 0.0, 0.0, 193.9, 0.0, 197.4, 0.0, 195.6, 194.3, 0.0, 197.5, 0.0, 194.1, 0.0, 194.4, 194.2, 0.0, 195.3, 0.0, 0.0, 195.7, 0.0, 195.8, 197.4, 0.0, 194.4, 0.0, 193.6, 0.0, 194.1, 0.0, 193.5, 0.0, 197.4, 0.0, 194.4, 0.0, 0.0, 194.6, 0.0, 391.5, 0.0, 193.4, 0.0, 194.0, 0.0, 193.7, 0.0, 0.0, 194.4, 195.9, 0.0, 0.0, 197.5, 194.2, 0.0, 197.0, 0.0, 194.3, 0.0, 193.7, 194.4, 0.0, 194.7, 0.0, 194.2, 0.0, 194.2, 0.0, 193.4, 0.0, 197.4, 0.0, 195.6, 0.0, 0.0, 197.4, 0.0, 194.3, 0.0, 195.4, 0.0, 193.3, 0.0, 193.3, 0.0, 197.1, 0.0, 195.6, 192.3, 0.0, 0.0, 193.9, 194.3, 194.2, 0.0, 195.7, 0.0, 194.4, 0.0, 193.7, 0.0, 193.9, 0.0, 194.4, 0.0, 195.6], [0.0, 0.0, 0.0, 197.5, 197.5, 0.0, 0.0, 193.5, 193.6, 0.0, 196.0, 0.0, 0.0, 197.5, 0.0, 193.2, 192.5, 0.0, 0.0, 193.4, 193.0, 0.0, 0.0, 195.8, 193.4, 0.0, 197.5, 193.3, 0.0, 193.5, 0.0, 0.0, 0.0, 195.8, 193.5, 0.0, 195.4, 193.7, 0.0, 197.5, 0.0, 0.0, 193.5, 195.0, 193.6, 0.0, 195.8, 0.0, 0.0, 197.5, 0.0, 0.0, 386.1, 0.0, 193.7, 0.0, 195.7, 0.0, 192.6, 0.0, 0.0, 193.4, 197.5, 0.0, 0.0, 193.7, 193.6, 0.0, 193.6, 195.8, 0.0, 0.0, 197.5, 0.0, 194.1, 0.0, 193.3, 0.0, 0.0, 195.7, 193.4, 193.1, 0.0, 192.0, 0.0, 193.8, 195.5, 0.0, 0.0, 193.6, 0.0, 193.6, 0.0, 193.4, 0.0, 197.5, 0.0, 193.3, 193.2, 0.0, 0.0, 194.4, 0.0, 194.6, 194.2, 0.0, 0.0, 197.5, 0.0, 194.0, 0.0, 194.3, 195.9, 0.0, 0.0, 197.5, 194.1, 0.0, 194.0, 0.0, 193.2, 0.0, 196.0, 193.3, 0.0, 195.9, 0.0, 0.0, 197.5, 193.6, 0.0, 193.3, 0.0, 0.0, 193.4, 195.8, 193.6, 0.0, 0.0, 0.0, 194.0, 0.0, 0.0, 194.5, 194.3, 0.0, 0.0, 0.0, 197.5, 194.5, 0.0, 193.9, 0.0, 194.4, 194.3, 0.0, 194.5, 0.0, 197.5, 0.0, 194.4, 192.9, 0.0, 0.0, 0.0, 195.7, 194.3, 0.0, 196.0, 0.0, 194.1, 197.7, 0.0, 194.4, 0.0, 193.9, 0.0, 193.8, 195.8, 0.0, 0.0, 197.4, 194.2, 0.0, 0.0, 194.1, 0.0, 0.0, 195.6, 0.0, 193.5, 197.5, 0.0, 0.0, 195.3, 194.1, 0.0, 193.0, 0.0, 193.5, 0.0, 195.7, 194.2, 0.0, 0.0, 193.9, 0.0, 194.3, 195.7, 0.0, 0.0, 197.4, 194.4, 0.0, 197.0, 0.0, 193.3, 0.0, 192.7, 0.0, 194.3, 0.0, 194.1, 197.4, 197.5, 0.0, 0.0, 191.7, 193.2, 0.0, 0.0, 195.5, 192.9, 0.0, 195.6, 0.0, 193.4, 0.0, 193.4, 0.0, 192.7, 0.0, 0.0, 194.3, 195.8, 0.0, 0.0, 195.7, 194.2, 0.0, 0.0, 194.4, 0.0], [0.0, 0.0, 193.4, 0.0, 0.0, 194.3, 194.3, 0.0, 0.0, 194.3, 0.0, 197.4, 0.0, 193.4, 193.4, 194.6, 0.0, 0.0, 0.0, 193.5, 0.0, 197.5, 0.0, 193.4, 196.4, 0.0, 193.7, 193.6, 0.0, 193.5, 0.0, 195.8, 0.0, 0.0, 197.5, 193.4, 0.0, 0.0, 193.4, 193.6, 0.0, 195.8, 0.0, 193.3, 197.5, 0.0, 193.7, 0.0, 0.0, 193.5, 0.0, 195.5, 0.0, 0.0, 197.5, 193.7, 193.5, 0.0, 0.0, 193.4, 0.0, 193.6, 0.0, 193.5, 195.6, 193.7, 0.0, 0.0, 0.0, 193.5, 192.9, 0.0, 193.0, 0.0, 194.1, 0.0, 0.0, 391.5, 0.0, 193.4, 0.0, 0.0, 192.7, 0.0, 193.4, 0.0, 193.4, 192.6, 0.0, 193.4, 195.7, 0.0, 0.0, 197.5, 0.0, 193.5, 0.0, 193.7, 0.0, 193.6, 0.0, 193.5, 0.0, 197.5, 193.4, 0.0, 192.8, 0.0, 193.4, 0.0, 0.0, 193.5, 0.0, 391.6, 0.0, 0.0, 197.5, 193.3, 0.0, 0.0, 193.2, 0.0, 193.3, 0.0, 193.3, 0.0, 195.7, 0.0, 194.1, 197.5, 194.3, 0.0, 0.0, 194.1, 193.2, 0.0, 195.7, 0.0, 194.3, 0.0, 194.4, 0.0, 0.0, 194.4, 194.2, 0.0, 0.0, 194.4, 0.0, 197.5, 0.0, 194.5, 194.2, 194.1, 0.0, 0.0, 195.6, 194.2, 0.0, 197.5, 0.0, 194.4, 0.0, 194.2, 0.0, 0.0, 197.5, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 387.1, 0.0, 197.4, 0.0, 194.3, 0.0, 197.4, 0.0, 197.1, 0.0, 194.1, 0.0, 194.2, 195.6, 0.0, 0.0, 197.4, 194.2, 0.0, 0.0, 0.0, 0.0, 193.5, 195.9, 0.0, 0.0, 197.4, 0.0, 194.5, 0.0, 194.1, 194.5, 0.0, 194.9, 0.0, 195.7, 197.5, 0.0, 0.0, 193.8, 192.7, 0.0, 194.2, 0.0, 194.1, 0.0, 195.6, 0.0, 0.0, 197.4, 194.5, 0.0, 0.0, 194.1, 0.0, 195.7, 0.0, 197.4, 0.0, 197.4, 0.0, 193.6, 0.0, 193.4, 0.0, 193.8, 195.8, 0.0, 0.0, 197.4, 193.7, 0.0, 195.5, 0.0, 194.1, 194.1, 0.0, 194.5, 0.0, 195.2, 0.0, 0.0, 394.9, 0.0, 0.0, 193.1, 194.3]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.013755287, "std_execution_time": 0.004025260118657737, "avg_memory_usage": 2.52890625, "std_memory_usage": 1.349230850951814, "avg_cpu_usage": 99.78907105085878, "std_cpu_usage": 0.4846560836680715}, "original_canonical_results": {"execution_times": [0.008170809131115675, 0.008256920147687197, 0.00838595605455339, 0.008367676055058837, 0.008417373057454824], "memory_usages": [3.171875, 1.57421875, 1.5859375, 1.65625, 0.63671875], "cpu_usages_list": [[0.0], [0.0], [0.0], [0.0], [0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.008319746889173984, "std_execution_time": 0.00010288510883157144, "avg_memory_usage": 1.725, "std_memory_usage": 0.9116747324956932, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 1.2217484666077851, "memory_usage_ratio": 0.8989805375347545, "cpu_usage_ratio": 1.004065816746191}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0168055008, "std": 0.0011647930385992177, "n": 5, "confidence_interval_95": [0.015359219218612982, 0.01825178238138702]}, "canonical": {"mean": 0.013755287000000001, "std": 0.004025260118657737, "n": 5, "confidence_interval_95": [0.008757266427257267, 0.018753307572742735]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.2011814104850345, "p_value": 0.09253072567871301, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.6276449793937333, "p_value": 0.14225086981440538, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 20.0, "p_value": 0.15079365079365079, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.029413071404405, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.2217484666077851, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.2734375, "std": 0.8762154992204936, "n": 5, "confidence_interval_95": [1.1854722599497727, 3.3614027400502273]}, "canonical": {"mean": 2.52890625, "std": 1.349230850951814, "n": 5, "confidence_interval_95": [0.8536148771073004, 4.2041976228927]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.29774506476483653, "p_value": 0.7807217828163647, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.3550796288832191, "p_value": 0.7317093604842348, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 7.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 12.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.22457207559965653, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.8989805375347545, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 100.19479512702421, "std": 0.4912539568323694, "n": 5, "confidence_interval_95": [99.58482277485031, 100.8047674791981]}, "canonical": {"mean": 99.78907105085878, "std": 0.4846560836680797, "n": 5, "confidence_interval_95": [99.18729104025769, 100.39085106145987]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.5205066305793717, "p_value": 0.20302187004045547, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.3146527941225028, "p_value": 0.22506201327197795, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 2.0, "p_value": 0.1875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 19.0, "p_value": 0.2222222222222222, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.831459432346306, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.004065816746191, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 2.019953374046517, "memory_usage_ratio": 1.3179347826086956, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0168055008, "std": 0.0011647930385992177, "n": 5, "confidence_interval_95": [0.015359219218612982, 0.01825178238138702]}, "canonical": {"mean": 0.008319746889173984, "std": 0.00010288510883157143, "n": 5, "confidence_interval_95": [0.008191998153586545, 0.008447495624761422]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 17.788103156898277, "p_value": 5.868639045216823e-05, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 16.22703058456774, "p_value": 2.0917058079259655e-07, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 10.262875261689523, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.019953374046517, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.2734375, "std": 0.8762154992204936, "n": 5, "confidence_interval_95": [1.1854722599497727, 3.3614027400502273]}, "canonical": {"mean": 1.725, "std": 0.9116747324956932, "n": 5, "confidence_interval_95": [0.5930063065880484, 2.856993693411952]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.6396834983386315, "p_value": 0.17641430253268098, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.9698417889683705, "p_value": 0.3605294273261281, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 2.0, "p_value": 0.1875, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 20.0, "p_value": 0.15079365079365079, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.6133818046304828, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.3179347826086956, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 100.19479512702421, "std": 0.4912539568323694, "n": 5, "confidence_interval_95": [99.58482277485031, 100.8047674791981]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 5, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 456.06222561610997, "p_value": 1.386886501130517e-10, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 456.06222561610997, "p_value": 5.983634673874179e-19, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007494957516935239, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 288.43907754249915, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": true}}}}}, "HumanEval/9": {"task_id": "HumanEval/9", "entry_point": "rolling_max", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.0058222460000000005, 0.006101182, 0.005740112, 0.006193775, 0.006134106], "memory_usages": [0.6328125, 1.62109375, 3.2265625, 0.6328125, 1.67578125], "cpu_usages_list": [[0.0, 0.0, 195.9, 0.0, 194.3, 0.0, 0.0], [0.0, 0.0, 197.5, 0.0, 0.0, 193.7, 0.0], [0.0, 0.0, 194.2, 0.0, 0.0, 194.2], [0.0, 0.0, 0.0, 197.5, 0.0, 194.2, 197.5], [0.0, 0.0, 0.0, 194.4, 193.7, 0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0059982842000000005, "std_execution_time": 0.00020303602155578198, "avg_memory_usage": 1.5578125, "std_memory_usage": 1.062296769396166, "avg_cpu_usage": 65.04333333333334, "std_cpu_usage": 11.580917185488495}, "evalperf_canonical_results": {"execution_times": [0.022769134, 0.0029184930000000003, 0.003055611, 0.0031528710000000002, 0.002988512], "memory_usages": [1.59375, 1.55859375, 3.19140625, 3.16796875, 0.6328125], "cpu_usages_list": [[0.0, 0.0, 194.7, 0.0, 0.0, 0.0], [0.0, 0.0, 191.8, 0.0, 195.6, 0.0], [0.0, 0.0, 197.0, 0.0, 0.0, 0.0], [0.0, 0.0, 192.5, 0.0, 195.4, 0.0], [0.0, 197.5, 0.0, 0.0, 194.9, 0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0069769242, "std_execution_time": 0.008828537055174866, "avg_memory_usage": 2.02890625, "std_memory_usage": 1.1189841595466874, "avg_cpu_usage": 51.980000000000004, "std_cpu_usage": 17.656905322155286}, "original_canonical_results": {"execution_times": [0.00852949102409184, 0.008167213993147016, 0.008155321003869176, 0.008361621061339974, 0.007952104089781642], "memory_usages": [0.62890625, 1.59765625, 1.55859375, 3.22265625, 0.63671875], "cpu_usages_list": [[0.0], [0.0], [0.0], [0.0], [0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.00823315023444593, "std_execution_time": 0.00022006506393011308, "avg_memory_usage": 1.52890625, "std_memory_usage": 1.0583472383641983, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 0.8597318858645476, "memory_usage_ratio": 0.7678090103966115, "cpu_usage_ratio": 1.2513146081826343}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0059982842, "std": 0.00020303602155578198, "n": 5, "confidence_interval_95": [0.005746181681695661, 0.006250386718304338]}, "canonical": {"mean": 0.0069769242, "std": 0.008828537055174866, "n": 5, "confidence_interval_95": [-0.00398515241821853, 0.01793900081821853]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.24508688218903837, "p_value": 0.8184494200051501, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.2478017694611174, "p_value": 0.8105295481576079, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 20.0, "p_value": 0.15079365079365079, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.1567235999434173, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.8597318858645475, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.5578125, "std": 1.062296769396166, "n": 5, "confidence_interval_95": [0.2387968460326122, 2.876828153967388]}, "canonical": {"mean": 2.02890625, "std": 1.1189841595466872, "n": 5, "confidence_interval_95": [0.6395039045305788, 3.418308595469421]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.777966406260816, "p_value": 0.4800379228285584, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.6827302288094884, "p_value": 0.5140577349462612, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 7.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 12.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.43179651009717834, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.7678090103966115, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 65.04333333333334, "std": 11.5809171854885, "n": 5, "confidence_interval_95": [50.66372539793297, 79.4229412687337]}, "canonical": {"mean": 51.98, "std": 17.65690532215528, "n": 5, "confidence_interval_95": [30.05605634178731, 73.90394365821268]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.7124436867897652, "p_value": 0.16197655869928843, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.3833373142707481, "p_value": 0.20393740502246951, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 0.3125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 17.0, "p_value": 0.42063492063492064, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.8748993370791425, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.2513146081826345, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 0.72855274459882, "memory_usage_ratio": 1.018906489524783, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0059982842, "std": 0.00020303602155578198, "n": 5, "confidence_interval_95": [0.005746181681695661, 0.006250386718304338]}, "canonical": {"mean": 0.00823315023444593, "std": 0.00022006506393011308, "n": 5, "confidence_interval_95": [0.007959903367301497, 0.008506397101590362]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -14.697326151891982, "p_value": 0.00012471307488842853, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -16.68998245674859, "p_value": 1.6799303101898412e-07, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 0.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -10.555671734315649, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.7285527445988199, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.5578125, "std": 1.062296769396166, "n": 5, "confidence_interval_95": [0.2387968460326122, 2.876828153967388]}, "canonical": {"mean": 1.52890625, "std": 1.0583472383641983, "n": 5, "confidence_interval_95": [0.21479458652479488, 2.843017913475205]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.03977065959870208, "p_value": 0.970181830200741, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.04310456151434337, "p_value": 0.9666746258696733, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 5.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 15.0, "p_value": 0.6751736149271245, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.02726171838563235, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.018906489524783, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 65.04333333333334, "std": 11.5809171854885, "n": 5, "confidence_interval_95": [50.66372539793297, 79.4229412687337]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 5, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 12.558704331186908, "p_value": 0.00023133123860401606, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 12.558704331186908, "p_value": 1.5145039202210653e-06, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007494957516935239, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 7.942822029434445, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": true}}}}}, "HumanEval/16": {"task_id": "HumanEval/16", "entry_point": "count_distinct_characters", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.069847691, 0.070437662, 0.06910055100000001, 0.06925186900000001, 0.07056942000000001], "memory_usages": [1.6171875, 0.6328125, 1.65625, 1.5859375, 0.6328125], "cpu_usages_list": [[0.0, 195.8, 0.0, 0.0, 197.5, 194.0], [0.0, 196.7, 0.0, 0.0, 193.7], [0.0, 0.0, 0.0, 195.7, 0.0], [0.0, 0.0, 0.0, 195.8, 193.5], [0.0, 0.0, 193.5, 0.0, 197.5]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0698414386, "std_execution_time": 0.0006674550739632566, "avg_memory_usage": 1.225, "std_memory_usage": 0.5411643804970445, "avg_cpu_usage": 74.23266666666667, "std_cpu_usage": 21.415815080345546}, "evalperf_canonical_results": {"execution_times": [0.0066458640000000005, 0.005965116, 0.0060354630000000005, 0.005818855, 0.00560932], "memory_usages": [0.62890625, 1.5703125, 1.57421875, 1.578125, 0.6328125], "cpu_usages_list": [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 194.3]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0060149236, "std_execution_time": 0.00038856969405796443, "avg_memory_usage": 1.196875, "std_memory_usage": 0.5167084379640817, "avg_cpu_usage": 12.953333333333333, "std_cpu_usage": 28.964533868547274}, "original_canonical_results": {"execution_times": [0.008195282891392708, 0.00823901011608541, 0.008095728000625968, 0.008087245980277658, 0.008124707033857703], "memory_usages": [1.62109375, 1.58203125, 3.19921875, 3.19140625, 1.58203125], "cpu_usages_list": [[0.0], [0.0], [0.0], [0.0], [0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.00814839480444789, "std_execution_time": 6.611298813066505e-05, "avg_memory_usage": 2.23515625, "std_memory_usage": 0.8766481408698845, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 11.611359219924255, "memory_usage_ratio": 1.0234986945169715, "cpu_usage_ratio": 5.7307771487390635}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0698414386, "std": 0.0006674550739632566, "n": 5, "confidence_interval_95": [0.06901268366424139, 0.07067019353575861]}, "canonical": {"mean": 0.006014923600000001, "std": 0.00038856969405796443, "n": 5, "confidence_interval_95": [0.0055324506000951745, 0.006497396599904828]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 169.76036923086224, "p_value": 7.222799281056762e-09, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 184.79372266491796, "p_value": 8.229132948243347e-16, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 116.87381218452424, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 11.611359219924253, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.225, "std": 0.5411643804970445, "n": 5, "confidence_interval_95": [0.5530556716265754, 1.8969443283734249]}, "canonical": {"mean": 1.196875, "std": 0.5167084379640817, "n": 5, "confidence_interval_95": [0.5552967350118954, 1.8384532649881045]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.09226417886813172, "p_value": 0.9309243133850457, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.08405103213400197, "p_value": 0.9350810770503086, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 18.0, "p_value": 0.2903468336607644, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.0531585402462898, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0234986945169715, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 74.23266666666666, "std": 21.415815080345542, "n": 5, "confidence_interval_95": [47.6414200892123, 100.82391324412102]}, "canonical": {"mean": 12.953333333333333, "std": 28.964533868547274, "n": 5, "confidence_interval_95": [-23.010885595995482, 48.91755226266215]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 4.00748690274355, "p_value": 0.016030017502383153, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 3.8039231584540847, "p_value": 0.005207485283102285, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 24.0, "p_value": 0.017750113652290653, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.405812244995299, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 5.730777148739063, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 8.571189820340601, "memory_usage_ratio": 0.5480601188395666, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0698414386, "std": 0.0006674550739632566, "n": 5, "confidence_interval_95": [0.06901268366424139, 0.07067019353575861]}, "canonical": {"mean": 0.00814839480444789, "std": 6.611298813066505e-05, "n": 5, "confidence_interval_95": [0.00806630468727237, 0.00823048492162341]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 219.83000353012218, "p_value": 2.5688779590452445e-09, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 205.67382919772126, "p_value": 3.495345435174302e-16, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 130.0795510706482, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 8.571189820340601, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.225, "std": 0.5411643804970445, "n": 5, "confidence_interval_95": [0.5530556716265754, 1.8969443283734249]}, "canonical": {"mean": 2.23515625, "std": 0.8766481408698846, "n": 5, "confidence_interval_95": [1.1466538143896006, 3.3236586856103996]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -3.5081511296575316, "p_value": 0.024713188515361174, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -2.1925019060318, "p_value": 0.05968769906481835, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 7.0, "p_value": 0.2933255737660211, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -1.3866599594641904, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.5480601188395666, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 74.23266666666666, "std": 21.415815080345542, "n": 5, "confidence_interval_95": [47.6414200892123, 100.82391324412102]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 5, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 7.750780822257229, "p_value": 0.0014929602120711998, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 7.750780822257229, "p_value": 5.480102963767324e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007494957516935239, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 4.902024208617107, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": true}}}}}, "Mbpp/3": {"task_id": "Mbpp/3", "entry_point": "is_not_prime", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [5.837186628, 5.8885390300000005, 5.979128006000001, 6.015038608, 6.135975912], "memory_usages": [1.62109375, 3.19140625, 3.13671875, 1.6171875, 1.66015625], "cpu_usages_list": [[0.0, 197.5, 0.0, 0.0, 197.5, 194.3, 0.0, 0.0, 195.7, 193.4, 0.0, 197.5, 0.0, 193.3, 0.0, 193.4, 0.0, 193.4, 0.0, 193.5, 0.0, 195.5, 0.0, 193.3, 197.5, 0.0, 0.0, 193.4, 193.4, 0.0, 0.0, 195.7, 193.7, 0.0, 197.5, 0.0, 193.6, 0.0, 192.9, 0.0, 193.6, 0.0, 195.7, 0.0, 195.7, 0.0, 193.5, 197.5, 0.0, 0.0, 193.0, 192.9, 0.0, 0.0, 196.0, 193.5, 0.0, 197.5, 0.0, 194.0, 0.0, 197.6, 0.0, 194.0, 0.0, 193.3, 0.0, 197.5, 0.0, 193.5, 196.6, 0.0, 0.0, 193.3, 193.7, 0.0, 0.0, 195.6, 195.6, 0.0, 197.5, 0.0, 193.0, 0.0, 193.6, 0.0, 193.0, 0.0, 193.1, 0.0, 197.5, 0.0, 193.7, 196.3, 0.0, 0.0, 192.3, 193.4, 0.0, 0.0, 195.6, 193.4, 0.0, 197.5, 0.0, 193.5, 0.0, 194.4, 0.0, 194.4, 0.0, 194.2, 0.0, 197.5, 0.0, 194.0, 0.0, 194.3, 0.0, 194.2, 195.6, 0.0, 0.0, 197.5, 194.2, 0.0, 0.0, 193.3, 193.6, 0.0, 195.1, 0.0, 197.5, 0.0, 193.6, 0.0, 193.2, 0.0, 195.9, 0.0, 195.7, 0.0, 193.6, 196.0, 0.0, 0.0, 194.8, 193.7, 0.0, 0.0, 192.8, 193.7, 0.0, 195.6, 0.0, 193.1, 0.0, 193.7, 0.0, 193.1, 0.0, 193.5, 0.0, 195.6, 0.0, 193.9, 195.7, 0.0, 0.0, 197.5, 193.5, 0.0, 0.0, 0.0, 193.6, 0.0, 195.7, 0.0, 192.2, 0.0, 193.5, 0.0, 193.7, 0.0, 193.5, 0.0, 193.4, 0.0, 193.5, 195.8, 0.0, 0.0, 197.5, 193.3, 0.0, 193.5, 0.0, 193.3, 0.0, 195.4, 0.0, 193.4, 0.0, 193.7, 0.0, 195.1, 0.0, 193.5, 0.0, 193.3, 0.0, 193.4, 195.9, 0.0, 0.0, 197.5, 193.6, 0.0, 193.6, 0.0, 192.2, 0.0, 195.7, 0.0, 193.4, 0.0, 193.3, 0.0, 197.2, 0.0, 193.6, 0.0, 195.6, 0.0, 193.1, 195.7, 0.0, 0.0, 197.4, 192.5, 0.0, 0.0, 0.0, 192.6, 0.0, 195.8, 0.0, 193.2, 0.0, 193.6, 0.0, 193.0, 0.0, 193.5, 0.0, 193.6, 0.0, 193.2, 195.6, 0.0, 0.0, 197.4, 193.7, 0.0, 193.2, 0.0, 193.7, 0.0, 195.8, 0.0, 193.4, 0.0, 193.3, 0.0, 197.4, 0.0, 193.4, 0.0, 0.0, 0.0, 193.4, 193.7, 0.0, 0.0, 195.8, 193.6, 0.0, 197.5, 0.0, 193.0, 0.0, 193.4, 0.0, 193.7, 0.0, 197.5, 0.0, 197.4, 0.0, 193.3, 0.0, 0.0, 0.0, 193.3, 195.9, 0.0, 0.0, 197.1, 193.3, 0.0, 194.4, 0.0, 194.2, 0.0, 190.6, 0.0, 193.5, 0.0, 193.7, 0.0, 197.5, 0.0, 193.7, 192.8, 0.0, 0.0, 194.4, 195.7, 0.0, 0.0, 197.5, 194.4, 0.0, 193.7, 0.0, 194.4, 0.0, 195.6, 0.0, 193.7, 0.0, 194.2, 0.0, 197.5, 0.0, 193.7, 0.0, 192.9, 0.0, 193.5, 195.8, 0.0, 0.0, 197.5, 193.4, 0.0, 192.8, 0.0, 193.7, 0.0, 195.8, 0.0, 193.7, 0.0, 194.2, 0.0, 197.5, 0.0, 193.6, 0.0, 193.4, 0.0, 193.4, 195.7, 0.0, 0.0, 197.5, 193.4, 0.0, 193.3, 0.0, 193.7, 0.0, 192.8, 0.0, 193.5, 0.0, 193.5, 0.0, 197.5, 0.0, 193.5, 192.6, 0.0, 0.0, 193.6, 192.1, 0.0, 0.0, 195.6, 193.6, 0.0, 197.4, 0.0, 193.3, 0.0, 193.3, 0.0, 193.8, 0.0, 193.6, 0.0, 195.7, 0.0, 193.3, 197.5, 0.0, 0.0, 193.5, 192.6, 0.0, 0.0, 195.8, 193.3, 0.0, 197.5, 0.0, 192.3, 0.0, 194.1, 0.0, 193.7, 0.0, 193.0, 0.0, 195.8, 0.0, 193.3, 197.5, 0.0, 0.0, 192.6, 193.4, 0.0, 0.0, 194.1, 193.6, 0.0, 196.0, 0.0, 193.5, 0.0, 193.2, 0.0, 191.6, 0.0, 193.8, 0.0, 195.1, 0.0, 193.8, 195.7, 0.0, 0.0, 197.3, 193.3, 0.0, 0.0, 0.0, 194.4, 0.0, 195.7, 0.0, 197.0, 0.0, 193.8, 0.0, 192.8, 0.0, 193.6, 0.0, 195.5, 0.0, 193.4, 195.7, 0.0, 0.0, 197.4, 193.4, 0.0, 0.0, 193.2, 193.7, 0.0, 195.6, 0.0, 193.7, 0.0, 193.6, 0.0, 192.4, 0.0, 193.8, 0.0, 197.5, 0.0, 193.5, 197.4, 0.0, 0.0, 195.4, 193.4, 0.0, 0.0, 193.4, 193.6, 0.0, 195.8, 0.0, 193.7, 0.0, 193.7, 0.0, 193.0, 0.0, 197.5, 0.0, 195.9, 0.0, 193.1, 197.5, 0.0, 0.0, 194.9, 193.4, 0.0, 0.0, 195.0, 197.5, 0.0, 195.6, 0.0, 194.5, 0.0, 194.3, 0.0, 194.6, 0.0, 194.2, 0.0, 195.9, 0.0, 194.3, 197.5, 0.0, 0.0, 194.2, 195.1, 0.0, 0.0, 197.4, 193.5, 0.0, 197.7, 0.0, 193.6, 0.0, 193.4, 0.0, 193.4, 0.0, 193.4, 0.0, 197.5, 0.0, 193.7, 193.0, 0.0, 0.0, 193.4, 193.4, 0.0, 0.0, 195.5, 193.4, 0.0, 197.5, 0.0, 192.6, 0.0, 193.4, 0.0, 193.5, 0.0, 193.5, 0.0, 195.9, 0.0, 193.7, 197.5, 0.0, 0.0, 193.0, 193.4, 0.0, 0.0, 194.0, 194.2, 0.0, 197.5, 0.0, 193.5, 0.0, 191.8, 0.0, 194.4, 0.0, 193.7, 0.0, 197.5, 0.0, 195.5, 196.9, 0.0, 0.0, 194.3, 195.4, 0.0, 0.0, 195.5, 193.5, 0.0, 197.5, 0.0, 194.4, 0.0, 193.6, 0.0, 194.3, 0.0, 194.5, 0.0, 197.5, 0.0, 194.3, 0.0, 194.3, 0.0, 194.2, 195.7, 0.0, 0.0, 197.4, 194.4, 0.0, 0.0, 194.4, 194.3, 0.0, 195.9, 0.0, 194.3, 0.0, 193.8, 0.0, 197.3, 0.0, 194.5, 0.0, 197.5, 0.0, 194.3, 196.9, 0.0, 0.0, 194.2, 195.6, 0.0, 0.0, 197.4, 194.4, 0.0, 195.1, 0.0, 194.5, 0.0, 195.6, 0.0, 194.5, 0.0, 194.2, 0.0, 197.5, 0.0, 194.4, 0.0, 194.3, 0.0, 194.5, 195.9, 0.0, 0.0, 197.5, 194.2, 0.0, 0.0, 194.4, 194.5, 0.0, 195.9, 0.0, 194.2, 0.0, 193.8, 0.0, 194.2, 0.0, 193.1, 0.0, 195.8, 0.0, 194.2, 197.5, 0.0, 0.0, 194.3, 194.0, 0.0, 0.0, 195.6, 194.2, 0.0, 197.5, 0.0, 193.8, 0.0, 193.7, 0.0, 194.2, 0.0, 194.4, 0.0, 197.5, 0.0, 194.5, 194.5, 0.0, 0.0, 194.4, 195.8, 0.0, 0.0, 195.5, 194.2, 0.0, 197.6, 0.0, 194.3, 0.0, 195.7, 0.0, 193.5, 0.0, 194.1, 0.0, 197.4, 0.0, 194.1, 0.0, 194.0, 0.0, 194.2, 195.4, 0.0, 0.0, 197.1, 194.3, 0.0, 196.9, 0.0, 194.0, 0.0, 193.9, 0.0, 194.2, 0.0, 194.2, 0.0, 197.5, 0.0, 193.5, 195.5, 0.0, 0.0, 193.6, 193.5, 0.0, 0.0, 195.7, 193.6, 0.0, 197.5, 0.0, 193.3, 0.0, 193.4, 0.0, 193.5, 0.0, 191.2, 0.0, 197.5, 0.0, 193.0, 197.5, 0.0, 0.0, 193.6, 193.1, 0.0, 0.0, 195.5, 193.3, 0.0, 197.5, 0.0, 193.6, 0.0, 193.3, 0.0, 194.1, 0.0, 193.6, 0.0, 195.9, 0.0, 193.4, 197.5, 0.0, 0.0, 193.5, 196.0, 0.0, 0.0, 195.5, 193.5, 0.0, 197.5, 0.0, 193.0, 0.0, 193.7, 0.0, 193.4, 0.0, 193.6, 0.0, 197.5, 0.0, 193.7, 195.6, 0.0, 0.0, 193.7, 193.7, 0.0, 0.0, 195.8, 193.5, 0.0, 197.5, 0.0, 193.5, 0.0, 193.7, 0.0, 192.2, 0.0, 193.4, 0.0, 197.5, 0.0, 193.7, 194.6, 0.0, 0.0, 193.5, 193.5, 0.0, 0.0, 193.4, 193.7, 0.0, 197.5, 0.0, 193.6, 0.0, 193.6, 0.0, 193.5, 0.0, 192.2, 0.0, 195.5, 0.0, 192.4, 197.5, 0.0, 0.0, 193.1, 193.6, 0.0, 0.0, 193.5, 193.7, 0.0, 195.7, 0.0, 193.7, 0.0, 197.5, 0.0, 194.0, 0.0, 192.0, 0.0, 195.8, 0.0, 193.5, 195.7, 0.0, 0.0, 197.5, 193.6, 0.0, 0.0, 0.0, 193.6, 0.0, 195.9, 0.0, 192.8, 0.0, 193.4, 0.0, 195.1, 0.0, 192.6, 0.0, 191.9, 0.0, 193.4, 0.0, 193.4, 0.0, 197.5, 0.0, 193.7, 195.5, 0.0, 0.0, 194.4, 195.7, 0.0, 0.0, 197.5, 197.5, 0.0, 195.8, 0.0, 193.6, 0.0, 193.9, 0.0, 194.3, 0.0, 194.1, 0.0, 197.4, 0.0, 195.8, 0.0, 194.4, 0.0, 194.2, 195.8, 0.0, 0.0, 197.5, 193.4, 0.0, 0.0, 195.7, 193.4, 0.0, 197.5, 0.0, 193.4, 0.0, 193.4, 0.0, 193.3, 0.0, 193.5, 0.0, 195.7, 0.0, 194.0, 197.5, 0.0, 0.0, 195.8, 193.5, 0.0, 0.0, 193.0, 193.5, 0.0, 195.6, 0.0], [0.0, 0.0, 194.4, 0.0, 0.0, 195.2, 193.4, 0.0, 195.6, 0.0, 194.1, 0.0, 195.0, 0.0, 191.9, 0.0, 192.9, 0.0, 0.0, 0.0, 193.4, 195.6, 0.0, 0.0, 197.5, 193.4, 0.0, 197.2, 0.0, 193.7, 0.0, 193.2, 0.0, 193.4, 0.0, 193.6, 0.0, 197.5, 0.0, 193.2, 193.5, 0.0, 0.0, 192.7, 191.9, 0.0, 0.0, 194.2, 192.6, 0.0, 195.9, 0.0, 193.5, 0.0, 192.8, 0.0, 192.7, 0.0, 193.5, 0.0, 192.2, 0.0, 193.7, 195.2, 0.0, 0.0, 197.5, 193.6, 0.0, 195.2, 0.0, 193.5, 0.0, 192.4, 0.0, 192.8, 0.0, 193.5, 0.0, 197.5, 0.0, 193.8, 197.5, 0.0, 0.0, 193.7, 195.8, 0.0, 0.0, 197.3, 193.5, 0.0, 197.5, 0.0, 193.8, 0.0, 193.0, 0.0, 192.8, 0.0, 193.6, 0.0, 195.2, 0.0, 194.4, 195.6, 0.0, 0.0, 193.4, 193.4, 0.0, 0.0, 195.9, 193.3, 0.0, 197.5, 0.0, 197.5, 0.0, 193.1, 0.0, 193.6, 0.0, 193.7, 0.0, 197.5, 0.0, 193.5, 193.0, 0.0, 0.0, 193.5, 195.8, 0.0, 0.0, 195.7, 194.1, 0.0, 197.5, 0.0, 194.3, 0.0, 194.0, 0.0, 194.1, 0.0, 194.3, 0.0, 197.3, 0.0, 194.3, 0.0, 195.6, 0.0, 194.3, 197.5, 0.0, 0.0, 194.1, 193.5, 0.0, 0.0, 191.5, 193.7, 0.0, 197.5, 0.0, 193.6, 0.0, 193.5, 0.0, 193.3, 0.0, 193.7, 0.0, 195.6, 0.0, 195.8, 196.8, 0.0, 0.0, 195.2, 194.3, 0.0, 0.0, 195.8, 193.5, 0.0, 197.5, 0.0, 197.5, 0.0, 195.7, 0.0, 192.6, 0.0, 193.5, 0.0, 195.6, 0.0, 193.1, 197.5, 0.0, 0.0, 193.9, 193.2, 0.0, 0.0, 195.7, 193.6, 0.0, 197.5, 0.0, 193.6, 0.0, 193.4, 0.0, 193.5, 0.0, 197.5, 0.0, 197.5, 0.0, 192.9, 192.9, 0.0, 0.0, 193.3, 196.0, 0.0, 0.0, 195.6, 197.5, 0.0, 192.2, 0.0, 193.3, 0.0, 193.0, 0.0, 193.7, 0.0, 192.4, 0.0, 197.5, 0.0, 193.6, 192.6, 0.0, 0.0, 193.3, 192.2, 0.0, 0.0, 195.9, 193.4, 0.0, 197.5, 0.0, 193.5, 0.0, 193.4, 0.0, 194.1, 0.0, 193.7, 0.0, 195.9, 0.0, 194.5, 196.8, 0.0, 0.0, 194.2, 195.5, 0.0, 0.0, 195.8, 194.1, 0.0, 197.5, 0.0, 194.3, 0.0, 195.4, 0.0, 194.5, 0.0, 197.5, 0.0, 193.9, 0.0, 194.4, 0.0, 195.3, 0.0, 194.4, 197.5, 0.0, 0.0, 193.7, 194.3, 0.0, 0.0, 195.6, 194.3, 0.0, 197.5, 0.0, 194.1, 0.0, 194.4, 0.0, 195.0, 0.0, 194.2, 0.0, 197.5, 0.0, 194.4, 194.0, 0.0, 0.0, 194.4, 195.6, 0.0, 0.0, 197.5, 194.5, 0.0, 194.3, 0.0, 194.5, 0.0, 194.6, 0.0, 193.7, 0.0, 194.3, 0.0, 193.4, 0.0, 194.4, 0.0, 195.6, 0.0, 194.0, 197.5, 0.0, 0.0, 193.5, 193.9, 0.0, 0.0, 195.9, 194.2, 0.0, 195.6, 0.0, 194.2, 0.0, 194.6, 0.0, 194.1, 0.0, 194.3, 0.0, 197.5, 0.0, 194.3, 192.9, 0.0, 0.0, 194.2, 194.6, 0.0, 0.0, 195.9, 194.2, 0.0, 197.5, 0.0, 194.5, 0.0, 193.9, 0.0, 194.2, 0.0, 195.6, 0.0, 197.5, 0.0, 194.5, 0.0, 195.8, 0.0, 193.4, 197.5, 0.0, 0.0, 194.4, 191.1, 0.0, 0.0, 195.8, 194.3, 0.0, 197.5, 0.0, 194.2, 0.0, 194.0, 0.0, 194.4, 0.0, 194.5, 0.0, 197.5, 0.0, 194.3, 194.5, 0.0, 0.0, 194.4, 195.6, 0.0, 0.0, 197.5, 194.4, 0.0, 193.2, 0.0, 194.1, 0.0, 195.6, 0.0, 194.0, 0.0, 194.2, 0.0, 193.7, 0.0, 194.4, 0.0, 195.7, 0.0, 194.5, 197.5, 0.0, 0.0, 193.3, 194.1, 0.0, 0.0, 194.2, 194.2, 0.0, 195.9, 0.0, 194.0, 0.0, 194.2, 0.0, 194.5, 0.0, 194.3, 0.0, 195.9, 0.0, 194.3, 197.3, 0.0, 0.0, 194.1, 192.8, 0.0, 0.0, 195.6, 194.0, 0.0, 197.5, 0.0, 194.3, 0.0, 194.0, 0.0, 194.4, 0.0, 194.3, 0.0, 197.5, 0.0, 194.5, 0.0, 194.1, 0.0, 194.4, 195.8, 0.0, 0.0, 197.5, 197.5, 0.0, 0.0, 195.6, 194.0, 0.0, 197.5, 0.0, 194.2, 0.0, 194.4, 0.0, 194.5, 0.0, 194.4, 0.0, 197.5, 0.0, 194.3, 194.0, 0.0, 0.0, 194.4, 195.5, 0.0, 0.0, 197.5, 194.5, 0.0, 193.4, 0.0, 194.2, 0.0, 195.8, 0.0, 194.3, 0.0, 194.5, 0.0, 192.7, 0.0, 194.5, 0.0, 195.4, 0.0, 194.5, 195.5, 0.0, 0.0, 197.5, 194.0, 0.0, 0.0, 193.6, 194.4, 0.0, 195.6, 0.0, 194.3, 0.0, 194.2, 0.0, 194.4, 0.0, 194.5, 0.0, 195.8, 0.0, 194.2, 197.5, 0.0, 0.0, 194.5, 193.8, 0.0, 0.0, 195.8, 194.6, 0.0, 197.5, 0.0, 194.2, 0.0, 194.6, 0.0, 194.4, 0.0, 197.5, 0.0, 197.4, 0.0, 197.4, 0.0, 195.7, 0.0, 194.2, 197.5, 0.0, 0.0, 195.9, 194.4, 0.0, 0.0, 195.8, 194.1, 0.0, 195.5, 0.0, 193.9, 0.0, 194.4, 0.0, 194.1, 0.0, 194.4, 0.0, 195.4, 0.0, 194.2, 197.5, 0.0, 0.0, 194.4, 194.4, 0.0, 0.0, 195.4, 194.2, 0.0, 197.5, 0.0, 194.4, 0.0, 194.3, 0.0, 193.9, 0.0, 194.4, 0.0, 197.5, 0.0, 197.6, 0.0, 194.0, 0.0, 197.5, 195.7, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 193.9, 194.2, 0.0, 195.4, 0.0, 197.3, 0.0, 194.3, 0.0, 194.6, 0.0, 194.1, 0.0, 195.4, 0.0, 194.4, 197.5, 0.0, 0.0, 194.3, 195.9, 0.0, 0.0, 197.5, 197.5, 0.0, 193.9, 0.0, 197.6, 0.0, 195.7, 0.0, 194.4, 0.0, 194.0, 0.0, 193.8, 0.0, 194.0, 0.0, 197.5, 0.0, 194.2, 197.5, 0.0, 0.0, 194.4, 194.4, 0.0, 0.0, 195.5, 194.2, 0.0, 197.5, 0.0, 194.2, 0.0, 194.4, 0.0, 194.5, 0.0, 194.5, 0.0, 197.5, 0.0, 194.5, 193.9, 0.0, 0.0, 194.3, 195.8, 0.0, 0.0, 197.5, 197.5, 0.0, 0.0, 194.5, 194.0, 0.0, 195.6, 0.0, 195.4, 0.0, 194.2, 0.0, 194.4, 0.0, 194.2, 0.0, 195.7, 0.0, 194.5, 197.5, 0.0, 0.0, 194.4, 194.0, 0.0, 0.0, 195.4, 193.5, 0.0, 197.5, 0.0, 194.2, 0.0, 194.4, 0.0, 194.2, 0.0, 194.4, 0.0, 197.5, 0.0, 194.5, 0.0, 194.4, 0.0, 194.2, 195.5, 0.0, 0.0, 197.5, 194.0, 0.0, 0.0, 194.2, 193.9, 0.0, 195.8, 0.0, 194.0, 0.0, 194.5, 0.0, 194.6, 0.0, 194.4, 0.0, 195.6, 0.0, 194.4, 197.5, 0.0, 0.0, 193.4, 194.4, 0.0, 0.0, 195.6, 194.3, 0.0, 197.5, 0.0, 194.5, 0.0, 194.4, 0.0, 194.3, 0.0, 197.5, 0.0, 197.5, 0.0, 193.7, 0.0, 0.0, 0.0, 194.1, 195.2, 0.0, 0.0, 197.5, 193.9, 0.0, 194.7, 0.0, 194.3, 0.0, 197.6, 0.0, 194.4, 0.0, 194.2, 0.0, 194.5, 0.0, 194.1, 0.0, 195.5, 0.0, 194.5, 197.5, 0.0, 0.0, 194.5, 193.7, 0.0, 0.0, 195.5, 194.2, 0.0, 197.5, 0.0, 194.3, 0.0, 194.3, 0.0, 195.4, 0.0, 194.2, 0.0, 197.5, 0.0, 194.2, 194.3, 0.0, 0.0, 194.2, 195.7, 0.0, 0.0, 197.5, 194.5, 0.0, 0.0, 0.0, 194.3, 0.0, 195.4, 0.0, 194.4, 0.0, 194.1, 0.0, 194.4, 0.0, 194.4, 0.0, 195.3, 0.0, 194.5, 197.4, 0.0, 0.0, 193.7, 194.5, 0.0, 0.0, 195.6, 194.5, 0.0, 197.5, 0.0, 194.2, 0.0, 194.4, 0.0, 194.4, 0.0, 194.5, 0.0, 197.5, 0.0, 194.5, 193.8, 0.0, 0.0, 194.3, 195.6, 0.0, 0.0, 197.5, 194.2, 0.0, 194.2, 0.0, 194.3, 0.0, 195.5, 0.0, 194.4, 0.0, 194.3, 0.0, 194.0, 0.0, 194.5, 0.0, 195.5, 0.0, 194.4], [0.0, 196.0, 0.0, 0.0, 197.4, 194.4, 0.0, 0.0, 194.1, 194.4, 0.0, 195.6, 0.0, 194.3, 0.0, 194.4, 0.0, 194.4, 0.0, 194.3, 0.0, 195.6, 0.0, 194.5, 197.5, 0.0, 0.0, 193.7, 194.1, 0.0, 0.0, 195.8, 194.5, 0.0, 197.5, 0.0, 194.2, 0.0, 194.1, 0.0, 194.5, 0.0, 194.5, 0.0, 197.5, 0.0, 194.3, 193.8, 0.0, 0.0, 193.8, 195.7, 0.0, 0.0, 197.5, 193.9, 0.0, 194.2, 0.0, 194.5, 0.0, 195.7, 0.0, 193.9, 0.0, 194.3, 0.0, 192.9, 0.0, 194.2, 0.0, 194.0, 0.0, 194.5, 195.0, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 197.5, 193.9, 0.0, 197.5, 0.0, 194.3, 0.0, 194.4, 0.0, 194.2, 0.0, 194.2, 0.0, 197.5, 0.0, 194.3, 194.0, 0.0, 0.0, 194.2, 195.7, 0.0, 0.0, 197.5, 194.5, 0.0, 193.2, 0.0, 194.4, 0.0, 195.6, 0.0, 194.2, 0.0, 194.4, 0.0, 194.4, 0.0, 194.4, 0.0, 195.7, 0.0, 197.5, 197.5, 0.0, 0.0, 192.7, 193.8, 0.0, 0.0, 195.7, 194.4, 0.0, 197.5, 0.0, 194.4, 0.0, 194.4, 0.0, 194.4, 0.0, 194.3, 0.0, 197.5, 0.0, 194.1, 0.0, 0.0, 0.0, 194.3, 195.7, 0.0, 0.0, 197.5, 194.2, 0.0, 194.5, 0.0, 194.6, 0.0, 194.8, 0.0, 193.9, 0.0, 194.5, 0.0, 195.5, 0.0, 194.5, 0.0, 195.4, 0.0, 194.5, 197.5, 0.0, 0.0, 194.6, 194.3, 0.0, 0.0, 195.4, 194.4, 0.0, 197.5, 0.0, 194.3, 0.0, 194.4, 0.0, 194.1, 0.0, 194.1, 0.0, 197.5, 0.0, 194.5, 193.9, 0.0, 0.0, 194.4, 195.7, 0.0, 0.0, 197.5, 194.5, 0.0, 193.9, 0.0, 194.4, 0.0, 195.8, 0.0, 194.2, 0.0, 194.5, 0.0, 194.3, 0.0, 194.5, 0.0, 195.6, 0.0, 194.3, 197.5, 0.0, 0.0, 194.2, 194.2, 0.0, 0.0, 193.8, 193.0, 0.0, 194.5, 0.0, 192.7, 0.0, 196.8, 0.0, 195.8, 0.0, 196.5, 196.7, 0.0, 0.0, 196.9, 197.3, 0.0, 0.0, 196.1, 196.7, 0.0, 0.0, 194.6, 195.4, 0.0, 196.8, 0.0, 192.9, 0.0, 193.0, 0.0, 196.8, 0.0, 192.6, 194.8, 0.0, 0.0, 194.3, 192.8, 0.0, 192.3, 0.0, 193.1, 0.0, 192.8, 0.0, 194.3, 0.0, 194.5, 0.0, 197.5, 0.0, 192.7, 196.0, 0.0, 0.0, 195.9, 195.8, 0.0, 0.0, 197.5, 194.4, 0.0, 193.7, 0.0, 194.5, 0.0, 195.2, 0.0, 194.0, 0.0, 194.4, 0.0, 197.5, 0.0, 194.5, 0.0, 195.7, 0.0, 194.1, 197.5, 0.0, 0.0, 194.2, 194.4, 0.0, 0.0, 195.9, 194.3, 0.0, 197.5, 0.0, 194.4, 0.0, 194.0, 0.0, 194.2, 0.0, 194.4, 0.0, 197.4, 0.0, 194.5, 0.0, 195.6, 0.0, 194.5, 195.9, 0.0, 0.0, 197.5, 194.1, 0.0, 0.0, 193.8, 193.4, 0.0, 195.8, 0.0, 194.2, 0.0, 193.7, 0.0, 194.4, 0.0, 194.3, 0.0, 195.5, 0.0, 194.4, 197.5, 0.0, 0.0, 194.3, 193.9, 0.0, 0.0, 195.7, 194.2, 0.0, 197.5, 0.0, 194.3, 0.0, 194.4, 0.0, 194.2, 0.0, 194.0, 0.0, 197.5, 0.0, 194.3, 0.0, 0.0, 0.0, 193.9, 197.6, 0.0, 0.0, 197.5, 194.5, 0.0, 0.0, 194.1, 194.3, 0.0, 195.7, 0.0, 194.4, 0.0, 194.3, 0.0, 193.7, 0.0, 194.4, 0.0, 195.7, 0.0, 194.2, 197.5, 0.0, 0.0, 194.2, 194.3, 0.0, 0.0, 195.9, 194.4, 0.0, 197.4, 0.0, 193.7, 0.0, 194.1, 0.0, 194.3, 0.0, 194.4, 0.0, 197.5, 0.0, 194.0, 0.0, 194.0, 0.0, 194.3, 195.8, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 0.0, 197.5, 0.0, 197.5, 0.0, 194.3, 0.0, 194.0, 0.0, 194.2, 0.0, 194.2, 0.0, 197.5, 0.0, 194.2, 196.3, 0.0, 0.0, 194.2, 195.6, 0.0, 0.0, 197.5, 194.6, 0.0, 193.8, 0.0, 194.1, 0.0, 195.9, 0.0, 194.6, 0.0, 194.3, 0.0, 194.4, 0.0, 194.5, 0.0, 195.7, 0.0, 194.5, 197.5, 0.0, 0.0, 194.2, 194.0, 0.0, 0.0, 195.8, 194.2, 0.0, 197.5, 0.0, 194.1, 0.0, 194.0, 0.0, 194.2, 0.0, 194.1, 0.0, 197.5, 0.0, 194.3, 0.0, 194.2, 0.0, 194.3, 195.8, 0.0, 0.0, 197.5, 194.6, 0.0, 0.0, 194.3, 194.0, 0.0, 197.5, 0.0, 194.5, 0.0, 194.4, 0.0, 195.6, 0.0, 194.3, 0.0, 195.8, 0.0, 194.4, 197.5, 0.0, 0.0, 194.3, 194.5, 0.0, 0.0, 196.0, 193.3, 0.0, 197.5, 0.0, 194.2, 0.0, 194.2, 0.0, 195.8, 0.0, 194.4, 0.0, 197.5, 0.0, 194.2, 0.0, 193.2, 0.0, 194.2, 195.9, 0.0, 0.0, 197.5, 194.5, 0.0, 0.0, 194.2, 194.2, 0.0, 195.9, 0.0, 194.4, 0.0, 194.5, 0.0, 194.2, 0.0, 194.6, 0.0, 195.3, 0.0, 194.4, 197.5, 0.0, 0.0, 194.1, 194.2, 0.0, 0.0, 195.6, 194.2, 0.0, 197.5, 0.0, 195.7, 0.0, 194.5, 0.0, 194.4, 0.0, 194.3, 0.0, 197.5, 0.0, 194.2, 193.7, 0.0, 0.0, 194.3, 195.3, 0.0, 0.0, 197.5, 194.4, 0.0, 193.6, 0.0, 194.4, 0.0, 195.3, 0.0, 194.1, 0.0, 195.6, 0.0, 195.8, 0.0, 194.5, 0.0, 195.6, 0.0, 194.3, 197.5, 0.0, 0.0, 194.2, 194.5, 0.0, 0.0, 195.6, 194.1, 0.0, 197.5, 0.0, 194.9, 0.0, 194.4, 0.0, 194.4, 0.0, 194.4, 0.0, 197.4, 0.0, 194.6, 193.8, 0.0, 0.0, 194.4, 195.8, 0.0, 0.0, 197.4, 193.5, 0.0, 193.8, 0.0, 194.5, 0.0, 195.4, 0.0, 194.3, 0.0, 194.4, 0.0, 194.5, 0.0, 194.2, 0.0, 195.6, 0.0, 194.3, 197.4, 0.0, 0.0, 194.4, 194.2, 0.0, 0.0, 195.6, 194.5, 0.0, 197.5, 0.0, 194.2, 0.0, 195.6, 0.0, 194.6, 0.0, 194.6, 0.0, 197.5, 0.0, 194.2, 194.1, 0.0, 0.0, 194.4, 195.9, 0.0, 0.0, 197.5, 194.2, 0.0, 194.0, 0.0, 194.5, 0.0, 195.7, 0.0, 193.6, 0.0, 194.4, 0.0, 193.5, 0.0, 194.7, 0.0, 196.0, 0.0, 194.3, 197.5, 0.0, 0.0, 194.6, 194.6, 0.0, 0.0, 195.8, 194.5, 0.0, 197.4, 0.0, 193.8, 0.0, 194.3, 0.0, 194.4, 0.0, 194.6, 0.0, 197.5, 0.0, 194.0, 192.7, 0.0, 0.0, 194.4, 195.7, 0.0, 0.0, 196.6, 194.5, 0.0, 193.7, 0.0, 194.2, 0.0, 197.5, 0.0, 194.5, 0.0, 194.2, 0.0, 193.1, 0.0, 194.5, 0.0, 195.8, 0.0, 194.5, 195.8, 0.0, 0.0, 197.5, 194.6, 0.0, 0.0, 193.9, 194.3, 0.0, 195.9, 0.0, 194.4, 0.0, 194.4, 0.0, 194.4, 0.0, 197.5, 0.0, 195.6, 0.0, 194.5, 197.5, 0.0, 0.0, 194.3, 194.0, 0.0, 0.0, 197.5, 194.6, 0.0, 193.7, 0.0, 194.4, 0.0, 195.6, 0.0, 194.3, 0.0, 194.3, 0.0, 197.5, 0.0, 193.6, 0.0, 195.3, 0.0, 194.5, 197.5, 0.0, 0.0, 193.7, 194.4, 0.0, 0.0, 194.8, 194.6, 0.0, 197.4, 0.0, 194.3, 0.0, 194.5, 0.0, 193.9, 0.0, 194.4, 0.0, 197.5, 0.0, 193.7, 194.2, 0.0, 0.0, 194.2, 195.9, 0.0, 0.0, 197.5, 194.6, 0.0, 195.3, 0.0, 194.2, 0.0, 193.7, 0.0, 194.4, 0.0, 194.3, 0.0, 197.4, 0.0, 194.3, 0.0, 194.5, 0.0, 194.4, 195.8, 0.0, 0.0, 196.8, 197.0, 0.0, 0.0, 194.4, 194.4, 0.0, 195.5, 0.0, 193.2, 0.0, 194.4, 0.0, 194.4, 0.0, 194.4, 0.0, 195.9, 0.0, 194.6, 197.5, 0.0, 0.0, 194.2, 194.1, 0.0, 0.0, 195.5, 194.3, 0.0, 197.4, 0.0, 194.3, 0.0, 194.2, 0.0, 194.3, 0.0, 194.3, 0.0, 197.5, 0.0, 197.4, 0.0, 0.0, 0.0, 194.3, 195.6, 0.0, 0.0, 197.5, 194.3, 0.0, 194.1, 0.0, 194.4, 0.0, 195.6, 0.0, 194.6, 0.0, 197.2, 0.0, 194.1, 0.0, 194.3, 0.0, 195.3, 0.0, 194.3, 197.5, 0.0, 0.0, 194.4, 194.2, 0.0, 0.0, 195.7, 194.1, 0.0, 197.5, 0.0, 194.3, 0.0, 194.5, 0.0, 194.4, 0.0, 194.3, 0.0, 197.5, 0.0, 197.5], [0.0, 0.0, 0.0, 193.6, 0.0, 193.3, 0.0, 192.9, 0.0, 195.4, 0.0, 193.5, 197.5, 0.0, 0.0, 193.5, 193.4, 0.0, 0.0, 195.8, 193.5, 0.0, 197.5, 0.0, 194.7, 0.0, 193.6, 0.0, 193.5, 0.0, 197.5, 0.0, 196.6, 0.0, 193.1, 193.3, 0.0, 0.0, 194.2, 195.6, 0.0, 0.0, 197.5, 194.4, 0.0, 193.2, 0.0, 193.3, 0.0, 195.7, 0.0, 194.1, 0.0, 194.3, 0.0, 197.5, 0.0, 194.5, 0.0, 193.4, 0.0, 193.5, 195.0, 0.0, 0.0, 197.4, 194.7, 0.0, 0.0, 191.3, 191.7, 0.0, 195.6, 0.0, 193.6, 0.0, 193.6, 0.0, 193.7, 0.0, 193.3, 0.0, 192.3, 0.0, 193.5, 195.7, 0.0, 0.0, 197.5, 193.1, 0.0, 198.3, 0.0, 193.2, 0.0, 195.8, 0.0, 193.4, 0.0, 197.5, 0.0, 192.6, 0.0, 197.5, 0.0, 194.5, 0.0, 193.6, 197.5, 0.0, 0.0, 193.4, 193.4, 0.0, 0.0, 195.8, 197.5, 0.0, 197.5, 0.0, 193.5, 0.0, 196.4, 0.0, 193.5, 0.0, 193.1, 0.0, 197.5, 0.0, 193.6, 192.3, 0.0, 0.0, 193.6, 195.5, 0.0, 0.0, 197.5, 192.3, 0.0, 193.3, 0.0, 197.5, 0.0, 195.9, 0.0, 192.7, 0.0, 193.0, 0.0, 197.5, 0.0, 192.8, 0.0, 0.0, 0.0, 193.7, 192.1, 0.0, 0.0, 197.5, 193.5, 0.0, 0.0, 194.8, 193.1, 0.0, 197.5, 0.0, 197.5, 0.0, 195.7, 0.0, 193.6, 0.0, 193.1, 0.0, 197.5, 0.0, 191.4, 197.5, 0.0, 0.0, 193.3, 195.6, 0.0, 0.0, 197.5, 193.4, 0.0, 0.0, 0.0, 192.5, 0.0, 195.7, 0.0, 194.0, 0.0, 193.6, 0.0, 191.9, 0.0, 194.4, 0.0, 196.6, 0.0, 197.3, 197.7, 0.0, 0.0, 194.2, 193.3, 0.0, 0.0, 197.2, 193.3, 0.0, 197.4, 0.0, 193.5, 0.0, 197.5, 0.0, 193.6, 0.0, 190.2, 0.0, 197.5, 0.0, 193.6, 192.2, 0.0, 0.0, 193.5, 197.5, 0.0, 0.0, 197.5, 197.5, 0.0, 193.3, 0.0, 193.6, 0.0, 195.7, 0.0, 193.4, 0.0, 193.4, 0.0, 197.9, 0.0, 193.0, 193.6, 0.0, 0.0, 194.2, 195.7, 0.0, 0.0, 197.5, 194.3, 0.0, 194.2, 0.0, 194.5, 0.0, 195.5, 0.0, 194.2, 0.0, 194.4, 0.0, 194.2, 0.0, 197.4, 0.0, 195.5, 0.0, 193.6, 197.5, 0.0, 0.0, 197.4, 193.3, 0.0, 0.0, 195.8, 193.4, 0.0, 197.5, 0.0, 195.7, 0.0, 193.3, 0.0, 192.4, 0.0, 195.6, 0.0, 197.5, 0.0, 192.7, 195.0, 0.0, 0.0, 196.4, 197.4, 0.0, 0.0, 197.5, 193.6, 0.0, 193.9, 0.0, 193.3, 0.0, 192.0, 0.0, 193.3, 0.0, 193.6, 0.0, 197.5, 0.0, 195.7, 193.2, 0.0, 0.0, 197.5, 193.4, 0.0, 0.0, 197.5, 197.5, 0.0, 193.4, 0.0, 193.3, 0.0, 194.7, 0.0, 194.2, 0.0, 193.7, 0.0, 197.5, 0.0, 193.4, 0.0, 0.0, 0.0, 195.7, 195.8, 0.0, 0.0, 197.3, 193.6, 0.0, 0.0, 191.9, 194.1, 0.0, 194.1, 0.0, 191.2, 0.0, 197.4, 0.0, 189.6, 0.0, 197.4, 0.0, 192.2, 0.0, 193.5, 196.0, 0.0, 0.0, 197.5, 193.7, 0.0, 0.0, 195.9, 193.6, 0.0, 195.9, 0.0, 193.2, 0.0, 197.3, 0.0, 193.2, 0.0, 193.4, 0.0, 195.8, 0.0, 193.6, 197.5, 0.0, 0.0, 192.3, 193.5, 0.0, 0.0, 197.5, 193.4, 0.0, 197.5, 0.0, 193.5, 0.0, 192.0, 0.0, 197.5, 0.0, 192.6, 0.0, 195.8, 0.0, 190.6, 197.5, 0.0, 0.0, 193.4, 192.8, 0.0, 0.0, 191.2, 193.4, 0.0, 195.4, 0.0, 190.6, 0.0, 192.8, 0.0, 197.5, 0.0, 193.4, 0.0, 193.7, 0.0, 194.1, 196.0, 0.0, 0.0, 197.5, 197.5, 0.0, 0.0, 190.8, 193.6, 0.0, 197.5, 0.0, 196.6, 0.0, 192.2, 0.0, 193.4, 0.0, 196.4, 0.0, 196.0, 0.0, 193.4, 197.5, 0.0, 0.0, 196.2, 193.6, 0.0, 0.0, 195.9, 193.2, 0.0, 195.7, 0.0, 194.4, 0.0, 194.5, 0.0, 194.3, 0.0, 194.3, 0.0, 195.5, 0.0, 194.3, 197.5, 0.0, 0.0, 194.2, 191.8, 0.0, 0.0, 195.6, 196.3, 0.0, 197.5, 0.0, 193.7, 0.0, 195.7, 0.0, 193.4, 0.0, 193.6, 0.0, 197.5, 0.0, 196.9, 0.0, 197.4, 0.0, 197.2, 197.7, 0.0, 0.0, 197.2, 195.9, 0.0, 0.0, 197.5, 193.4, 0.0, 193.5, 0.0, 193.5, 0.0, 195.6, 0.0, 192.4, 0.0, 193.0, 0.0, 196.8, 0.0, 193.5, 0.0, 191.4, 0.0, 197.5, 195.6, 0.0, 0.0, 197.9, 194.4, 0.0, 0.0, 195.6, 194.2, 0.0, 197.5, 0.0, 194.5, 0.0, 194.5, 0.0, 194.1, 0.0, 194.1, 0.0, 197.5, 0.0, 194.4, 193.4, 0.0, 0.0, 194.3, 195.6, 0.0, 0.0, 197.5, 194.2, 0.0, 194.0, 0.0, 194.4, 0.0, 195.7, 0.0, 194.1, 0.0, 194.3, 0.0, 194.3, 0.0, 194.4, 0.0, 195.8, 0.0, 196.0, 197.5, 0.0, 0.0, 194.1, 194.3, 0.0, 0.0, 197.5, 195.3, 0.0, 197.6, 0.0, 197.4, 0.0, 197.7, 0.0, 194.0, 0.0, 194.5, 0.0, 194.2, 0.0, 194.3, 0.0, 195.7, 0.0, 194.4, 197.5, 0.0, 0.0, 194.2, 194.2, 0.0, 0.0, 194.5, 194.4, 0.0, 197.5, 0.0, 194.1, 0.0, 194.3, 0.0, 194.2, 0.0, 194.2, 0.0, 197.5, 0.0, 194.0, 0.0, 194.3, 0.0, 194.5, 195.9, 0.0, 0.0, 197.5, 194.3, 0.0, 0.0, 194.2, 194.4, 0.0, 195.7, 0.0, 194.5, 0.0, 194.4, 0.0, 194.3, 0.0, 194.3, 0.0, 195.5, 0.0, 193.8, 197.5, 0.0, 0.0, 194.3, 194.1, 0.0, 0.0, 195.8, 194.5, 0.0, 197.5, 0.0, 194.0, 0.0, 193.8, 0.0, 194.2, 0.0, 194.2, 0.0, 197.5, 0.0, 197.5, 0.0, 194.3, 0.0, 194.3, 195.8, 0.0, 0.0, 197.5, 193.2, 0.0, 0.0, 193.6, 194.1, 0.0, 195.8, 0.0, 193.5, 0.0, 195.4, 0.0, 195.4, 0.0, 194.5, 0.0, 195.7, 0.0, 194.3, 197.5, 0.0, 0.0, 197.5, 195.5, 0.0, 0.0, 195.9, 193.9, 0.0, 197.3, 0.0, 194.4, 0.0, 195.6, 0.0, 194.4, 0.0, 194.4, 0.0, 194.3, 0.0, 194.2, 0.0, 195.6, 0.0, 194.3, 197.5, 0.0, 0.0, 194.0, 194.3, 0.0, 0.0, 197.7, 197.4, 0.0, 0.0, 197.2, 197.3, 0.0, 197.7, 0.0, 197.3, 0.0, 195.5, 0.0, 194.5, 0.0, 194.0, 0.0, 193.8, 0.0, 194.4, 0.0, 196.0, 0.0, 194.2, 197.5, 0.0, 0.0, 193.8, 194.3, 0.0, 0.0, 195.7, 194.3, 0.0, 197.5, 0.0, 194.3, 0.0, 194.2, 0.0, 194.2, 0.0, 196.6, 0.0, 197.6, 0.0, 197.2, 0.0, 197.7, 0.0, 197.2, 194.8, 0.0, 0.0, 194.0, 195.6, 0.0, 0.0, 197.5, 196.9, 0.0, 0.0, 197.7, 196.9, 0.0, 197.7, 0.0, 197.3, 0.0, 197.7, 0.0, 194.5, 0.0, 194.4, 0.0, 194.2, 0.0, 194.5, 0.0, 195.7, 0.0, 194.2, 197.8, 0.0, 0.0, 194.2, 195.0, 0.0, 0.0, 197.5, 194.2, 0.0, 194.6, 0.0, 194.3, 0.0, 195.6, 0.0, 195.8, 0.0, 194.0, 0.0, 192.7, 0.0, 194.5, 0.0, 195.9, 0.0, 197.5, 197.5, 0.0, 0.0, 191.4, 194.4, 0.0, 0.0, 195.6, 194.5, 0.0, 197.5, 0.0, 194.3, 0.0, 194.4, 0.0, 194.2, 0.0, 194.3, 0.0, 197.5, 0.0, 194.3, 194.6, 0.0, 0.0, 194.1, 195.5, 0.0, 0.0, 195.3, 194.5, 0.0, 197.5, 0.0, 193.9, 0.0, 195.6, 0.0, 194.4, 0.0, 194.4, 0.0, 193.9, 0.0, 193.8, 0.0, 195.6, 0.0, 194.5, 197.5, 0.0, 0.0, 193.8, 194.0, 0.0, 0.0, 195.6, 194.2, 0.0, 197.5, 0.0, 194.2, 0.0, 194.5, 0.0, 195.6, 0.0, 194.3, 0.0, 197.5, 0.0, 194.2, 193.0, 0.0, 0.0, 194.3, 193.9, 0.0, 0.0, 195.6, 194.2], [0.0, 0.0, 0.0, 197.5, 194.1, 0.0, 0.0, 193.5, 194.3, 0.0, 195.6, 0.0, 194.2, 0.0, 194.4, 0.0, 194.3, 0.0, 193.6, 0.0, 195.4, 0.0, 194.3, 197.5, 0.0, 0.0, 194.3, 194.2, 0.0, 0.0, 195.7, 193.8, 0.0, 197.5, 0.0, 192.8, 0.0, 194.0, 0.0, 194.2, 0.0, 194.1, 0.0, 197.5, 0.0, 194.4, 192.7, 0.0, 0.0, 194.4, 195.6, 0.0, 0.0, 197.5, 194.6, 0.0, 193.1, 0.0, 194.2, 0.0, 193.5, 0.0, 195.2, 0.0, 195.6, 0.0, 194.4, 0.0, 194.5, 0.0, 194.2, 0.0, 194.0, 195.9, 0.0, 0.0, 197.3, 194.3, 0.0, 0.0, 195.9, 194.4, 0.0, 196.0, 0.0, 194.2, 0.0, 194.1, 0.0, 193.1, 0.0, 194.1, 0.0, 195.6, 0.0, 194.3, 197.5, 0.0, 0.0, 194.3, 194.0, 0.0, 0.0, 195.9, 197.0, 0.0, 194.4, 0.0, 195.8, 0.0, 194.4, 0.0, 194.4, 0.0, 194.3, 0.0, 194.7, 0.0, 194.4, 0.0, 195.9, 0.0, 194.1, 197.5, 0.0, 0.0, 194.3, 194.3, 0.0, 0.0, 195.7, 194.3, 0.0, 197.5, 0.0, 194.4, 0.0, 194.1, 0.0, 194.5, 0.0, 194.3, 0.0, 197.5, 0.0, 194.3, 193.6, 0.0, 0.0, 194.3, 195.7, 0.0, 0.0, 197.5, 194.5, 0.0, 194.5, 0.0, 197.5, 0.0, 195.8, 0.0, 194.2, 0.0, 194.2, 0.0, 193.7, 0.0, 194.5, 0.0, 195.8, 0.0, 194.2, 195.5, 0.0, 0.0, 194.4, 194.4, 0.0, 0.0, 195.7, 195.7, 0.0, 197.5, 0.0, 194.2, 0.0, 194.3, 0.0, 194.5, 0.0, 194.3, 0.0, 197.5, 0.0, 193.9, 193.5, 0.0, 0.0, 194.3, 197.5, 0.0, 0.0, 197.5, 194.5, 0.0, 0.0, 0.0, 194.4, 0.0, 195.6, 0.0, 193.9, 0.0, 194.5, 0.0, 193.6, 0.0, 194.4, 0.0, 195.7, 0.0, 193.2, 197.5, 0.0, 0.0, 194.3, 194.3, 0.0, 0.0, 195.6, 193.9, 0.0, 195.2, 0.0, 197.5, 0.0, 194.5, 0.0, 194.2, 0.0, 194.1, 0.0, 195.7, 0.0, 197.5, 194.0, 0.0, 0.0, 194.4, 195.7, 0.0, 0.0, 197.5, 194.5, 0.0, 193.7, 0.0, 194.2, 0.0, 196.0, 0.0, 194.0, 0.0, 194.3, 0.0, 194.7, 0.0, 194.3, 0.0, 195.6, 0.0, 194.0, 197.5, 0.0, 0.0, 195.2, 194.0, 0.0, 0.0, 195.4, 194.3, 0.0, 195.3, 0.0, 194.4, 0.0, 193.7, 0.0, 194.3, 0.0, 194.2, 0.0, 195.8, 0.0, 193.3, 197.4, 0.0, 0.0, 193.7, 194.1, 0.0, 0.0, 195.8, 194.4, 0.0, 197.5, 0.0, 194.5, 0.0, 194.2, 0.0, 193.7, 0.0, 194.4, 0.0, 197.5, 0.0, 194.5, 0.0, 193.9, 0.0, 190.7, 195.9, 0.0, 0.0, 197.4, 194.2, 0.0, 0.0, 0.0, 194.2, 0.0, 197.5, 0.0, 194.1, 0.0, 194.4, 0.0, 194.4, 0.0, 194.2, 0.0, 195.6, 0.0, 194.0, 197.5, 0.0, 0.0, 193.8, 194.4, 0.0, 0.0, 195.6, 193.6, 0.0, 197.5, 0.0, 197.5, 0.0, 194.2, 0.0, 194.5, 0.0, 194.4, 0.0, 197.5, 0.0, 194.5, 0.0, 194.2, 0.0, 194.4, 195.8, 0.0, 0.0, 197.5, 194.5, 0.0, 0.0, 194.2, 194.5, 0.0, 195.3, 0.0, 195.5, 0.0, 194.2, 0.0, 194.3, 0.0, 194.1, 0.0, 195.6, 0.0, 194.3, 197.5, 0.0, 0.0, 194.3, 194.0, 0.0, 0.0, 195.8, 194.2, 0.0, 197.4, 0.0, 194.7, 0.0, 194.2, 0.0, 195.5, 0.0, 194.3, 0.0, 197.5, 0.0, 197.5, 0.0, 197.5, 0.0, 194.4, 195.6, 0.0, 0.0, 197.4, 197.4, 0.0, 0.0, 197.6, 197.4, 0.0, 0.0, 194.3, 194.0, 0.0, 195.5, 0.0, 194.2, 0.0, 197.4, 0.0, 194.0, 0.0, 194.3, 0.0, 194.2, 0.0, 194.3, 196.5, 0.0, 0.0, 194.2, 195.4, 0.0, 0.0, 195.6, 194.4, 0.0, 197.5, 0.0, 194.4, 0.0, 194.2, 0.0, 193.9, 0.0, 194.4, 0.0, 197.5, 0.0, 194.4, 0.0, 193.7, 0.0, 194.2, 195.5, 0.0, 0.0, 197.5, 194.7, 0.0, 0.0, 194.1, 194.3, 0.0, 195.4, 0.0, 194.3, 0.0, 194.4, 0.0, 194.2, 0.0, 193.8, 0.0, 197.4, 0.0, 194.4, 0.0, 0.0, 0.0, 194.5, 195.6, 0.0, 0.0, 197.5, 193.7, 0.0, 0.0, 0.0, 194.6, 0.0, 195.4, 0.0, 194.4, 0.0, 197.4, 0.0, 194.4, 0.0, 194.6, 0.0, 195.6, 0.0, 193.8, 197.5, 0.0, 0.0, 194.3, 194.1, 0.0, 0.0, 195.7, 194.3, 0.0, 197.5, 0.0, 194.2, 0.0, 194.4, 0.0, 197.5, 0.0, 194.2, 0.0, 197.5, 0.0, 194.4, 0.0, 194.2, 0.0, 194.4, 195.5, 0.0, 0.0, 197.5, 193.9, 0.0, 194.2, 0.0, 194.2, 0.0, 195.6, 0.0, 193.7, 0.0, 194.1, 0.0, 192.8, 0.0, 194.3, 0.0, 195.6, 0.0, 194.1, 197.5, 0.0, 0.0, 193.5, 194.1, 0.0, 0.0, 195.6, 194.1, 0.0, 197.5, 0.0, 194.4, 0.0, 194.4, 0.0, 194.5, 0.0, 194.3, 0.0, 197.5, 0.0, 194.2, 193.9, 0.0, 0.0, 194.4, 195.3, 0.0, 0.0, 197.5, 194.5, 0.0, 193.1, 0.0, 195.6, 0.0, 195.3, 0.0, 194.5, 0.0, 197.4, 0.0, 197.7, 0.0, 197.0, 0.0, 197.6, 0.0, 194.4, 195.4, 0.0, 0.0, 194.3, 195.7, 0.0, 0.0, 197.5, 194.3, 0.0, 196.0, 0.0, 194.5, 0.0, 195.5, 0.0, 194.4, 0.0, 197.5, 0.0, 193.6, 0.0, 194.0, 0.0, 195.6, 0.0, 197.5, 197.5, 0.0, 0.0, 193.7, 194.0, 0.0, 0.0, 195.5, 194.2, 0.0, 197.5, 0.0, 194.3, 0.0, 194.3, 0.0, 194.4, 0.0, 194.3, 0.0, 195.8, 0.0, 194.1, 197.5, 0.0, 0.0, 194.4, 195.3, 0.0, 0.0, 197.5, 193.9, 0.0, 194.8, 0.0, 194.5, 0.0, 195.6, 0.0, 194.2, 0.0, 194.4, 0.0, 196.9, 0.0, 194.4, 0.0, 193.5, 0.0, 194.4, 195.3, 0.0, 0.0, 197.1, 194.3, 0.0, 0.0, 194.0, 194.3, 0.0, 195.0, 0.0, 193.2, 0.0, 193.9, 0.0, 194.2, 0.0, 194.1, 0.0, 195.6, 0.0, 194.2, 197.5, 0.0, 0.0, 193.8, 194.1, 0.0, 0.0, 194.4, 194.4, 0.0, 197.5, 0.0, 194.4, 0.0, 194.0, 0.0, 194.4, 0.0, 194.5, 0.0, 195.3, 0.0, 194.3, 196.8, 0.0, 0.0, 193.8, 195.7, 0.0, 0.0, 197.5, 194.4, 0.0, 193.6, 0.0, 193.8, 0.0, 195.9, 0.0, 194.3, 0.0, 194.5, 0.0, 193.6, 0.0, 194.2, 0.0, 195.3, 0.0, 194.1, 197.5, 0.0, 0.0, 193.6, 194.2, 0.0, 0.0, 195.6, 194.3, 0.0, 197.4, 0.0, 194.3, 0.0, 194.0, 0.0, 194.0, 0.0, 194.3, 0.0, 197.5, 0.0, 194.5, 193.0, 0.0, 0.0, 193.6, 195.5, 0.0, 0.0, 195.5, 194.2, 0.0, 198.0, 0.0, 194.5, 0.0, 195.0, 0.0, 194.5, 0.0, 194.4, 0.0, 197.5, 0.0, 194.4, 0.0, 195.4, 0.0, 193.1, 195.5, 0.0, 0.0, 197.5, 194.3, 0.0, 0.0, 194.4, 194.1, 0.0, 195.3, 0.0, 194.2, 0.0, 194.3, 0.0, 194.3, 0.0, 194.2, 0.0, 195.4, 0.0, 194.1, 197.5, 0.0, 0.0, 195.4, 194.1, 0.0, 0.0, 195.9, 194.5, 0.0, 197.5, 0.0, 197.5, 0.0, 195.5, 0.0, 195.6, 0.0, 193.4, 0.0, 196.2, 0.0, 194.3, 0.0, 195.8, 0.0, 194.4, 197.5, 0.0, 0.0, 194.7, 194.2, 0.0, 0.0, 195.4, 194.5, 0.0, 197.5, 0.0, 195.1, 0.0, 194.2, 0.0, 194.5, 0.0, 194.1, 0.0, 195.8, 0.0, 194.3, 197.0, 0.0, 0.0, 194.4, 194.4, 0.0, 0.0, 195.9, 194.5, 0.0, 197.5, 0.0, 194.2, 0.0, 194.2, 0.0, 194.5, 0.0, 194.5, 0.0, 197.5, 0.0, 193.6, 0.0, 0.0, 0.0, 194.3, 195.5, 0.0, 0.0, 197.5, 194.4, 0.0, 194.4, 0.0, 194.3, 0.0, 195.8, 0.0, 194.5, 0.0, 194.5, 0.0, 193.8, 0.0, 194.2, 0.0, 195.7, 0.0, 194.3, 197.5, 0.0, 0.0, 194.4, 194.3, 0.0, 0.0, 195.7]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 5.971173636800001, "std_execution_time": 0.1161122241271403, "avg_memory_usage": 2.2453125, "std_memory_usage": 0.8390912326318798, "avg_cpu_usage": 100.43322731856796, "std_cpu_usage": 0.23631630875135037}, "evalperf_canonical_results": {"execution_times": [0.000360705, 0.000367511, 0.00035107700000000004, 0.00037333600000000005, 0.00034620100000000003], "memory_usages": [1.55859375, 1.65234375, 0.6328125, 1.5703125, 1.60546875], "cpu_usages_list": [[0.0, 197.5], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.000359766, "std_execution_time": 1.1230756786610595e-05, "avg_memory_usage": 1.40390625, "std_memory_usage": 0.43259492218381246, "avg_cpu_usage": 19.75, "std_cpu_usage": 44.162342555620846}, "original_canonical_results": {"execution_times": [0.000376168, 0.000392422, 0.00035663000000000003, 0.000357482, 0.000358988], "memory_usages": [1.62109375, 1.578125, 1.62109375, 3.1875, 1.57421875], "cpu_usages_list": [[0.0, 0.0], [0.0, 193.9], [0.0, 0.0], [0.0], [0.0, 197.5]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.000368338, "std_execution_time": 1.5681999681163114e-05, "avg_memory_usage": 1.91640625, "std_memory_usage": 0.710919256290645, "avg_cpu_usage": 39.14, "std_cpu_usage": 53.59843048075195}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 16597.38173368245, "memory_usage_ratio": 1.5993322203672788, "cpu_usage_ratio": 5.085226699674327}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 5.971173636800001, "std": 0.1161122241271403, "n": 5, "confidence_interval_95": [5.827001268349964, 6.1153460052500375]}, "canonical": {"mean": 0.00035976599999999997, "std": 1.1230756786610595e-05, "n": 5, "confidence_interval_95": [0.000345821173625483, 0.00037371082637451696]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 114.97956375468522, "p_value": 3.431228591293966e-08, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 114.98484103650978, "p_value": 3.6571904834857693e-14, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 72.72279881355344, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 16597.38173368245, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.2453125, "std": 0.8390912326318798, "n": 5, "confidence_interval_95": [1.2034431252325732, 3.2871818747674264]}, "canonical": {"mean": 1.40390625, "std": 0.43259492218381246, "n": 5, "confidence_interval_95": [0.8667687093186002, 1.9410437906813995]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.665003667524687, "p_value": 0.1712427917800356, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.992967011563749, "p_value": 0.08139814784265802, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 23.0, "p_value": 0.031746031746031744, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.260463011624116, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.5993322203672788, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 100.43322731856796, "std": 0.23631630875135037, "n": 5, "confidence_interval_95": [100.139801865803, 100.72665277133292]}, "canonical": {"mean": 19.75, "std": 44.162342555620846, "n": 5, "confidence_interval_95": [-35.084790827656526, 74.58479082765652]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 4.072031133535575, "p_value": 0.015197556889085332, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 4.0851682125829685, "p_value": 0.003508961050794204, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.009700785068229596, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.583687235336222, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 5.085226699674327, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 16211.125750804968, "memory_usage_ratio": 1.1716265796983285, "cpu_usage_ratio": 2.5659996759981594}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 5.971173636800001, "std": 0.1161122241271403, "n": 5, "confidence_interval_95": [5.827001268349964, 6.1153460052500375]}, "canonical": {"mean": 0.00036833800000000006, "std": 1.5681999681163114e-05, "n": 5, "confidence_interval_95": [0.0003488662255760569, 0.00038780977442394324]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 114.97369731264843, "p_value": 3.4319287712405385e-08, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 114.98467544765204, "p_value": 3.657232594381149e-14, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 72.72269408596432, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 16211.125750804966, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.2453125, "std": 0.8390912326318798, "n": 5, "confidence_interval_95": [1.2034431252325732, 3.2871818747674264]}, "canonical": {"mean": 1.91640625, "std": 0.710919256290645, "n": 5, "confidence_interval_95": [1.0336834038341096, 2.7991290961658906]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.5627401916478777, "p_value": 0.6036559769100422, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.6687406630349929, "p_value": 0.5224857521522296, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 0.625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 18.0, "p_value": 0.2903468336607644, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.4229487318323496, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.1716265796983285, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 100.43322731856796, "std": 0.23631630875135037, "n": 5, "confidence_interval_95": [100.139801865803, 100.72665277133292]}, "canonical": {"mean": 39.14, "std": 53.59843048075195, "n": 5, "confidence_interval_95": [-27.411241488177083, 105.69124148817708]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.5558936198363007, "p_value": 0.06291113367469621, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 2.557061643262766, "p_value": 0.03380116082751855, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.011159425282914755, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.6172277820326584, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.5659996759981594, "better_performance": true}}}}}, "Mbpp/4": {"task_id": "Mbpp/4", "entry_point": "heap_queue_largest", "evalperf_canonical_score": 100.00000000000001, "generated_code_results": {"execution_times": [0.083439197, 0.084631071, 0.082538923, 0.081207475, 0.08562286100000001], "memory_usages": [3.1875, 1.65625, 1.56640625, 1.62109375, 3.28125], "cpu_usages_list": [[0.0, 0.0, 197.5, 0.0, 193.4, 197.5, 0.0, 0.0, 197.5, 195.5, 0.0, 0.0, 197.5, 197.5, 0.0, 194.1, 0.0, 193.4], [0.0, 196.1, 0.0, 0.0, 193.7, 195.1, 0.0, 0.0, 197.5, 193.4, 0.0, 197.5, 0.0, 193.4, 0.0, 191.0, 0.0, 193.4], [0.0, 0.0, 0.0, 197.5, 194.3, 0.0, 192.5, 0.0, 193.5, 0.0, 195.7, 0.0, 193.5, 0.0, 193.6, 0.0, 197.1, 0.0, 193.6], [0.0, 0.0, 193.7, 197.5, 0.0, 0.0, 193.0, 193.1, 0.0, 0.0, 194.7, 193.5, 0.0, 194.8, 0.0, 193.5, 0.0, 193.7], [0.0, 197.5, 0.0, 0.0, 196.3, 192.8, 0.0, 0.0, 195.7, 193.4, 0.0, 195.5, 0.0, 193.0, 0.0, 193.4, 0.0, 194.2, 0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0834879054, "std_execution_time": 0.0017297518547453009, "avg_memory_usage": 2.2625, "std_memory_usage": 0.8883923839340537, "avg_cpu_usage": 95.34695906432748, "std_cpu_usage": 2.9045974075586725}, "evalperf_canonical_results": {"execution_times": [0.00041007900000000004, 0.00047057900000000005, 0.000505564, 0.00044827200000000005, 0.000470618], "memory_usages": [3.27734375, 3.1875, 1.5625, 3.125, 1.5703125], "cpu_usages_list": [[0.0, 0.0, 0.0], [0.0, 0.0, 193.6], [0.0, 0.0, 194.2], [0.0, 0.0, 193.1], [0.0, 0.0, 194.3]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.00046102240000000003, "std_execution_time": 3.508974557189036e-05, "avg_memory_usage": 2.54453125, "std_memory_usage": 0.8945466019425008, "avg_cpu_usage": 51.68, "std_cpu_usage": 28.890450171485924}, "original_canonical_results": {"execution_times": [0.00099081, 0.000933291, 0.000946497, 0.000943895, 0.001002305], "memory_usages": [1.578125, 0.62890625, 1.62109375, 1.58203125, 3.265625], "cpu_usages_list": [[0.0, 195.2, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 194.4], [0.0, 0.0, 196.3], [0.0, 0.0, 0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0009633596, "std_execution_time": 3.0974212174000454e-05, "avg_memory_usage": 1.73515625, "std_memory_usage": 0.952265717937275, "avg_cpu_usage": 39.06, "std_cpu_usage": 35.657447406615574}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 181.09294776132353, "memory_usage_ratio": 0.8891618053423397, "cpu_usage_ratio": 1.8449488983035502}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0834879054, "std": 0.0017297518547453009, "n": 5, "confidence_interval_95": [0.08134013479613658, 0.08563567600386343]}, "canonical": {"mean": 0.00046102240000000003, "std": 3.508974557189036e-05, "n": 5, "confidence_interval_95": [0.0004174527262172537, 0.0005045920737827463]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 107.49362640572836, "p_value": 4.4912772455821324e-08, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 107.30762643372945, "p_value": 6.354589040087634e-14, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 67.86730196741529, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 181.09294776132353, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.2625, "std": 0.8883923839340537, "n": 5, "confidence_interval_95": [1.1594151605906675, 3.365584839409333]}, "canonical": {"mean": 2.54453125, "std": 0.8945466019425008, "n": 5, "confidence_interval_95": [1.4338049396524812, 3.6552575603475184]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.47188595052457155, "p_value": 0.6615982761413668, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.50021667339939, "p_value": 0.6303901789569109, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 6.0, "p_value": 0.8125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 13.5, "p_value": 0.9165626446795413, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.31636480230692676, "interpretation": "small", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.8891618053423397, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 95.34695906432749, "std": 2.90459740755868, "n": 5, "confidence_interval_95": [91.74042503408589, 98.9534930945691]}, "canonical": {"mean": 51.67999999999999, "std": 28.890450171485924, "n": 5, "confidence_interval_95": [15.807768130166089, 87.55223186983389]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 3.20293715403501, "p_value": 0.032808840500415735, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 3.3627899944563064, "p_value": 0.009890764324400033, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.126815135061385, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.844948898303551, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 86.66328274509333, "memory_usage_ratio": 1.3039171544349393, "cpu_usage_ratio": 2.4410383785030074}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0834879054, "std": 0.0017297518547453009, "n": 5, "confidence_interval_95": [0.08134013479613658, 0.08563567600386343]}, "canonical": {"mean": 0.0009633596000000001, "std": 3.0974212174000454e-05, "n": 5, "confidence_interval_95": [0.0009249000358708189, 0.0010018191641291813]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 107.63706995676984, "p_value": 4.4673905535762666e-08, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 106.66322794318822, "p_value": 6.668086779805418e-14, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 67.45974857723834, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 86.66328274509331, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.2625, "std": 0.8883923839340537, "n": 5, "confidence_interval_95": [1.1594151605906675, 3.365584839409333]}, "canonical": {"mean": 1.73515625, "std": 0.952265717937275, "n": 5, "confidence_interval_95": [0.5527621913136229, 2.917550308686377]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.5688132080769197, "p_value": 0.19177056235041623, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.905439315888245, "p_value": 0.3916854274546042, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 0.3125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 17.5, "p_value": 0.345741825860727, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.5726501042543075, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.3039171544349393, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 95.34695906432749, "std": 2.90459740755868, "n": 5, "confidence_interval_95": [91.74042503408589, 98.9534930945691]}, "canonical": {"mean": 39.06, "std": 35.657447406615574, "n": 5, "confidence_interval_95": [-5.214568712638815, 83.33456871263883]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 3.5745650106397044, "p_value": 0.023280488149193958, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 3.518085780406786, "p_value": 0.00786790844540604, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.0119252335930176, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.2250328139872835, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.441038378503008, "better_performance": true}}}}}, "Mbpp/12": {"task_id": "Mbpp/12", "entry_point": "sort_matrix", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [2.2882747990000003, 2.3664651140000004, 2.2853614230000003, 2.2946947680000003, 2.3203739110000003], "memory_usages": [3.140625, 3.171875, 1.61328125, 1.55859375, 1.62109375], "cpu_usages_list": [[0.0, 0.0, 195.8, 0.0, 0.0, 197.5, 197.6, 0.0, 197.5, 0.0, 0.0, 0.0, 393.4, 0.0, 194.3, 0.0, 0.0, 194.4, 0.0, 388.6, 0.0, 0.0, 0.0, 194.2, 0.0, 197.5, 193.6, 194.3, 0.0, 194.1, 0.0, 194.2, 0.0, 0.0, 194.3, 197.5, 0.0, 0.0, 193.4, 0.0, 193.7, 0.0, 195.6, 193.6, 0.0, 197.5, 0.0, 193.7, 195.5, 0.0, 193.3, 0.0, 193.6, 0.0, 0.0, 195.3, 0.0, 193.2, 197.5, 0.0, 0.0, 193.6, 195.9, 0.0, 0.0, 195.9, 193.6, 0.0, 197.5, 0.0, 193.6, 0.0, 193.4, 0.0, 193.6, 195.5, 0.0, 0.0, 197.5, 193.7, 0.0, 0.0, 192.9, 0.0, 193.3, 0.0, 193.4, 0.0, 195.1, 0.0, 194.3, 197.5, 0.0, 194.3, 194.0, 0.0, 0.0, 195.3, 192.8, 0.0, 192.8, 0.0, 192.9, 0.0, 195.6, 0.0, 0.0, 0.0, 0.0, 193.6, 0.0, 197.5, 0.0, 197.5, 0.0, 193.4, 0.0, 0.0, 0.0, 193.3, 196.0, 0.0, 0.0, 197.5, 195.8, 0.0, 193.1, 0.0, 192.6, 0.0, 195.6, 0.0, 193.7, 0.0, 193.5, 0.0, 197.5, 0.0, 193.5, 0.0, 0.0, 0.0, 193.5, 195.6, 0.0, 0.0, 197.5, 193.6, 0.0, 193.1, 0.0, 193.6, 0.0, 191.5, 0.0, 193.6, 0.0, 193.4, 0.0, 196.6, 0.0, 193.4, 195.2, 0.0, 0.0, 193.5, 192.2, 0.0, 0.0, 196.0, 193.6, 0.0, 197.5, 0.0, 195.9, 0.0, 193.4, 0.0, 197.5, 0.0, 195.9, 0.0, 197.5, 0.0, 193.6, 0.0, 0.0, 0.0, 194.0, 195.5, 0.0, 0.0, 197.6, 193.5, 0.0, 191.7, 0.0, 193.7, 0.0, 194.8, 0.0, 193.4, 0.0, 193.3, 0.0, 197.5, 0.0, 193.3, 193.2, 0.0, 0.0, 192.6, 195.1, 0.0, 0.0, 197.5, 193.7, 0.0, 196.4, 0.0, 193.4, 0.0, 193.4, 0.0, 193.3, 0.0, 193.6, 0.0, 195.7, 0.0, 194.3, 193.2, 0.0, 0.0, 194.3, 193.6, 0.0, 0.0, 195.9, 194.2, 0.0, 197.5, 0.0, 194.4, 0.0, 194.1, 0.0, 194.2, 0.0, 194.0, 0.0, 197.5, 0.0, 193.2, 192.8, 0.0, 0.0, 193.6, 195.7, 0.0, 0.0, 195.6, 193.5, 0.0, 197.5, 0.0, 193.5, 0.0, 193.7, 0.0, 193.4, 0.0, 197.5, 0.0, 197.5, 0.0, 192.2, 193.7, 0.0, 0.0, 193.0, 195.7, 0.0, 0.0, 195.5, 191.9, 0.0, 197.5, 0.0, 193.4, 0.0, 193.8, 0.0, 193.6, 0.0, 193.4, 0.0, 197.5, 0.0, 193.5, 192.8, 0.0, 0.0, 192.9, 192.1, 0.0, 0.0, 195.8, 192.7, 0.0, 197.5, 0.0, 193.4, 0.0, 194.0, 0.0, 193.5, 0.0, 193.4, 0.0, 195.0, 0.0, 193.3, 197.5, 0.0, 0.0, 192.0, 193.3, 0.0, 0.0, 195.9, 193.5, 0.0, 195.7, 0.0, 193.5, 0.0, 193.6, 0.0, 193.4, 0.0, 194.0, 0.0, 195.6, 0.0, 193.6, 197.5, 0.0, 0.0, 192.8, 193.6, 0.0, 0.0, 191.9, 193.7, 0.0, 195.6, 0.0, 193.4, 0.0, 193.4, 0.0, 192.7, 0.0, 191.8, 0.0, 191.3, 0.0, 193.3, 195.6, 0.0, 0.0, 197.5, 193.6, 0.0, 193.0, 0.0, 193.2, 0.0, 191.0, 0.0, 193.6, 0.0, 193.7, 0.0, 197.5, 0.0, 193.5, 192.2, 0.0, 0.0, 193.3, 192.0, 0.0, 0.0, 195.9, 193.4, 0.0, 197.5, 0.0, 193.4, 0.0, 193.5, 0.0, 193.4, 0.0, 192.9, 0.0, 195.7, 0.0, 193.7, 197.5, 0.0, 0.0, 189.9, 193.6, 0.0, 0.0, 191.9, 193.5, 0.0, 195.8, 0.0, 193.6, 0.0, 193.7, 0.0, 192.2, 0.0, 193.6, 0.0, 195.9, 0.0, 193.5, 195.6, 0.0, 0.0, 197.4, 193.5, 0.0, 193.3, 0.0, 193.6, 0.0, 196.0, 0.0, 193.6, 0.0, 193.3, 0.0, 195.7, 0.0, 194.2, 0.0, 195.6, 0.0, 193.7, 195.8, 0.0, 0.0, 197.9, 194.4, 0.0, 0.0, 197.5, 194.4, 0.0, 197.5, 0.0, 197.0, 0.0, 197.4, 0.0, 197.4, 0.0, 193.3, 0.0, 191.5, 0.0, 193.4, 0.0, 192.7, 0.0, 193.4, 195.5, 0.0, 0.0, 197.5, 193.3, 0.0, 192.6, 0.0, 193.5, 0.0, 193.6, 0.0, 193.6, 0.0, 193.4, 0.0, 197.5, 0.0, 193.4, 0.0, 193.5, 0.0, 192.5, 195.7, 0.0, 0.0, 197.5, 193.6, 0.0, 192.9, 0.0, 193.5, 0.0, 195.7, 0.0, 193.4, 0.0, 195.3, 0.0, 197.6, 0.0, 193.4, 0.0, 0.0, 0.0, 193.4, 196.0, 0.0, 0.0, 197.5, 193.3, 0.0, 193.6, 0.0, 192.4, 0.0, 195.8, 0.0, 197.5, 0.0, 193.4, 0.0, 192.8, 0.0, 193.2, 0.0, 195.8, 0.0, 193.2, 197.5, 0.0, 0.0, 195.6, 193.4, 0.0, 0.0, 191.7, 193.4, 0.0, 195.6, 0.0, 193.3, 0.0, 193.5, 0.0, 193.8, 0.0, 193.4, 0.0, 195.6, 0.0, 193.5, 197.5, 0.0, 0.0, 193.0, 193.4, 0.0, 0.0, 191.2, 193.6, 0.0, 195.7, 194.2, 0.0, 197.5], [0.0, 0.0, 0.0, 195.1, 0.0, 193.4, 197.5, 0.0, 0.0, 197.5, 0.0, 193.5, 0.0, 391.3, 0.0, 0.0, 197.5, 197.5, 0.0, 0.0, 193.7, 0.0, 192.8, 0.0, 193.7, 0.0, 195.7, 193.2, 0.0, 0.0, 194.4, 0.0, 193.7, 194.0, 0.0, 0.0, 195.4, 193.6, 0.0, 0.0, 387.2, 0.0, 0.0, 193.3, 192.9, 0.0, 0.0, 194.5, 193.4, 0.0, 0.0, 193.5, 197.5, 0.0, 0.0, 193.0, 0.0, 193.6, 0.0, 391.6, 0.0, 0.0, 195.9, 193.5, 0.0, 0.0, 193.6, 0.0, 193.6, 0.0, 193.8, 0.0, 195.3, 0.0, 0.0, 197.4, 193.3, 0.0, 193.2, 0.0, 193.5, 0.0, 193.5, 193.5, 0.0, 195.6, 0.0, 190.0, 197.5, 0.0, 0.0, 194.1, 0.0, 193.7, 0.0, 195.6, 194.2, 0.0, 195.4, 0.0, 193.9, 197.4, 0.0, 0.0, 194.3, 0.0, 193.4, 0.0, 195.6, 0.0, 193.3, 0.0, 193.3, 0.0, 195.5, 0.0, 193.5, 0.0, 195.7, 0.0, 193.6, 194.7, 0.0, 0.0, 197.7, 193.5, 0.0, 0.0, 191.9, 193.3, 0.0, 195.5, 0.0, 193.5, 0.0, 193.4, 0.0, 192.9, 0.0, 192.3, 0.0, 192.4, 0.0, 193.5, 195.7, 0.0, 0.0, 197.5, 193.2, 0.0, 192.7, 0.0, 192.3, 0.0, 190.8, 0.0, 192.9, 0.0, 193.4, 0.0, 197.4, 0.0, 193.8, 197.5, 0.0, 0.0, 193.4, 191.2, 0.0, 0.0, 195.6, 193.5, 0.0, 197.5, 0.0, 192.6, 0.0, 193.7, 0.0, 194.5, 0.0, 194.4, 0.0, 197.5, 0.0, 194.1, 196.0, 0.0, 0.0, 194.3, 193.4, 0.0, 0.0, 197.5, 194.2, 0.0, 196.5, 0.0, 193.5, 0.0, 190.5, 0.0, 193.3, 0.0, 193.6, 0.0, 197.5, 0.0, 193.4, 193.9, 0.0, 0.0, 193.5, 195.8, 0.0, 0.0, 195.8, 194.1, 0.0, 197.4, 0.0, 192.7, 0.0, 194.3, 0.0, 194.3, 0.0, 193.7, 0.0, 197.5, 0.0, 194.2, 0.0, 193.4, 0.0, 197.5, 195.6, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 193.3, 193.2, 0.0, 195.8, 0.0, 194.2, 0.0, 193.4, 0.0, 193.4, 0.0, 193.6, 0.0, 195.6, 0.0, 193.7, 197.4, 0.0, 0.0, 196.8, 194.4, 0.0, 0.0, 195.8, 193.7, 0.0, 197.5, 0.0, 193.5, 0.0, 194.9, 0.0, 194.3, 0.0, 195.9, 0.0, 197.5, 0.0, 195.9, 193.3, 0.0, 0.0, 193.3, 195.7, 0.0, 0.0, 197.7, 197.7, 0.0, 0.0, 197.7, 197.4, 0.0, 197.7, 0.0, 197.4, 0.0, 197.7, 0.0, 193.2, 0.0, 193.4, 0.0, 193.3, 0.0, 194.5, 0.0, 195.6, 0.0, 193.6, 197.4, 0.0, 0.0, 194.1, 194.2, 0.0, 0.0, 195.7, 194.2, 0.0, 197.5, 0.0, 193.4, 0.0, 194.4, 0.0, 193.4, 0.0, 193.6, 0.0, 195.9, 0.0, 194.2, 197.5, 0.0, 0.0, 197.5, 195.8, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 0.0, 194.5, 0.0, 195.7, 0.0, 192.5, 0.0, 193.6, 0.0, 194.0, 0.0, 193.5, 0.0, 195.7, 0.0, 194.1, 197.4, 0.0, 0.0, 195.2, 194.2, 0.0, 0.0, 195.4, 194.2, 0.0, 197.5, 0.0, 194.1, 0.0, 195.9, 0.0, 194.2, 0.0, 194.4, 0.0, 196.8, 0.0, 193.8, 193.6, 0.0, 0.0, 193.7, 195.7, 0.0, 0.0, 197.4, 194.0, 0.0, 194.8, 0.0, 194.2, 0.0, 195.9, 0.0, 194.3, 0.0, 194.2, 0.0, 197.5, 0.0, 197.5, 0.0, 195.7, 0.0, 194.3, 197.5, 0.0, 0.0, 193.1, 194.1, 0.0, 0.0, 195.7, 193.3, 0.0, 197.5, 0.0, 193.2, 0.0, 193.4, 0.0, 193.4, 0.0, 193.4, 0.0, 195.5, 0.0, 193.6, 197.5, 0.0, 0.0, 194.4, 193.1, 0.0, 0.0, 195.9, 194.5, 0.0, 197.5, 0.0, 193.5, 0.0, 194.2, 0.0, 193.6, 0.0, 194.3, 0.0, 197.5, 0.0, 192.8, 194.8, 0.0, 0.0, 193.6, 195.3, 0.0, 0.0, 197.5, 194.2, 0.0, 196.9, 0.0, 194.4, 0.0, 193.6, 0.0, 193.7, 0.0, 194.2, 0.0, 197.5, 0.0, 194.1, 0.0, 194.2, 0.0, 194.2, 195.6, 0.0, 0.0, 197.5, 194.2, 0.0, 193.4, 0.0, 194.2, 0.0, 195.6, 0.0, 194.5, 0.0, 194.3, 0.0, 194.5, 0.0, 193.6, 0.0, 194.2, 0.0, 193.4, 195.9, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 0.0, 194.5, 0.0, 195.8, 0.0, 193.3, 0.0, 194.4, 0.0, 193.2, 0.0, 194.4, 0.0, 195.7, 0.0, 194.3, 197.5, 0.0, 0.0, 194.0, 193.6, 0.0, 0.0, 196.0, 194.4, 0.0, 197.5, 0.0, 194.3, 0.0, 193.5, 0.0, 193.3, 0.0, 193.0, 0.0, 195.9, 0.0, 195.6, 197.5, 0.0, 0.0, 194.2, 194.3, 0.0, 0.0, 195.9, 194.3, 0.0, 197.5, 0.0, 193.2, 0.0, 194.4, 0.0, 195.8, 0.0, 193.7, 0.0, 197.5, 0.0, 193.5, 191.3, 0.0, 0.0, 193.3, 193.5, 0.0, 0.0, 194.8, 194.3, 0.0, 197.5, 192.1, 0.0], [0.0, 0.0, 0.0, 391.6, 0.0, 0.0, 197.5, 0.0, 0.0, 197.7, 193.6, 0.0, 0.0, 193.1, 0.0, 193.5, 0.0, 193.4, 0.0, 197.5, 0.0, 193.5, 193.4, 193.3, 0.0, 0.0, 195.8, 193.0, 0.0, 197.5, 0.0, 0.0, 195.3, 193.5, 0.0, 194.4, 195.5, 0.0, 0.0, 0.0, 193.5, 193.4, 195.2, 0.0, 0.0, 193.3, 195.0, 0.0, 194.5, 0.0, 0.0, 0.0, 197.5, 193.6, 0.0, 193.3, 0.0, 193.6, 0.0, 195.6, 0.0, 193.6, 197.5, 193.4, 0.0, 196.6, 0.0, 193.6, 0.0, 193.2, 0.0, 193.4, 0.0, 0.0, 193.6, 197.5, 0.0, 0.0, 193.1, 0.0, 193.7, 0.0, 195.8, 0.0, 193.5, 197.5, 0.0, 0.0, 394.9, 0.0, 0.0, 197.5, 196.0, 0.0, 0.0, 197.6, 0.0, 197.7, 197.4, 0.0, 194.4, 0.0, 197.4, 0.0, 194.4, 0.0, 194.4, 0.0, 194.4, 0.0, 194.3, 0.0, 197.3, 0.0, 194.2, 0.0, 197.4, 0.0, 194.2, 0.0, 194.0, 0.0, 195.3, 0.0, 193.7, 0.0, 191.1, 0.0, 197.5, 195.9, 0.0, 0.0, 197.5, 193.7, 0.0, 193.3, 0.0, 193.5, 0.0, 195.8, 0.0, 193.6, 0.0, 193.5, 0.0, 197.5, 0.0, 193.6, 193.5, 0.0, 0.0, 192.8, 195.6, 0.0, 0.0, 197.5, 193.4, 0.0, 194.6, 0.0, 194.5, 0.0, 195.8, 0.0, 194.5, 0.0, 194.0, 0.0, 196.3, 0.0, 197.4, 0.0, 197.7, 0.0, 197.7, 0.0, 197.4, 0.0, 197.5, 197.7, 0.0, 0.0, 193.4, 193.4, 0.0, 0.0, 194.6, 193.5, 0.0, 197.5, 0.0, 193.5, 0.0, 192.7, 0.0, 194.1, 0.0, 194.2, 0.0, 195.9, 0.0, 193.4, 197.5, 0.0, 0.0, 193.4, 195.5, 0.0, 0.0, 197.5, 193.6, 0.0, 196.3, 0.0, 194.3, 0.0, 195.5, 0.0, 194.5, 0.0, 193.3, 0.0, 196.4, 0.0, 194.3, 0.0, 194.3, 0.0, 194.1, 197.5, 0.0, 0.0, 197.5, 194.2, 0.0, 0.0, 195.7, 194.0, 0.0, 197.5, 0.0, 195.7, 0.0, 194.1, 0.0, 194.2, 0.0, 193.7, 0.0, 197.5, 0.0, 197.6, 193.4, 0.0, 0.0, 193.3, 195.6, 0.0, 0.0, 197.5, 194.2, 0.0, 194.1, 0.0, 194.3, 0.0, 195.7, 0.0, 194.5, 0.0, 194.4, 0.0, 194.4, 0.0, 194.5, 0.0, 195.7, 0.0, 194.2, 197.5, 0.0, 0.0, 194.1, 197.5, 0.0, 0.0, 197.5, 194.0, 0.0, 193.6, 0.0, 194.4, 0.0, 195.7, 0.0, 194.2, 0.0, 194.5, 0.0, 197.5, 0.0, 194.5, 0.0, 195.7, 0.0, 194.5, 197.5, 0.0, 0.0, 196.4, 194.5, 0.0, 0.0, 195.7, 197.5, 0.0, 197.5, 0.0, 194.2, 0.0, 194.4, 0.0, 194.0, 0.0, 194.5, 0.0, 197.5, 0.0, 194.2, 0.0, 0.0, 0.0, 194.3, 195.7, 0.0, 0.0, 197.5, 194.1, 0.0, 194.1, 0.0, 194.5, 0.0, 196.0, 0.0, 197.5, 0.0, 194.4, 0.0, 194.4, 0.0, 194.4, 0.0, 195.7, 0.0, 194.5, 197.5, 0.0, 0.0, 193.6, 194.1, 0.0, 0.0, 195.4, 194.4, 0.0, 197.4, 0.0, 194.1, 0.0, 194.5, 0.0, 194.3, 0.0, 194.5, 0.0, 197.5, 0.0, 194.2, 193.7, 0.0, 0.0, 194.1, 195.6, 0.0, 0.0, 197.5, 194.5, 0.0, 193.2, 0.0, 194.2, 0.0, 195.6, 0.0, 194.2, 0.0, 194.3, 0.0, 197.6, 0.0, 194.3, 0.0, 195.3, 0.0, 194.4, 195.6, 0.0, 0.0, 197.2, 193.7, 0.0, 0.0, 194.1, 194.6, 0.0, 195.9, 0.0, 194.2, 0.0, 194.4, 0.0, 194.1, 0.0, 194.5, 0.0, 195.6, 0.0, 193.7, 197.4, 0.0, 0.0, 194.2, 194.2, 0.0, 0.0, 195.5, 193.6, 0.0, 197.5, 0.0, 194.4, 0.0, 193.6, 0.0, 194.5, 0.0, 194.0, 0.0, 197.5, 0.0, 194.4, 0.0, 194.1, 0.0, 194.5, 195.7, 0.0, 0.0, 197.5, 197.5, 0.0, 0.0, 194.2, 194.5, 0.0, 197.5, 0.0, 194.4, 0.0, 194.4, 0.0, 194.1, 0.0, 193.9, 0.0, 195.6, 0.0, 193.0, 198.2, 0.0, 0.0, 197.4, 197.7, 0.0, 0.0, 197.3, 194.5, 0.0, 0.0, 197.6, 194.0, 0.0, 197.2, 0.0, 194.2, 0.0, 195.9, 0.0, 194.5, 0.0, 194.4, 0.0, 196.6, 0.0, 194.4, 0.0, 194.8, 0.0, 194.3, 197.5, 0.0, 0.0, 194.4, 194.2, 0.0, 0.0, 195.6, 194.1, 0.0, 197.5, 0.0, 194.3, 0.0, 194.4, 0.0, 194.5, 0.0, 194.2, 0.0, 197.5, 0.0, 194.2, 0.0, 0.0, 0.0, 194.4, 195.5, 0.0, 0.0, 197.5, 194.4, 0.0, 0.0, 194.1, 194.2, 0.0, 195.7, 0.0, 194.5, 0.0, 193.9, 0.0, 194.2, 0.0, 194.4, 0.0, 195.9, 0.0, 194.2, 197.5, 0.0, 0.0, 194.0, 194.1, 0.0, 0.0, 195.6, 194.1, 0.0, 197.4, 0.0, 195.7, 0.0, 193.6, 0.0, 194.3, 0.0, 193.7, 0.0, 197.5, 0.0, 197.5, 0.0, 194.4, 0.0, 194.5, 195.6, 0.0, 0.0, 197.4, 194.4, 0.0, 0.0, 194.3, 194.2], [0.0, 0.0, 193.4, 0.0, 192.9, 0.0, 0.0, 193.6, 197.1, 0.0, 0.0, 197.5, 193.6, 0.0, 191.7, 193.7, 0.0, 0.0, 193.4, 0.0, 193.5, 195.8, 0.0, 0.0, 197.5, 193.6, 0.0, 0.0, 193.4, 0.0, 0.0, 391.4, 0.0, 0.0, 195.5, 0.0, 194.4, 197.5, 0.0, 193.7, 0.0, 193.2, 0.0, 0.0, 391.9, 0.0, 0.0, 197.5, 193.9, 0.0, 193.7, 0.0, 194.0, 0.0, 195.8, 0.0, 0.0, 197.5, 194.2, 0.0, 193.5, 0.0, 0.0, 193.4, 195.6, 193.7, 0.0, 197.5, 0.0, 193.5, 0.0, 193.4, 0.0, 193.6, 0.0, 0.0, 0.0, 195.7, 193.2, 0.0, 197.5, 0.0, 193.6, 193.0, 0.0, 0.0, 195.6, 195.8, 0.0, 0.0, 392.0, 0.0, 0.0, 197.5, 0.0, 194.6, 0.0, 196.0, 0.0, 195.2, 0.0, 194.5, 0.0, 190.7, 0.0, 193.7, 0.0, 193.5, 193.6, 0.0, 195.8, 194.1, 0.0, 197.4, 0.0, 193.4, 0.0, 193.3, 0.0, 197.5, 0.0, 192.5, 0.0, 197.5, 0.0, 193.5, 195.1, 0.0, 0.0, 193.4, 191.6, 0.0, 0.0, 195.6, 195.6, 0.0, 197.5, 0.0, 192.9, 0.0, 193.2, 0.0, 193.6, 0.0, 194.2, 0.0, 195.6, 0.0, 193.5, 197.5, 0.0, 0.0, 194.5, 193.6, 0.0, 0.0, 196.0, 193.5, 0.0, 197.5, 0.0, 193.6, 0.0, 194.3, 0.0, 193.6, 0.0, 193.7, 0.0, 195.8, 0.0, 193.3, 197.5, 0.0, 0.0, 193.1, 193.0, 0.0, 0.0, 195.7, 193.4, 0.0, 197.5, 0.0, 193.4, 0.0, 193.6, 0.0, 193.5, 0.0, 193.5, 0.0, 193.9, 0.0, 193.0, 197.5, 0.0, 0.0, 195.6, 193.5, 0.0, 0.0, 191.7, 193.6, 0.0, 195.9, 0.0, 193.7, 0.0, 193.4, 0.0, 193.3, 0.0, 193.2, 0.0, 193.0, 0.0, 193.4, 195.6, 0.0, 0.0, 197.5, 193.3, 0.0, 192.9, 0.0, 193.5, 0.0, 195.8, 0.0, 193.4, 0.0, 193.3, 0.0, 194.5, 0.0, 193.4, 0.0, 193.6, 0.0, 192.5, 195.6, 0.0, 0.0, 197.5, 193.5, 0.0, 193.6, 0.0, 193.4, 0.0, 192.6, 0.0, 193.5, 0.0, 197.5, 0.0, 197.6, 0.0, 195.7, 0.0, 195.7, 0.0, 193.7, 195.8, 0.0, 0.0, 197.5, 194.1, 0.0, 194.1, 0.0, 194.0, 0.0, 195.6, 0.0, 194.2, 0.0, 193.5, 0.0, 193.1, 0.0, 193.4, 0.0, 192.5, 0.0, 193.4, 195.7, 0.0, 0.0, 197.5, 193.4, 0.0, 0.0, 197.7, 197.4, 0.0, 197.4, 0.0, 197.4, 0.0, 197.7, 0.0, 193.7, 0.0, 193.5, 0.0, 193.6, 0.0, 193.6, 0.0, 195.8, 0.0, 193.7, 197.5, 0.0, 0.0, 193.0, 193.1, 0.0, 0.0, 195.1, 192.9, 0.0, 195.8, 0.0, 193.6, 0.0, 193.1, 0.0, 193.6, 0.0, 192.1, 0.0, 195.7, 0.0, 193.4, 195.6, 0.0, 0.0, 197.5, 193.5, 0.0, 0.0, 192.1, 193.3, 0.0, 195.8, 0.0, 193.6, 0.0, 193.7, 0.0, 193.5, 0.0, 193.7, 0.0, 195.7, 0.0, 194.2, 197.5, 0.0, 0.0, 191.7, 193.3, 0.0, 0.0, 193.4, 193.6, 0.0, 195.4, 0.0, 193.2, 0.0, 193.7, 0.0, 193.1, 0.0, 193.5, 0.0, 195.5, 0.0, 193.5, 195.7, 0.0, 0.0, 195.8, 193.4, 0.0, 0.0, 0.0, 193.4, 0.0, 195.6, 0.0, 193.5, 0.0, 193.5, 0.0, 194.4, 0.0, 192.4, 0.0, 0.0, 0.0, 193.0, 195.1, 0.0, 0.0, 197.5, 193.5, 0.0, 193.2, 0.0, 193.4, 0.0, 193.0, 0.0, 193.5, 0.0, 193.4, 0.0, 197.5, 0.0, 193.5, 193.1, 0.0, 0.0, 193.5, 192.6, 0.0, 0.0, 195.7, 193.5, 0.0, 197.5, 0.0, 193.7, 0.0, 194.4, 0.0, 193.5, 0.0, 193.3, 0.0, 196.0, 0.0, 193.7, 197.5, 0.0, 0.0, 192.8, 193.3, 0.0, 0.0, 195.9, 192.6, 0.0, 197.5, 0.0, 193.6, 0.0, 193.5, 0.0, 193.5, 0.0, 193.7, 0.0, 195.6, 0.0, 195.7, 197.5, 0.0, 0.0, 192.5, 192.3, 0.0, 0.0, 194.1, 197.3, 0.0, 197.7, 0.0, 194.2, 0.0, 194.1, 0.0, 195.3, 0.0, 194.2, 0.0, 197.5, 0.0, 197.5, 0.0, 0.0, 0.0, 193.5, 195.8, 0.0, 0.0, 196.2, 192.7, 0.0, 192.9, 0.0, 193.7, 0.0, 192.2, 0.0, 193.6, 0.0, 193.7, 0.0, 197.5, 0.0, 192.7, 192.7, 0.0, 0.0, 193.6, 191.6, 0.0, 0.0, 193.2, 193.7, 0.0, 197.5, 0.0, 193.5, 0.0, 193.5, 0.0, 193.4, 0.0, 193.4, 0.0, 195.7, 0.0, 193.4, 197.5, 0.0, 0.0, 193.3, 193.5, 0.0, 0.0, 195.8, 193.6, 0.0, 197.5, 0.0, 193.5, 0.0, 193.6, 0.0, 193.4, 0.0, 193.5, 0.0, 197.5, 0.0, 193.7, 193.1, 0.0, 0.0, 193.7, 195.5, 0.0, 0.0, 193.9, 193.4, 0.0, 197.5, 0.0, 193.5, 0.0, 193.7, 0.0, 193.6, 0.0, 193.5, 0.0, 195.9, 0.0, 193.2, 196.7, 0.0, 0.0, 194.3, 0.0], [0.0, 197.4, 0.0, 0.0, 0.0, 194.6, 0.0, 193.6, 193.4, 0.0, 193.5, 0.0, 193.6, 0.0, 197.5, 0.0, 193.5, 0.0, 193.5, 0.0, 193.2, 0.0, 193.6, 193.5, 0.0, 0.0, 193.4, 191.6, 0.0, 193.1, 0.0, 193.4, 0.0, 193.4, 195.8, 0.0, 0.0, 197.5, 0.0, 193.6, 0.0, 193.5, 0.0, 193.7, 0.0, 193.5, 0.0, 195.9, 0.0, 193.5, 0.0, 193.4, 0.0, 195.7, 0.0, 193.4, 0.0, 195.6, 0.0, 193.6, 197.5, 0.0, 0.0, 192.9, 193.6, 0.0, 0.0, 391.8, 0.0, 0.0, 197.5, 0.0, 0.0, 197.5, 193.4, 193.3, 0.0, 0.0, 193.4, 0.0, 195.7, 0.0, 193.4, 197.5, 193.5, 0.0, 193.0, 0.0, 193.5, 0.0, 0.0, 0.0, 194.1, 195.9, 0.0, 194.0, 198.2, 0.0, 0.0, 194.4, 195.3, 0.0, 0.0, 197.6, 193.6, 0.0, 193.7, 0.0, 192.7, 0.0, 193.9, 0.0, 194.0, 0.0, 193.4, 0.0, 197.5, 0.0, 193.6, 194.9, 0.0, 0.0, 193.4, 194.6, 0.0, 0.0, 195.8, 193.5, 0.0, 197.5, 0.0, 192.1, 0.0, 193.8, 0.0, 193.3, 0.0, 193.2, 0.0, 195.7, 0.0, 192.0, 197.5, 0.0, 0.0, 193.4, 193.4, 0.0, 0.0, 195.0, 193.2, 0.0, 197.5, 0.0, 193.6, 0.0, 193.7, 0.0, 193.7, 0.0, 193.4, 0.0, 195.9, 0.0, 193.4, 197.5, 0.0, 0.0, 193.5, 193.2, 0.0, 0.0, 196.0, 193.4, 0.0, 195.6, 0.0, 193.2, 0.0, 193.6, 0.0, 193.6, 0.0, 193.4, 0.0, 197.5, 0.0, 193.3, 197.5, 0.0, 0.0, 193.3, 193.6, 0.0, 0.0, 195.7, 193.6, 0.0, 197.5, 0.0, 194.2, 0.0, 193.7, 0.0, 194.0, 0.0, 194.1, 0.0, 195.7, 0.0, 194.1, 197.1, 0.0, 0.0, 194.2, 193.7, 0.0, 0.0, 195.7, 193.3, 0.0, 197.5, 0.0, 193.0, 0.0, 197.5, 0.0, 193.4, 0.0, 193.1, 0.0, 197.5, 0.0, 193.6, 192.8, 0.0, 0.0, 193.4, 195.7, 0.0, 0.0, 195.6, 193.5, 0.0, 197.5, 0.0, 193.0, 0.0, 193.0, 0.0, 193.5, 0.0, 192.4, 0.0, 197.5, 0.0, 193.7, 194.3, 0.0, 0.0, 193.4, 195.8, 0.0, 0.0, 197.5, 193.6, 0.0, 194.7, 0.0, 192.3, 0.0, 192.2, 0.0, 193.3, 0.0, 194.0, 0.0, 197.5, 0.0, 193.5, 193.1, 0.0, 0.0, 197.5, 195.8, 0.0, 0.0, 197.5, 193.7, 0.0, 193.0, 0.0, 193.3, 0.0, 190.8, 0.0, 192.9, 0.0, 193.3, 0.0, 197.5, 0.0, 193.6, 197.5, 0.0, 0.0, 193.6, 192.5, 0.0, 0.0, 195.6, 197.5, 0.0, 195.8, 0.0, 193.4, 0.0, 196.0, 0.0, 193.5, 0.0, 193.4, 0.0, 197.5, 0.0, 193.5, 0.0, 195.5, 0.0, 194.0, 195.9, 0.0, 0.0, 196.7, 191.8, 0.0, 0.0, 0.0, 194.4, 0.0, 195.6, 0.0, 193.7, 0.0, 194.3, 0.0, 193.9, 0.0, 193.5, 0.0, 195.2, 0.0, 193.4, 197.5, 0.0, 0.0, 194.2, 197.2, 0.0, 0.0, 196.0, 194.3, 0.0, 197.5, 0.0, 193.4, 0.0, 193.7, 0.0, 194.3, 0.0, 193.5, 0.0, 197.5, 0.0, 194.2, 192.2, 0.0, 0.0, 193.5, 193.6, 0.0, 0.0, 196.0, 193.3, 0.0, 197.5, 0.0, 191.9, 0.0, 194.3, 0.0, 193.6, 0.0, 193.6, 0.0, 197.5, 0.0, 190.8, 197.5, 0.0, 0.0, 193.3, 195.7, 0.0, 0.0, 195.9, 193.4, 0.0, 198.1, 0.0, 194.0, 0.0, 193.8, 0.0, 194.1, 0.0, 194.2, 0.0, 197.4, 0.0, 193.9, 0.0, 0.0, 0.0, 194.7, 195.9, 0.0, 0.0, 197.5, 194.3, 0.0, 193.1, 0.0, 193.9, 0.0, 195.8, 0.0, 194.3, 0.0, 194.0, 0.0, 192.6, 0.0, 193.4, 0.0, 195.6, 0.0, 195.6, 197.5, 0.0, 0.0, 194.3, 193.6, 0.0, 0.0, 195.7, 194.0, 0.0, 197.5, 0.0, 193.4, 0.0, 194.3, 0.0, 193.7, 0.0, 193.5, 0.0, 197.5, 0.0, 194.4, 194.2, 0.0, 0.0, 193.5, 194.1, 0.0, 0.0, 195.9, 193.2, 0.0, 197.5, 0.0, 193.5, 0.0, 193.6, 0.0, 194.4, 0.0, 193.6, 0.0, 197.5, 0.0, 193.3, 193.8, 0.0, 0.0, 193.6, 195.9, 0.0, 0.0, 197.5, 193.6, 0.0, 192.9, 0.0, 193.4, 0.0, 195.5, 0.0, 194.5, 0.0, 193.5, 0.0, 193.5, 0.0, 194.2, 0.0, 195.6, 0.0, 193.5, 197.5, 0.0, 0.0, 197.5, 193.6, 0.0, 0.0, 195.7, 193.3, 0.0, 195.8, 0.0, 194.1, 0.0, 194.5, 0.0, 193.5, 0.0, 193.5, 0.0, 195.6, 0.0, 194.5, 197.5, 0.0, 0.0, 196.6, 194.3, 0.0, 0.0, 197.4, 194.1, 0.0, 196.5, 0.0, 193.5, 0.0, 193.4, 0.0, 193.4, 0.0, 194.4, 0.0, 197.5, 0.0, 197.5, 0.0, 194.3, 0.0, 193.7, 195.8, 0.0, 0.0, 197.5, 197.5, 0.0, 0.0, 195.7, 193.0, 0.0, 195.8, 0.0, 194.5, 0.0, 193.5, 0.0, 193.4, 192.9]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 2.3110340030000005, "std_execution_time": 0.0339259313766698, "avg_memory_usage": 2.22109375, "std_memory_usage": 0.8540879624245935, "avg_cpu_usage": 100.24567873657897, "std_cpu_usage": 0.44735136433039957}, "evalperf_canonical_results": {"execution_times": [0.005012377, 0.006004833, 0.006012998, 0.006245367000000001, 0.00612819], "memory_usages": [1.60546875, 1.5546875, 1.60546875, 0.6328125, 1.59765625], "cpu_usages_list": [[0.0, 0.0, 193.3, 0.0, 0.0, 195.6, 193.6, 0.0, 197.5, 0.0, 0.0, 194.3, 0.0, 192.9, 193.6, 195.8, 0.0, 0.0, 197.5, 0.0, 193.5, 193.8, 0.0, 0.0, 0.0, 195.5, 0.0, 193.5, 195.6, 0.0, 193.6, 0.0, 193.5, 194.2, 0.0, 0.0, 192.5, 0.0, 195.5, 193.4, 0.0, 197.5, 0.0, 194.2, 0.0, 193.9, 0.0, 194.4, 0.0, 0.0, 193.5, 195.8, 192.2, 0.0, 197.5, 0.0, 193.6, 0.0, 0.0, 193.6, 0.0, 194.2, 0.0, 193.4, 0.0, 193.5, 193.6, 194.5, 0.0, 0.0, 193.6, 0.0, 0.0, 193.6, 195.9, 0.0, 193.4, 197.5, 0.0, 193.4, 0.0, 0.0, 193.3, 195.9, 0.0, 194.2, 0.0, 0.0, 194.5, 0.0, 197.4, 194.3, 194.4, 0.0, 0.0, 194.2, 0.0, 195.5, 193.8, 0.0, 197.4, 0.0, 194.2, 0.0, 193.7, 0.0, 0.0, 195.8, 194.7, 0.0, 197.3, 0.0, 194.0, 0.0, 193.6, 0.0, 0.0, 193.4, 194.4, 0.0], [0.0, 0.0, 197.5, 194.3, 0.0, 0.0, 193.1, 0.0, 0.0, 195.9, 193.6, 0.0, 197.5, 0.0, 193.5, 0.0, 0.0, 193.4, 193.4, 0.0, 192.6, 0.0, 195.6, 0.0, 0.0, 197.5, 193.7, 0.0, 193.5, 193.6, 0.0, 193.5, 0.0, 193.7, 0.0, 197.5, 0.0, 0.0, 191.5, 0.0, 197.5, 0.0, 195.7, 194.2, 0.0, 195.9, 0.0, 0.0, 197.5, 193.6, 193.5, 0.0, 193.6, 0.0, 0.0, 195.6, 0.0, 0.0, 197.5, 193.6, 193.2, 0.0, 0.0, 193.7, 0.0, 195.8, 193.6, 0.0, 0.0, 193.4, 0.0, 197.4, 0.0, 193.6, 0.0, 0.0, 0.0, 194.2, 195.6, 0.0, 0.0, 394.9, 0.0, 0.0, 194.2, 0.0, 194.5, 0.0, 195.8, 0.0, 194.1, 197.5, 0.0, 0.0, 388.0, 0.0, 0.0, 194.1, 0.0, 194.4, 0.0, 197.5, 0.0, 194.2, 193.8, 0.0, 193.7, 0.0, 193.0, 0.0, 0.0, 195.6, 0.0, 193.5, 0.0, 194.4, 194.2, 0.0, 193.4, 0.0], [0.0, 0.0, 193.6, 0.0, 0.0, 193.7, 0.0, 197.5, 0.0, 193.4, 192.9, 195.8, 0.0, 0.0, 195.9, 0.0, 197.0, 197.4, 0.0, 193.6, 0.0, 0.0, 0.0, 193.7, 193.6, 194.1, 0.0, 195.6, 0.0, 193.6, 0.0, 194.0, 0.0, 193.3, 0.0, 0.0, 193.7, 195.9, 193.7, 0.0, 0.0, 0.0, 193.5, 197.5, 0.0, 196.8, 0.0, 197.6, 0.0, 197.3, 0.0, 193.6, 0.0, 193.3, 193.6, 0.0, 0.0, 195.7, 0.0, 193.5, 197.5, 0.0, 0.0, 194.0, 194.1, 0.0, 0.0, 195.6, 194.2, 0.0, 197.5, 194.5, 0.0, 194.2, 0.0, 0.0, 194.4, 0.0, 194.1, 0.0, 197.4, 194.2, 0.0, 193.4, 193.1, 0.0, 0.0, 195.6, 0.0, 193.1, 0.0, 0.0, 194.2, 197.4, 194.4, 0.0, 0.0, 193.7, 0.0, 194.6, 197.5, 0.0, 0.0, 194.3, 195.9, 0.0, 0.0, 195.5, 193.6, 0.0, 195.9, 0.0, 0.0, 197.4, 193.2, 0.0, 193.1, 0.0, 193.6, 0.0], [0.0, 0.0, 0.0, 194.4, 192.7, 0.0, 195.6, 0.0, 0.0, 197.5, 0.0, 193.4, 191.8, 0.0, 0.0, 193.6, 195.6, 193.3, 0.0, 0.0, 193.5, 0.0, 197.5, 0.0, 0.0, 193.5, 193.2, 0.0, 193.4, 195.8, 0.0, 197.5, 0.0, 193.6, 0.0, 0.0, 193.4, 0.0, 193.6, 0.0, 193.5, 193.4, 0.0, 194.4, 0.0, 193.3, 0.0, 195.8, 0.0, 195.5, 0.0, 192.1, 197.5, 0.0, 0.0, 193.6, 194.1, 0.0, 0.0, 195.0, 0.0, 194.5, 0.0, 197.5, 194.2, 192.8, 0.0, 0.0, 197.5, 0.0, 193.5, 192.7, 0.0, 0.0, 193.5, 192.7, 0.0, 0.0, 194.1, 192.5, 0.0, 0.0, 195.5, 193.5, 0.0, 197.5, 0.0, 193.7, 0.0, 193.3, 0.0, 193.8, 195.7, 0.0, 194.4, 0.0, 194.4, 0.0, 194.1, 0.0, 0.0, 194.5, 195.9, 0.0, 0.0, 197.5, 193.7, 0.0, 0.0, 192.0, 193.5, 0.0, 0.0, 193.9, 0.0, 197.4, 0.0, 194.1, 195.6, 194.2, 0.0], [0.0, 0.0, 0.0, 193.5, 0.0, 197.5, 0.0, 192.7, 0.0, 194.4, 193.4, 0.0, 0.0, 194.1, 194.1, 0.0, 0.0, 194.4, 197.5, 0.0, 0.0, 193.6, 195.8, 0.0, 193.2, 195.7, 0.0, 0.0, 197.4, 0.0, 0.0, 193.7, 194.0, 194.3, 0.0, 0.0, 194.3, 194.2, 197.5, 0.0, 0.0, 0.0, 0.0, 0.0, 194.4, 195.7, 0.0, 195.6, 0.0, 194.4, 194.3, 0.0, 194.1, 0.0, 0.0, 195.6, 0.0, 0.0, 197.5, 193.7, 0.0, 193.7, 0.0, 193.7, 0.0, 195.8, 193.5, 0.0, 197.5, 193.6, 0.0, 196.2, 0.0, 193.9, 0.0, 0.0, 192.6, 0.0, 195.6, 0.0, 193.6, 197.5, 0.0, 193.4, 0.0, 0.0, 195.7, 193.6, 195.8, 0.0, 0.0, 197.5, 0.0, 0.0, 387.5, 0.0, 194.5, 0.0, 0.0, 194.2, 0.0, 197.3, 0.0, 194.3, 193.2, 0.0, 0.0, 0.0, 391.5, 0.0, 194.9, 0.0, 193.3, 0.0, 197.5, 0.0, 194.0, 0.0, 194.4, 193.6]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.005880753000000001, "std_execution_time": 0.000495260007916549, "avg_memory_usage": 1.39921875, "std_memory_usage": 0.4289535635333605, "avg_cpu_usage": 98.79445592286501, "std_cpu_usage": 0.8444277186466759}, "original_canonical_results": {"execution_times": [0.006037377, 0.006322989, 0.005630599, 0.0062112930000000005, 0.00601591], "memory_usages": [3.13671875, 1.65234375, 3.1875, 3.22265625, 3.265625], "cpu_usages_list": [[0.0, 0.0, 196.7, 0.0, 0.0, 197.6, 197.4, 0.0, 197.7, 0.0, 193.3, 194.7, 0.0, 0.0, 193.5, 0.0, 193.4, 0.0, 195.7, 193.4, 0.0, 197.5, 0.0, 0.0, 195.0, 0.0, 0.0, 192.9, 195.7, 193.7, 0.0, 0.0, 193.7, 0.0, 394.9, 0.0, 0.0, 193.7, 0.0, 193.5, 0.0, 195.9, 0.0, 194.5, 0.0, 0.0, 197.4, 197.4, 195.7, 0.0, 0.0, 197.5, 0.0, 0.0, 193.3, 193.5, 0.0, 0.0, 197.5, 192.5, 193.5, 0.0, 0.0, 195.8, 195.1, 0.0, 194.3, 0.0, 0.0, 193.6, 0.0, 391.1, 0.0, 0.0, 197.5, 193.7, 0.0, 193.0, 0.0, 0.0, 195.9, 0.0, 193.5, 0.0, 197.5, 194.1, 0.0, 194.2, 0.0, 0.0, 193.6, 193.6, 0.0, 194.0, 0.0, 0.0, 194.2, 197.4, 194.1, 0.0, 0.0, 193.9, 197.5, 0.0, 197.5, 0.0, 193.2, 0.0, 193.0, 0.0, 193.6, 0.0, 194.2, 0.0, 195.7, 0.0, 0.0, 197.5, 0.0, 194.6], [0.0, 0.0, 195.9, 0.0, 193.4, 197.5, 0.0, 0.0, 197.5, 0.0, 193.4, 0.0, 0.0, 193.4, 0.0, 391.3, 0.0, 0.0, 197.5, 0.0, 0.0, 197.5, 193.7, 0.0, 193.7, 195.5, 0.0, 0.0, 197.5, 193.7, 0.0, 0.0, 193.1, 0.0, 0.0, 385.6, 0.0, 193.6, 0.0, 0.0, 193.5, 197.5, 0.0, 0.0, 384.5, 0.0, 0.0, 194.2, 0.0, 193.4, 0.0, 195.9, 193.7, 0.0, 0.0, 193.7, 193.6, 193.4, 0.0, 0.0, 193.6, 0.0, 0.0, 193.5, 197.5, 0.0, 193.6, 0.0, 193.4, 0.0, 0.0, 194.2, 197.3, 195.5, 0.0, 0.0, 193.4, 0.0, 193.6, 0.0, 0.0, 195.1, 193.7, 0.0, 195.5, 0.0, 193.7, 197.5, 0.0, 193.7, 0.0, 193.9, 0.0, 194.2, 0.0, 0.0, 194.5, 197.4, 0.0, 0.0, 194.4, 194.2, 0.0, 0.0, 195.7, 193.6, 0.0, 197.5, 0.0, 193.4, 195.5, 0.0, 0.0, 194.1, 194.4, 0.0, 0.0, 391.7, 0.0], [0.0, 0.0, 0.0, 193.1, 195.7, 0.0, 0.0, 197.5, 193.6, 0.0, 189.5, 0.0, 192.6, 0.0, 194.1, 0.0, 193.0, 0.0, 0.0, 193.6, 197.5, 192.5, 0.0, 191.8, 0.0, 0.0, 193.6, 191.3, 0.0, 0.0, 195.5, 0.0, 193.7, 0.0, 192.8, 193.5, 192.8, 0.0, 0.0, 0.0, 195.7, 193.5, 0.0, 196.0, 193.3, 0.0, 0.0, 193.3, 0.0, 191.2, 0.0, 192.8, 0.0, 391.2, 0.0, 0.0, 0.0, 193.4, 193.1, 0.0, 193.5, 193.5, 0.0, 192.5, 0.0, 193.5, 0.0, 193.4, 0.0, 197.5, 0.0, 0.0, 192.9, 193.5, 0.0, 193.4, 0.0, 0.0, 193.3, 197.5, 0.0, 193.6, 0.0, 0.0, 194.1, 0.0, 193.5, 0.0, 195.6, 195.6, 0.0, 194.0, 197.4, 0.0, 0.0, 194.3, 0.0, 194.4, 0.0, 195.8, 0.0, 197.5, 0.0, 194.4, 0.0, 194.5, 193.5, 0.0, 194.1, 195.6, 0.0, 0.0, 197.4, 193.8, 0.0, 193.2, 0.0, 0.0, 194.2, 0.0], [0.0, 197.6, 0.0, 0.0, 193.1, 193.1, 0.0, 0.0, 0.0, 193.5, 0.0, 197.5, 0.0, 194.3, 194.5, 193.9, 0.0, 0.0, 195.4, 0.0, 193.6, 0.0, 193.6, 0.0, 197.5, 0.0, 0.0, 193.2, 193.5, 193.3, 0.0, 195.9, 0.0, 0.0, 197.5, 0.0, 193.2, 194.0, 0.0, 193.4, 0.0, 193.4, 193.3, 0.0, 0.0, 193.5, 0.0, 197.5, 193.4, 0.0, 189.8, 0.0, 0.0, 193.4, 0.0, 190.2, 0.0, 194.2, 193.9, 0.0, 0.0, 194.2, 0.0, 392.8, 0.0, 0.0, 192.9, 0.0, 193.5, 0.0, 193.6, 0.0, 193.6, 0.0, 193.4, 193.6, 197.5, 0.0, 0.0, 0.0, 195.6, 0.0, 197.5, 195.7, 0.0, 194.3, 197.5, 0.0, 194.5, 0.0, 194.3, 0.0, 194.3, 0.0, 0.0, 0.0, 394.8, 0.0, 0.0, 197.5, 0.0, 194.0, 0.0, 195.6, 0.0, 193.5, 197.5, 193.5, 0.0, 0.0, 192.8, 0.0, 194.4, 0.0, 194.4, 0.0, 197.5, 0.0, 193.4], [0.0, 0.0, 0.0, 394.9, 0.0, 0.0, 0.0, 193.7, 194.2, 0.0, 195.7, 193.5, 0.0, 197.5, 0.0, 0.0, 191.4, 0.0, 193.5, 0.0, 193.5, 190.5, 0.0, 196.1, 0.0, 0.0, 197.5, 0.0, 193.6, 191.4, 193.5, 0.0, 0.0, 192.6, 193.2, 0.0, 0.0, 195.6, 0.0, 197.5, 193.4, 0.0, 194.3, 0.0, 0.0, 193.6, 0.0, 193.6, 0.0, 394.9, 0.0, 0.0, 195.0, 193.4, 0.0, 0.0, 195.2, 193.2, 0.0, 0.0, 193.5, 0.0, 197.5, 0.0, 0.0, 193.9, 194.5, 194.0, 0.0, 195.7, 0.0, 0.0, 197.5, 194.2, 0.0, 197.5, 0.0, 0.0, 194.4, 195.7, 0.0, 194.4, 0.0, 193.7, 0.0, 193.6, 0.0, 193.3, 0.0, 195.7, 0.0, 0.0, 197.5, 193.6, 0.0, 194.2, 0.0, 194.1, 0.0, 195.8, 194.6, 0.0, 195.5, 0.0, 193.1, 197.4, 0.0, 0.0, 193.5, 0.0, 193.4, 0.0, 195.9, 0.0, 193.5, 197.4, 0.0, 0.0, 193.2, 0.0, 194.1, 194.2, 0.0, 0.0]], "success_count": 5, "error_count": 0, "errors": [], "avg_execution_time": 0.0060436336, "std_execution_time": 0.0002634171775697251, "avg_memory_usage": 2.89296875, "std_memory_usage": 0.6951402086399324, "avg_cpu_usage": 99.26697623565555, "std_cpu_usage": 0.5008557644035595}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 392.98266786583287, "memory_usage_ratio": 1.5873813512004469, "cpu_usage_ratio": 1.014689314295602}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.3110340030000005, "std": 0.0339259313766698, "n": 5, "confidence_interval_95": [2.2689093954040582, 2.353158610595943]}, "canonical": {"mean": 0.005880753, "std": 0.000495260007916549, "n": 5, "confidence_interval_95": [0.005265806478419908, 0.006495699521580091]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 152.56790343514808, "p_value": 1.1070676567779616e-08, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 151.91713269857067, "p_value": 3.942957726203554e-15, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 96.08083098590504, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 392.9826678658329, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.22109375, "std": 0.8540879624245935, "n": 5, "confidence_interval_95": [1.1606034757581711, 3.281584024241829]}, "canonical": {"mean": 1.39921875, "std": 0.4289535635333605, "n": 5, "confidence_interval_95": [0.8666025532594142, 1.9318349467405858]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.347118189160644, "p_value": 0.07876295799935003, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.9228448536161882, "p_value": 0.09071661508115124, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 22.0, "p_value": 0.059327060946523506, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.216113864912042, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.5873813512004469, "better_performance": true}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 100.24567873657898, "std": 0.44735136433040085, "n": 5, "confidence_interval_95": [99.69021865294259, 100.80113882021537]}, "canonical": {"mean": 98.79445592286501, "std": 0.8444277186466808, "n": 5, "confidence_interval_95": [97.74596042553608, 99.84295142019394]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 3.041003995118276, "p_value": 0.0383644888655025, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 3.3957879825501944, "p_value": 0.009418956930384415, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 24.0, "p_value": 0.015873015873015872, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.1476848951773464, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0146893142956022, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 382.39148101235, "memory_usage_ratio": 0.7677558736159871, "cpu_usage_ratio": 1.0098592959918515}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 2.3110340030000005, "std": 0.0339259313766698, "n": 5, "confidence_interval_95": [2.2689093954040582, 2.353158610595943]}, "canonical": {"mean": 0.0060436336, "std": 0.0002634171775697251, "n": 5, "confidence_interval_95": [0.005716557974103223, 0.0063707092258967766]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 152.69043833967095, "p_value": 1.1035187280979578e-08, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 151.91800454500054, "p_value": 3.9427767595018954e-15, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 25.0, "p_value": 0.007936507936507936, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 96.08138239000269, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 382.39148101235, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 2.22109375, "std": 0.8540879624245935, "n": 5, "confidence_interval_95": [1.1606034757581711, 3.281584024241829]}, "canonical": {"mean": 2.89296875, "std": 0.6951402086399323, "n": 5, "confidence_interval_95": [2.029838179227941, 3.756099320772059]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.0620845874058198, "p_value": 0.3480666352682383, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.364266684303476, "p_value": 0.20962362454839004, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 0.3125, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.09523809523809523, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.862838011656974, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.7677558736159871, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 100.24567873657898, "std": 0.44735136433040085, "n": 5, "confidence_interval_95": [99.69021865294259, 100.80113882021537]}, "canonical": {"mean": 99.26697623565556, "std": 0.5008557644035587, "n": 5, "confidence_interval_95": [98.64508166470283, 99.8888708066083]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.983379129231778, "p_value": 0.04060293646408029, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 3.2587960735033086, "p_value": 0.01154806739141388, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.0625, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 23.0, "p_value": 0.031746031746031744, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2.0610436044567884, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0098592959918515, "better_performance": true}}}}}}}