{"model": "copilot", "total_tasks": 2, "tested_tasks": 2, "successful_tasks": 2, "task_results": {"HumanEval/24": {"task_id": "HumanEval/24", "entry_point": "largest_divisor", "evalperf_canonical_score": 100.00000000000001, "generated_code_results": {"execution_times": [10.339947728, 10.131430363, 9.989611389], "memory_usages": [0.53515625, 0.54296875, 0.0078125], "cpu_usages_list": [[99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 109.7, 99.9, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.9, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.7, 10.0], [99.8, 99.8, 99.8, 109.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.9, 99.8, 99.9, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.8, 99.8, 99.8, 99.8, 99.8, 69.9], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 109.8, 99.8, 99.9, 99.7, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.9, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 99.8, 109.8, 99.9, 29.9]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 10.15366316, "std_execution_time": 0.17622318392542213, "avg_memory_usage": 0.3619791666666667, "std_memory_usage": 0.3067422038494146, "avg_cpu_usage": 99.28367816091954, "std_cpu_usage": 0.5033190639522132}, "evalperf_canonical_results": {"execution_times": [0.002259259, 0.0020995180000000003, 0.0021323080000000003], "memory_usages": [0.52734375, 0.62890625, 0.5390625], "cpu_usages_list": [[], [10.0], []], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.002163695, "std_execution_time": 8.436915666877305e-05, "avg_memory_usage": 0.5651041666666666, "std_memory_usage": 0.055564032013937156, "avg_cpu_usage": 3.3333333333333335, "std_cpu_usage": 5.773502691896258}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 3, "errors": ["Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 4692.742350469914, "memory_usage_ratio": 0.6405529953917052, "cpu_usage_ratio": 29.78510344827586}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 10.153663159999999, "std": 0.17622318392542213, "n": 3, "confidence_interval_95": [9.715900503131568, 10.59142581686843]}, "canonical": {"mean": 0.002163695, "std": 8.436915666877305e-05, "n": 3, "confidence_interval_95": [0.0019541103962131263, 0.002373279603786874]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 99.81556251830366, "p_value": 0.00010035478934711458, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 99.77637697605078, "p_value": 6.04991896130701e-08, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 81.46707065830135, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 4692.742350469913, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.3619791666666667, "std": 0.30674220384941464, "n": 3, "confidence_interval_95": [-0.4000107096916286, 1.123969043024962]}, "canonical": {"mean": 0.5651041666666666, "std": 0.05556403201393716, "n": 3, "confidence_interval_95": [0.42707545932565316, 0.7031328740076801]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -1.2215865629108429, "p_value": 0.3463126082994502, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -1.1285991312962127, "p_value": 0.32217174577138236, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 1.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 3.0, "p_value": 0.7, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.9214973319413595, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.6405529953917052, "better_performance": false}}}}}, "HumanEval/9": {"task_id": "HumanEval/9", "entry_point": "rolling_max", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.055368001, 0.059036479, 0.058236089000000005], "memory_usages": [0.52734375, 0.0078125, 0.54296875], "cpu_usages_list": [[39.9], [39.9], [39.9]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.057546856333333334, "std_execution_time": 0.001928915229013795, "avg_memory_usage": 0.359375, "std_memory_usage": 0.30456227394563745, "avg_cpu_usage": 39.9, "std_cpu_usage": 0.0}, "evalperf_canonical_results": {"execution_times": [0.027220639, 0.026448416000000002, 0.026166388000000002], "memory_usages": [0.5390625, 0.5390625, 0.0078125], "cpu_usages_list": [[29.9], [29.9], [29.9]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.026611814333333334, "std_execution_time": 0.0005457889282976825, "avg_memory_usage": 0.3619791666666667, "std_memory_usage": 0.3067173305069887, "avg_cpu_usage": 29.9, "std_cpu_usage": 0.0}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 3, "errors": ["Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 2.162455201757946, "memory_usage_ratio": 0.9928057553956834, "cpu_usage_ratio": 1.334448160535117}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.05754685633333334, "std": 0.001928915229013795, "n": 3, "confidence_interval_95": [0.052755165270230026, 0.062338547396436655]}, "canonical": {"mean": 0.026611814333333334, "std": 0.0005457889282976825, "n": 3, "confidence_interval_95": [0.025255999473910065, 0.027967629192756603]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 22.06730876835025, "p_value": 0.0020472270553352945, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 26.728462255686306, "p_value": 1.1646984113971773e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 21.823698045223644, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.1624552017579464, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.359375, "std": 0.30456227394563745, "n": 3, "confidence_interval_95": [-0.39719963027541716, 1.1159496302754173]}, "canonical": {"mean": 0.3619791666666667, "std": 0.3067173305069887, "n": 3, "confidence_interval_95": [-0.39994892088369177, 1.1239072542170252]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.008458417805961954, "p_value": 0.9940191023859171, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.010435224487670534, "p_value": 0.9921737591811091, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.5, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.008520325115396271, "interpretation": "negligible", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9928057553956834, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 39.9, "std": 0.0, "n": 3, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 29.899999999999995, "std": 4.351167857633658e-15, "n": 3, "confidence_interval_95": [29.899999999999984, 29.900000000000006]}}, "statistical_tests": {"paired_t_test": {"t_statistic": Infinity, "p_value": 0.0, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 3980657295328609.0, "p_value": 2.389637866698647e-62, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.04685417760387376, "significant": true, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 3250193071480819.0, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.3344481605351173, "better_performance": true}}}}}}}