{"model": "copilot", "total_tasks": 1, "tested_tasks": 1, "successful_tasks": 1, "task_results": {"Mbpp/3": {"task_id": "Mbpp/3", "entry_point": "is_not_prime", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [6.0054731960000005, 6.1918073950000005, 6.114437876], "memory_usages": [0.0, 0.0, 0.0], "cpu_usages_list": [[79.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 119.8, 79.9, 119.8, 79.9, 119.8, 79.9, 119.8, 79.9, 119.8, 79.9, 119.8, 79.9, 119.8, 79.9, 119.8, 79.9, 119.8, 99.9, 99.9, 99.9, 99.9, 99.9, 99.8, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9, 99.9], [59.8, 119.6, 99.7, 99.7, 99.6, 99.9, 99.9, 99.7, 99.6, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.6, 99.7, 99.8, 99.7, 99.8, 99.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 119.6, 79.8, 119.8, 79.7, 119.8, 79.7, 119.6, 79.8, 119.6, 79.8, 119.6, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.6, 99.6, 99.7, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.9, 119.6, 79.8, 119.6, 79.7, 119.6, 79.8, 119.6, 79.8, 119.6, 99.7, 99.7, 99.7, 99.7, 19.9], [99.7, 79.7, 119.8, 79.9, 119.8, 79.8, 119.6, 79.7, 119.6, 79.8, 119.7, 99.7, 99.7, 99.6, 99.8, 99.6, 99.6, 99.7, 99.7, 99.7, 99.9, 99.6, 99.8, 99.7, 99.8, 99.6, 99.7, 99.7, 99.7, 99.9, 99.7, 99.9, 99.6, 99.6, 99.8, 99.7, 99.6, 99.7, 99.6, 99.7, 99.7, 99.9, 99.7, 99.7, 99.6, 99.7, 99.6, 119.6, 79.9, 119.8, 79.7, 119.8, 79.7, 119.6, 79.7, 119.6, 99.6, 99.9, 99.9, 99.6, 99.7, 99.6, 99.7, 99.7, 99.7, 99.6, 99.7, 99.7, 99.6, 99.8, 99.7, 99.7, 99.7, 99.6, 99.9, 99.7, 99.7, 99.7, 99.5, 99.6, 99.9, 99.7, 99.7, 99.9, 99.7, 99.9, 99.7, 99.8, 99.7, 99.9, 99.6, 99.7, 119.6, 79.7, 119.7, 79.7, 119.5, 39.9]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 6.103906155666667, "std_execution_time": 0.0936124792916391, "avg_memory_usage": 0.0, "std_memory_usage": 0.0, "avg_cpu_usage": 99.50197482929441, "std_cpu_usage": 0.3903255669636607}, "evalperf_canonical_results": {"execution_times": [0.000366844, 0.00038939400000000005, 0.00035982], "memory_usages": [0.0, 0.0, 0.0], "cpu_usages_list": [[0.0], [0.0], [20.0]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.00037201933333333334, "std_execution_time": 1.5451324387680622e-05, "avg_memory_usage": 0.0, "std_memory_usage": 0.0, "avg_cpu_usage": 6.666666666666667, "std_cpu_usage": 11.547005383792516}, "original_canonical_results": {"execution_times": [0.00038228800000000005, 0.00039029600000000005, 0.000407544], "memory_usages": [0.0, 0.0, 0.0], "cpu_usages_list": [[19.9], [19.9], [0.0]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.000393376, "std_execution_time": 1.2906633333290276e-05, "avg_memory_usage": 0.0, "std_memory_usage": 0.0, "avg_cpu_usage": 13.266666666666666, "std_cpu_usage": 11.48927035687355}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 16407.49716143785, "memory_usage_ratio": Infinity, "cpu_usage_ratio": 14.925296224394161}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 6.103906155666667, "std": 0.0936124792916391, "n": 3, "confidence_interval_95": [5.871359865570741, 6.3364524457625935]}, "canonical": {"mean": 0.00037201933333333334, "std": 1.5451324387680622e-05, "n": 3, "confidence_interval_95": [0.000333636115725884, 0.00041040255094078267]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 112.9420172861776, "p_value": 7.838588261416882e-05, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 112.92972011692142, "p_value": 3.6871537777429824e-08, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 92.20673036059137, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 16407.49716143785, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.0, "std": 0.0, "n": 3, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 3, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.5, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": NaN, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 99.50197482929444, "std": 0.39032556696365356, "n": 3, "confidence_interval_95": [98.53235236855171, 100.47159729003717]}, "canonical": {"mean": 6.666666666666667, "std": 11.547005383792515, "n": 3, "confidence_interval_95": [-22.017684864640945, 35.35101819797428]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 13.92273849595727, "p_value": 0.005119243455911122, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 13.917347128856367, "p_value": 0.00015456937869650245, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.07652250047505922, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 11.363466346295528, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 14.925296224394165, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 15516.722310630712, "memory_usage_ratio": Infinity, "cpu_usage_ratio": 7.500148856479479}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 6.103906155666667, "std": 0.0936124792916391, "n": 3, "confidence_interval_95": [5.871359865570741, 6.3364524457625935]}, "canonical": {"mean": 0.000393376, "std": 1.2906633333290276e-05, "n": 3, "confidence_interval_95": [0.0003613141454054371, 0.00042543785459456295]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 112.93557533606581, "p_value": 7.839482422474857e-05, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 112.92932543338033, "p_value": 3.6872053103727854e-08, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 92.20640810282953, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 15516.722310630712, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.0, "std": 0.0, "n": 3, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 3, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.5, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": NaN, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 99.50197482929444, "std": 0.39032556696365356, "n": 3, "confidence_interval_95": [98.53235236855171, 100.47159729003717]}, "canonical": {"mean": 13.266666666666666, "std": 11.489270356873552, "n": 3, "confidence_interval_95": [-15.27426310698441, 41.80759644031774]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 12.987749375601492, "p_value": 0.005876125613941165, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 12.992801935839493, "p_value": 0.0002024793884095499, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.07652250047505922, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 10.608578357284086, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 7.500148856479481, "better_performance": true}}}}}}}