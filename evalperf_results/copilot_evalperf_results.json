{"model": "copilot", "total_tasks": 1, "tested_tasks": 1, "successful_tasks": 1, "task_results": {"HumanEval/9": {"task_id": "HumanEval/9", "entry_point": "rolling_max", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.0059773330000000005, 0.00583294], "memory_usages": [0.0, 0.0], "cpu_usages_list": [[59.8], [59.8]], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.0059051365, "std_execution_time": 0.00010210126945586949, "avg_memory_usage": 0.0, "std_memory_usage": 0.0, "avg_cpu_usage": 59.8, "std_cpu_usage": 0.0}, "evalperf_canonical_results": {"execution_times": [0.002776693, 0.003029784], "memory_usages": [0.0, 0.0], "cpu_usages_list": [[39.9], [59.8]], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.0029032385000000004, "std_execution_time": 0.0001789623623572845, "avg_memory_usage": 0.0, "std_memory_usage": 0.0, "avg_cpu_usage": 49.849999999999994, "std_cpu_usage": 14.071424945612295}, "original_canonical_results": {"execution_times": [0.05045882402919233, 0.050285706063732505], "memory_usages": [0.0, 0.0], "cpu_usages_list": [[0.0], [0.0]], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.05037226504646242, "std_execution_time": 0.00012241288732185978, "avg_memory_usage": 0.0, "std_memory_usage": 0.0, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 2.033982568087327, "memory_usage_ratio": Infinity, "cpu_usage_ratio": 1.1995987963891677}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0059051365, "std": 0.00010210126945586949, "n": 2, "confidence_interval_95": [0.004987792989746177, 0.006822480010253823]}, "canonical": {"mean": 0.0029032385000000004, "std": 0.0001789623623572845, "n": 2, "confidence_interval_95": [0.0012953254685258327, 0.004511151531474168]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 15.104497287941133, "p_value": 0.042086278217002476, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 20.60444149941866, "p_value": 0.0023471841026173447, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 20.60444149941866, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.033982568087327, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.0, "std": 0.0, "n": 2, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 2, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 2.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": NaN, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 59.8, "std": 0.0, "n": 2, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 49.849999999999994, "std": 14.071424945612295, "n": 2, "confidence_interval_95": [-76.57673712749934, 176.27673712749933]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.0, "p_value": 0.49999999999999956, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.0000000000000004, "p_value": 0.42264973081037405, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 3.0, "p_value": 0.6170750774519738, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.0000000000000004, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.1995987963891677, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 0.11722991798270764, "memory_usage_ratio": Infinity, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.0059051365, "std": 0.00010210126945586949, "n": 2, "confidence_interval_95": [0.004987792989746177, 0.006822480010253823]}, "canonical": {"mean": 0.05037226504646242, "std": 0.00012241288732185978, "n": 2, "confidence_interval_95": [0.04927242889011887, 0.051472101202805966]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -3096.0614110158695, "p_value": 0.00020562245566719173, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -394.50753676895005, "p_value": 6.42517861716485e-06, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 0.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -394.50753676895005, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.11722991798270764, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.0, "std": 0.0, "n": 2, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 2, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": NaN, "p_value": NaN, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 2.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": NaN, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 59.8, "std": 0.0, "n": 2, "confidence_interval_95": [NaN, NaN]}, "canonical": {"mean": 0.0, "std": 0.0, "n": 2, "confidence_interval_95": [NaN, NaN]}}, "statistical_tests": {"paired_t_test": {"t_statistic": Infinity, "p_value": 0.0, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": Infinity, "p_value": 0.0, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.1939308522824107, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": Infinity, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": Infinity, "better_performance": true}}}}}}}