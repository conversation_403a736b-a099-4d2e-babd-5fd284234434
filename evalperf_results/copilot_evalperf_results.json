{"model": "copilot", "total_tasks": 1, "tested_tasks": 1, "successful_tasks": 1, "task_results": {"Mbpp/3": {"task_id": "Mbpp/3", "entry_point": "is_not_prime", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [6.249178408000001, 6.244372353], "memory_usages": [3.55859375, 3.140625], "cpu_usages_list": [[79.7, 99.7, 99.7, 99.7, 99.6, 119.6, 79.7, 119.6, 79.7, 119.6, 79.8, 119.6, 99.9, 99.7, 99.9, 99.7, 99.8, 99.7, 99.8, 99.7, 99.7, 99.7, 99.8, 99.7, 99.6, 99.8, 99.7, 99.7, 99.6, 99.7, 99.7, 99.6, 99.9, 99.5, 99.7, 99.7, 99.7, 99.7, 99.8, 99.6, 99.6, 99.6, 99.6, 99.6, 99.9, 99.9, 99.8, 99.7, 119.5, 79.7, 119.6, 79.8, 119.7, 79.7, 119.8, 79.9, 119.5, 99.7, 99.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.9, 99.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 119.6, 79.8, 119.8, 79.8, 119.6], [99.6, 79.7, 119.6, 79.8, 119.7, 79.7, 119.6, 79.7, 119.6, 99.7, 99.7, 99.9, 99.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.8, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.6, 99.7, 99.7, 99.8, 99.9, 99.7, 99.7, 99.7, 99.6, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 119.6, 79.8, 119.8, 79.8, 119.6, 79.8, 119.7, 79.8, 119.6, 99.6, 99.7, 99.9, 99.8, 99.8, 99.7, 99.7, 99.7, 99.7, 99.6, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.9, 99.7, 99.7, 99.7, 99.7, 99.7, 99.6, 99.7, 99.7, 99.9, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 99.7, 119.7, 79.8, 119.6, 79.8, 119.6, 79.7]], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 6.246775380500001, "std_execution_time": 0.0033983940812558097, "avg_memory_usage": 3.349609375, "std_memory_usage": 0.2955485374490648, "avg_cpu_usage": 100.01632653061225, "std_cpu_usage": 0.1428644313009552}, "evalperf_canonical_results": {"execution_times": [0.000366803, 0.00036510700000000004], "memory_usages": [3.5703125, 1.6171875], "cpu_usages_list": [[], []], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.00036595500000000006, "std_execution_time": 1.1992531008923693e-06, "avg_memory_usage": 2.59375, "std_memory_usage": 1.3810679320049757, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "original_canonical_results": {"execution_times": [0.00039132800000000003, 0.00036063100000000004], "memory_usages": [1.67578125, 3.203125], "cpu_usages_list": [[], []], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.00037597950000000003, "std_execution_time": 2.1706056862083444e-05, "avg_memory_usage": 2.439453125, "std_memory_usage": 1.079995122827891, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 17069.79104124824, "memory_usage_ratio": 1.2914156626506024, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 6.246775380500001, "std": 0.0033983940812558097, "n": 2, "confidence_interval_95": [6.216242021097721, 6.27730873990228]}, "canonical": {"mean": 0.00036595500000000006, "std": 1.1992531008923693e-06, "n": 2, "confidence_interval_95": [0.0003551801383835058, 0.0003767298616164943]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2600.3091881765904, "p_value": 0.0002448246323468775, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 2599.391407949596, "p_value": 1.4799823817349153e-07, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2599.391407949596, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 17069.79104124824, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.349609375, "std": 0.2955485374490648, "n": 2, "confidence_interval_95": [0.6942111195346992, 6.0050076304653]}, "canonical": {"mean": 2.59375, "std": 1.3810679320049757, "n": 2, "confidence_interval_95": [-9.814653062921968, 15.002153062921968]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.9847328244274809, "p_value": 0.5048969785862338, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.7568633444506128, "p_value": 0.5281424549823139, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 1.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 2.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.7568633444506127, "interpretation": "medium", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.2914156626506024, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 16614.670162867922, "memory_usage_ratio": 1.3730984787830265, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 6.246775380500001, "std": 0.0033983940812558097, "n": 2, "confidence_interval_95": [6.216242021097721, 6.27730873990228]}, "canonical": {"mean": 0.00037597950000000003, "std": 2.1706056862083444e-05, "n": 2, "confidence_interval_95": [0.0001809583166028721, 0.000571000683397128]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2616.0968040507314, "p_value": 0.00024334716527899767, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 2599.334377895381, "p_value": 1.480047324809084e-07, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 2599.334377895381, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 16614.670162867922, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 3.349609375, "std": 0.2955485374490648, "n": 2, "confidence_interval_95": [0.6942111195346992, 6.0050076304653]}, "canonical": {"mean": 2.439453125, "std": 1.079995122827891, "n": 2, "confidence_interval_95": [-7.26391807020498, 12.14282432020498]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.9357429718875502, "p_value": 0.5211248496068273, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.149549002869462, "p_value": 0.36924197378789025, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 1.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 3.0, "p_value": 0.6666666666666666, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.149549002869462, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.3730984787830265, "better_performance": true}}}}}}}