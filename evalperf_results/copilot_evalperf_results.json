{"model": "copilot", "total_tasks": 1, "tested_tasks": 1, "successful_tasks": 1, "task_results": {"Mbpp/4": {"task_id": "Mbpp/4", "entry_point": "heap_queue_largest", "evalperf_canonical_score": 100.00000000000001, "generated_code_results": {"execution_times": [0.08360360600000001, 0.082180997], "memory_usages": [1.62109375, 1.609375], "cpu_usages_list": [[0.0, 0.0, 197.4, 0.0, 194.2, 192.7, 0.0, 0.0, 194.1, 193.5, 0.0, 0.0, 197.5, 194.3, 0.0, 197.4, 0.0, 194.5, 0.0], [0.0, 0.0, 194.3, 0.0, 196.1, 0.0, 193.2, 0.0, 193.0, 0.0, 195.6, 0.0, 195.8, 0.0, 197.5, 0.0, 193.4, 193.2]], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.08289230150000002, "std_execution_time": 0.0010059364708770169, "avg_memory_usage": 1.615234375, "std_memory_usage": 0.008286407592029853, "avg_cpu_usage": 94.86944444444444, "std_cpu_usage": 3.4923218248602224}, "evalperf_canonical_results": {"execution_times": [0.00043709200000000004, 0.000462545], "memory_usages": [3.1328125, 1.6015625], "cpu_usages_list": [[0.0, 0.0, 0.0], [0.0, 0.0, 197.0]], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.0004498185, "std_execution_time": 1.7997988901541184e-05, "avg_memory_usage": 2.3671875, "std_memory_usage": 1.082757258691901, "avg_cpu_usage": 32.833333333333336, "std_cpu_usage": 46.433345297916624}, "original_canonical_results": {"execution_times": [0.001111328, 0.000999773], "memory_usages": [1.609375, 1.640625], "cpu_usages_list": [[0.0, 0.0, 197.5], [0.0, 0.0, 0.0]], "success_count": 2, "error_count": 0, "errors": [], "avg_execution_time": 0.0010555504999999999, "std_execution_time": 7.888129697526527e-05, "avg_memory_usage": 1.625, "std_memory_usage": 0.02209708691207961, "avg_cpu_usage": 32.916666666666664, "std_cpu_usage": 46.55119642811437}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 184.27944048544026, "memory_usage_ratio": 0.6823432343234324, "cpu_usage_ratio": 2.8894247038917085}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.08289230150000002, "std": 0.0010059364708770169, "n": 2, "confidence_interval_95": [0.07385432089305452, 0.09193028210694551]}, "canonical": {"mean": 0.0004498185, "std": 1.7997988901541188e-05, "n": 2, "confidence_interval_95": [0.000288112985421797, 0.000611524014578203]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 113.86595739685181, "p_value": 0.005590814152043259, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 115.88467471301422, "p_value": 7.445596248511974e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 115.88467471301422, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 184.27944048544026, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.615234375, "std": 0.008286407592029853, "n": 2, "confidence_interval_95": [1.5407839566224681, 1.6896847933775319]}, "canonical": {"mean": 2.3671875, "std": 1.082757258691901, "n": 2, "confidence_interval_95": [-7.361000501330823, 12.095375501330823]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.9897172236503856, "p_value": 0.5032899953740981, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.9821140966737497, "p_value": 0.42959582689926123, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 1.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 2.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.9821140966737496, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.6823432343234324, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 94.86944444444444, "std": 3.4923218248602423, "n": 2, "confidence_interval_95": [63.49217774808837, 126.2467111408005]}, "canonical": {"mean": 32.833333333333336, "std": 46.433345297916624, "n": 2, "confidence_interval_95": [-384.3537221795205, 450.02038884618713]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 2.0430884640014644, "p_value": 0.28977412524464047, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.884103248887117, "p_value": 0.20023154685418434, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.884103248887117, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.8894247038917085, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 78.52992490648246, "memory_usage_ratio": 0.9939903846153846, "cpu_usage_ratio": 2.8821097046413504}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.08289230150000002, "std": 0.0010059364708770169, "n": 2, "confidence_interval_95": [0.07385432089305452, 0.09193028210694551]}, "canonical": {"mean": 0.0010555504999999999, "std": 7.888129697526527e-05, "n": 2, "confidence_interval_95": [0.00034683016531365915, 0.0017642708346863405]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 124.84115986069169, "p_value": 0.005099329081838966, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 114.69953714333191, "p_value": 7.600237363439563e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 114.69953714333191, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 78.52992490648246, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.615234375, "std": 0.008286407592029853, "n": 2, "confidence_interval_95": [1.5407839566224681, 1.6896847933775319]}, "canonical": {"mean": 1.625, "std": 0.02209708691207961, "n": 2, "confidence_interval_95": [1.4264655509932485, 1.8235344490067515]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.45454545454545453, "p_value": 0.7284005024398164, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.5852057359806528, "p_value": 0.6176404435490637, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 1.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 1.5, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.5852057359806527, "interpretation": "medium", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.9939903846153846, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 94.86944444444444, "std": 3.4923218248602423, "n": 2, "confidence_interval_95": [63.49217774808837, 126.2467111408005]}, "canonical": {"mean": 32.916666666666664, "std": 46.55119642811437, "n": 2, "confidence_interval_95": [-385.32923924088965, 451.162572574223]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.7507653661982885, "p_value": 0.33037878282272426, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.876835545124203, "p_value": 0.2013474199139072, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 4.0, "p_value": 0.3333333333333333, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 1.876835545124203, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.8821097046413504, "better_performance": true}}}}}}}