{"model": "copilot", "total_tasks": 2, "tested_tasks": 2, "successful_tasks": 2, "task_results": {"HumanEval/24": {"task_id": "HumanEval/24", "entry_point": "largest_divisor", "evalperf_canonical_score": 100.00000000000001, "generated_code_results": {"execution_times": [0.9494611190000001, 0.9416346210000001, 0.9336599130000001], "memory_usages": [0.5390625, 0.52734375, 0.5546875], "cpu_usages_list": [[99.8, 99.8, 99.8, 99.8, 99.8, 99.7, 89.8], [99.8, 99.8, 99.8, 99.8, 99.8, 99.8, 89.8], [99.8, 99.8, 99.8, 99.8, 109.7, 99.9, 79.9]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.9415852176666667, "std_execution_time": 0.007900718845815072, "avg_memory_usage": 0.5403645833333334, "std_memory_usage": 0.013718299157360338, "avg_cpu_usage": 98.37142857142857, "std_cpu_usage": 0.014285714285712459}, "evalperf_canonical_results": {"execution_times": [8.5925e-05, 8.332100000000001e-05, 8.4066e-05], "memory_usages": [0.53125, 0.0078125, 0.52734375], "cpu_usages_list": [[], [], []], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 8.443733333333334e-05, "std_execution_time": 1.3411265165275503e-06, "avg_memory_usage": 0.35546875, "std_memory_usage": 0.3010854792584865, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 3, "errors": ["Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 11151.290317868872, "memory_usage_ratio": 1.5201465201465203, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.9415852176666668, "std": 0.00790071884581507, "n": 3, "confidence_interval_95": [0.9219587440320842, 0.9612116913012494]}, "canonical": {"mean": 8.443733333333334e-05, "std": 1.3411265165275503e-06, "n": 3, "confidence_interval_95": [8.110579037744019e-05, 8.776887628922649e-05]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 206.4265285633372, "p_value": 2.346679039223248e-05, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 206.40237872441492, "p_value": 3.305403628096108e-09, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 168.52683652383442, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 11151.290317868874, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.5403645833333334, "std": 0.013718299157360338, "n": 3, "confidence_interval_95": [0.5062864390558841, 0.5744427276107826]}, "canonical": {"mean": 0.35546875, "std": 0.3010854792584865, "n": 3, "confidence_interval_95": [-0.3924690434771595, 1.1034065434771594]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.1044314913115554, "p_value": 0.384502306837625, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.0625456975878669, "p_value": 0.34788083890608457, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 7.5, "p_value": 0.26828588367711736, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.8675649291599588, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.5201465201465203, "better_performance": true}}}}}, "HumanEval/9": {"task_id": "HumanEval/9", "entry_point": "rolling_max", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.006096749, 0.00577026, 0.0058102150000000005], "memory_usages": [0.53125, 0.0078125, 0.53125], "cpu_usages_list": [[], [], []], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.005892408, "std_execution_time": 0.00017808855582827324, "avg_memory_usage": 0.3567708333333333, "std_memory_usage": 0.3022067815289447, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "evalperf_canonical_results": {"execution_times": [0.002678417, 0.002847618, 0.002813641], "memory_usages": [0.53125, 0.5234375, 0.0078125], "cpu_usages_list": [[], [], []], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.002779892, "std_execution_time": 8.950693186005197e-05, "avg_memory_usage": 0.3541666666666667, "std_memory_usage": 0.2999769413881396, "avg_cpu_usage": 0.0, "std_cpu_usage": 0.0}, "original_canonical_results": {"execution_times": [], "memory_usages": [], "cpu_usages_list": [], "success_count": 0, "error_count": 3, "errors": ["Performance test failed", "Performance test failed", "Performance test failed"]}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 2.119653569275353, "memory_usage_ratio": 1.0073529411764706, "cpu_usage_ratio": Infinity}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.005892408000000001, "std": 0.00017808855582827324, "n": 3, "confidence_interval_95": [0.005450011502441315, 0.006334804497558686]}, "canonical": {"mean": 0.0027798920000000004, "std": 8.950693186005197e-05, "n": 3, "confidence_interval_95": [0.0025575444551061335, 0.0030022395448938673]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 20.160054504715223, "p_value": 0.0024514178979918397, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 27.047628982602028, "p_value": 1.1109311772765709e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 22.084296586496222, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.119653569275353, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 0.3567708333333333, "std": 0.3022067815289447, "n": 3, "confidence_interval_95": [-0.39395242940010805, 1.1074940960667747]}, "canonical": {"mean": 0.3541666666666667, "std": 0.2999769413881396, "n": 3, "confidence_interval_95": [-0.3910173660818946, 1.099350699415228]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.008681877134475364, "p_value": 0.9938611014835486, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.010592839965175745, "p_value": 0.9920555557400426, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 1.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 5.5, "p_value": 0.8136637157667919, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.008649017613880568, "interpretation": "negligible", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0073529411764706, "better_performance": true}}}}}}}