{"model": "copilot", "total_tasks": 1, "tested_tasks": 1, "successful_tasks": 1, "task_results": {"HumanEval/9": {"task_id": "HumanEval/9", "entry_point": "rolling_max", "evalperf_canonical_score": 100.0, "generated_code_results": {"execution_times": [0.0058786970000000004, 0.006011596, 0.005733618], "memory_usages": [0.62890625, 1.57421875, 1.62109375], "cpu_usages_list": [[0.0, 0.0, 194.3, 0.0, 197.4, 0.0], [0.0, 0.0, 0.0, 194.3, 0.0, 194.3], [0.0, 0.0, 0.0, 197.5, 0.0, 197.5]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.005874637, "std_execution_time": 0.00013903346655032373, "avg_memory_usage": 1.2747395833333333, "std_memory_usage": 0.5597989257220697, "avg_cpu_usage": 65.29444444444444, "std_cpu_usage": 0.5334201318257823}, "evalperf_canonical_results": {"execution_times": [0.002686066, 0.002954523, 0.0027732800000000004], "memory_usages": [3.53125, 0.63671875, 1.609375], "cpu_usages_list": [[0.0, 0.0, 197.2, 0.0, 0.0, 193.4], [0.0, 0.0, 0.0, 194.2, 0.0, 195.6], [0.0, 0.0, 197.5, 195.7, 0.0, 0.0]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.002804623, "std_execution_time": 0.00013694552913111116, "avg_memory_usage": 1.92578125, "std_memory_usage": 1.472977418559408, "avg_cpu_usage": 65.2, "std_cpu_usage": 0.2962731472438521}, "original_canonical_results": {"execution_times": [0.008370138006284833, 0.013648463180288672, 0.015009291004389524], "memory_usages": [1.671875, 3.140625, 3.1875], "cpu_usages_list": [[0.0], [0.0, 0.0], [0.0, 193.0]], "success_count": 3, "error_count": 0, "errors": [], "avg_execution_time": 0.01234263073032101, "std_execution_time": 0.003506919862728836, "avg_memory_usage": 2.6666666666666665, "std_memory_usage": 0.8618336042754038, "avg_cpu_usage": 32.166666666666664, "std_cpu_usage": 55.71430097679889}, "performance_comparison": {"vs_evalperf_canonical": {"execution_time_ratio": 2.0946262652770087, "memory_usage_ratio": 0.6619337390128465, "cpu_usage_ratio": 1.0014485344239943}, "statistical_analysis_vs_evalperf": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.005874636999999999, "std": 0.0001390334665503237, "n": 3, "confidence_interval_95": [0.005529258722551242, 0.006220015277448756]}, "canonical": {"mean": 0.0028046230000000004, "std": 0.00013694552913111116, "n": 3, "confidence_interval_95": [0.0024644314466340197, 0.003144814553365981]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 45.57035445897651, "p_value": 0.0004811955781047682, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 27.247554041870288, "p_value": 1.0788260660488344e-05, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 9.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 22.247534713830532, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.0946262652770082, "better_performance": false}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.2747395833333333, "std": 0.5597989257220698, "n": 3, "confidence_interval_95": [-0.11587803903294924, 2.6653572056996158]}, "canonical": {"mean": 1.92578125, "std": 1.472977418559408, "n": 3, "confidence_interval_95": [-1.7332975039547316, 5.584860003954732]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -0.5627266882294109, "p_value": 0.6302857087725349, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -0.7156122870230396, "p_value": 0.5137823700759291, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 3.0, "p_value": 1.0, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 3.0, "p_value": 0.7, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -0.5842949856241824, "interpretation": "medium", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.6619337390128465, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 65.29444444444444, "std": 0.5334201318257822, "n": 3, "confidence_interval_95": [63.9693553787856, 66.61953351010328]}, "canonical": {"mean": 65.2, "std": 0.29627314724385534, "n": 3, "confidence_interval_95": [64.46401670196171, 65.9359832980383]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 0.6253551088610968, "p_value": 0.5955818421406409, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 0.26809078404602055, "p_value": 0.8018868710084102, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 2.0, "p_value": 0.75, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 5.0, "p_value": 1.0, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.21889520855180916, "interpretation": "small", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 1.0014485344239943, "better_performance": true}}}, "vs_original_canonical": {"execution_time_ratio": 0.47596311745504283, "memory_usage_ratio": 0.47802734375, "cpu_usage_ratio": 2.029879101899827}, "statistical_analysis_vs_original": {"execution_time": {"metric": "execution_time", "descriptive_stats": {"generated": {"mean": 0.005874636999999999, "std": 0.0001390334665503237, "n": 3, "confidence_interval_95": [0.005529258722551242, 0.006220015277448756]}, "canonical": {"mean": 0.01234263073032101, "std": 0.003506919862728836, "n": 3, "confidence_interval_95": [0.003630958847337976, 0.021054302613304043]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -3.1647161014099487, "p_value": 0.08701186425561633, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -3.1920033593499446, "p_value": 0.033152750579973474, "significant": true, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 0.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -2.6062598292190455, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.4759631174550428, "better_performance": true}}, "memory_usage": {"metric": "memory_usage", "descriptive_stats": {"generated": {"mean": 1.2747395833333333, "std": 0.5597989257220698, "n": 3, "confidence_interval_95": [-0.11587803903294924, 2.6653572056996158]}, "canonical": {"mean": 2.6666666666666665, "std": 0.8618336042754038, "n": 3, "confidence_interval_95": [0.5257533090589566, 4.807580024274376]}}, "statistical_tests": {"paired_t_test": {"t_statistic": -7.977611940298507, "p_value": 0.015351921789913518, "significant": true, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": -2.3459460047580545, "p_value": 0.07886392707508269, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 0.0, "p_value": 0.25, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 0.0, "p_value": 0.1, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": -1.9154568919260102, "interpretation": "large", "direction": "generated_better"}, "performance_ratio": {"mean_ratio": 0.47802734375, "better_performance": false}}, "cpu_usage": {"metric": "cpu_usage", "descriptive_stats": {"generated": {"mean": 65.29444444444444, "std": 0.5334201318257822, "n": 3, "confidence_interval_95": [63.9693553787856, 66.61953351010328]}, "canonical": {"mean": 32.166666666666664, "std": 55.71430097679889, "n": 3, "confidence_interval_95": [-106.23532947189258, 170.5686628052259]}}, "statistical_tests": {"paired_t_test": {"t_statistic": 1.0385674149452941, "p_value": 0.4080888938740621, "significant": false, "test_type": "paired_t_test"}, "independent_t_test": {"t_statistic": 1.029831903005323, "p_value": 0.36128363707579003, "significant": false, "test_type": "independent_t_test"}, "wilcoxon_signed_rank": {"w_statistic": 1.0, "p_value": 0.5, "significant": false, "test_type": "wilcoxon_signed_rank"}, "mann_whitney_u": {"u_statistic": 6.0, "p_value": 0.6579050194284821, "significant": false, "test_type": "mann_whitney_u"}}, "effect_size": {"cohens_d": 0.8408542277341398, "interpretation": "large", "direction": "canonical_better"}, "performance_ratio": {"mean_ratio": 2.029879101899827, "better_performance": true}}}}}}}