#!/usr/bin/env python3
"""
EvalPerf原始方法兼容测试器
完全遵循原始方法的预处理+独立脚本运行流程
"""

import json
import os
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional
import statistics
import numpy as np
from scipy import stats
import math

class EvalPerfOriginalMethodTester:
    def __init__(self,
                 evalperf_dataset_path: str,
                 original_humaneval_path: str,
                 original_mbpp_path: str):
        """
        初始化原始方法兼容测试器
        """
        self.evalperf_dataset_path = evalperf_dataset_path
        self.original_humaneval_path = original_humaneval_path
        self.original_mbpp_path = original_mbpp_path
        
        # 创建临时工作目录
        self.temp_dir = tempfile.mkdtemp(prefix="evalperf_original_")
        self.scripts_dir = os.path.join(self.temp_dir, "scripts")
        self.results_dir = os.path.join(self.temp_dir, "results")
        
        os.makedirs(self.scripts_dir, exist_ok=True)
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 加载数据集
        self.evalperf_data = self._load_evalperf_data()
        self.original_humaneval_data = self._load_original_data(original_humaneval_path)
        self.original_mbpp_data = self._load_original_data(original_mbpp_path)
        
        print(f"临时工作目录: {self.temp_dir}")
        print(f"加载了 {len(self.evalperf_data)} 个EvalPerf任务")

    def _load_evalperf_data(self) -> Dict[str, Any]:
        """加载EvalPerf数据集"""
        evalperf_data = {}
        with open(self.evalperf_dataset_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    item = json.loads(line.strip())
                    task_id = item['task_id']
                    evalperf_data[task_id] = item
        return evalperf_data

    def _load_original_data(self, file_path: str) -> Dict[str, Any]:
        """加载原始数据集"""
        data = {}
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    item = json.loads(line.strip())
                    task_id = item['task_id']
                    data[task_id] = item
        return data

    def _copy_original_scripts(self):
        """复制原始性能测试脚本"""
        original_scripts = [
            "replication-final/scripts/scripts_for_humaneval/cprofile.py",
            "replication-final/scripts/scripts_for_humaneval/memory.py", 
            "replication-final/scripts/scripts_for_humaneval/cpu.py"
        ]
        
        for script_path in original_scripts:
            if os.path.exists(script_path):
                script_name = os.path.basename(script_path)
                dest_path = os.path.join(self.scripts_dir, script_name)
                shutil.copy2(script_path, dest_path)
                print(f"复制脚本: {script_name}")
            else:
                print(f"警告: 找不到原始脚本 {script_path}")

    def _create_modified_scripts(self):
        """创建修改后的原始脚本，支持指定目录"""
        
        # 修改后的cprofile.py
        cprofile_script = '''import cProfile
import pstats
import os
import sys

def profile_function(file_path, profile_file):
    with open(file_path, "rb") as file:
        code = compile(file.read(), file_path, 'exec')
        cProfile.run(code, profile_file)

def extract_cumtime(profile_file):
    p = pstats.Stats(profile_file)
    cumtime = p.stats[list(p.stats.keys())[0]][3]
    return cumtime

def save_times_to_file(times, output_file):
    with open(output_file, 'w') as file:
        for time in times:
            file.write(f"{time}\\n")

def profile_folder(folder_path, output_file):
    run_times = []
    profile_data = "profile_data"
    file_list = [filename for filename in os.listdir(folder_path) if filename.endswith('.py')]
    
    file_list.sort()
    for filename in file_list:
        file_path = os.path.join(folder_path, filename)
        try:
            profile_function(file_path, profile_data)
            run_times.append(extract_cumtime(profile_data))
            print(f"Profiled {filename}: {run_times[-1]:.6f}s")
        except Exception as e:
            print(f"Error profiling {filename}: {e}")
            run_times.append(0.0)
    
    save_times_to_file(run_times, output_file)
    return run_times

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python cprofile.py <folder_path> <output_file>")
        sys.exit(1)
    
    folder_path = sys.argv[1]
    output_file = sys.argv[2]
    
    times = profile_folder(folder_path, output_file)
    print(f"Execution times saved to {output_file}")
'''

        # 修改后的memory.py
        memory_script = '''import os
import subprocess
import sys
from memory_profiler import memory_usage

def run_python_script(filepath):
    command = ["python3", filepath]
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    return process

def monitor_memory_usage(process, interval=0.1):
    try:
        mem_usage = memory_usage(proc=process.pid, interval=interval, timeout=None)
        return max(mem_usage) 
    except Exception as e:
        print(f"Memory monitoring error: {e}")
        return 0.0

def process_directory(directory, output_file):
    max_memory_usages = {}
    file_list = [f for f in os.listdir(directory) if f.endswith('.py')]
    file_list.sort()
    
    for filename in file_list:
        filepath = os.path.join(directory, filename)
        try:
            process = run_python_script(filepath)
            max_usage = monitor_memory_usage(process)
            max_memory_usages[filename] = max_usage
            print(f"Processed {filename}: Max memory usage = {max_usage:.3f} MB")
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            max_memory_usages[filename] = 0.0
    
    # 保存结果
    with open(output_file, 'w') as file:
        for filename in file_list:
            max_usage = max_memory_usages.get(filename, 0.0)
            file.write(f"{filename}: {max_usage:.3f} MB\\n")
    
    return max_memory_usages

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python memory.py <directory> <output_file>")
        sys.exit(1)
    
    directory = sys.argv[1]
    output_file = sys.argv[2]
    
    results = process_directory(directory, output_file)
    print(f"Memory results saved to {output_file}")
'''

        # 修改后的cpu.py (修复了原始版本的问题)
        cpu_script = '''import os
import subprocess
import psutil
import sys
from memory_profiler import memory_usage
import threading
import time

def run_python_script(filepath):
    command = ["python3", filepath]
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    return process

def monitor_memory_usage(process, interval=0.01):
    try:
        mem_usage = memory_usage(proc=process.pid, interval=interval, timeout=None)
        max_mem = max(mem_usage)
        return max_mem
    except Exception as e:
        print(f"Memory monitoring error: {e}")
        return 0.0

def monitor_cpu_usage_fixed(process, interval=0.1):
    """修复后的CPU监控 - 并行监控而非串行"""
    cpu_usages = []
    
    try:
        ps_process = psutil.Process(process.pid)
        ps_process.cpu_percent()  # 初始化
        
        def cpu_monitor():
            nonlocal cpu_usages
            try:
                while process.poll() is None:
                    try:
                        cpu_usage = ps_process.cpu_percent(interval=interval)
                        if cpu_usage > 0:
                            cpu_usages.append(cpu_usage)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        break
            except:
                pass
        
        # 启动CPU监控线程
        cpu_thread = threading.Thread(target=cpu_monitor)
        cpu_thread.start()
        
        # 等待进程结束
        process.wait()
        cpu_thread.join(timeout=1)
        
    except Exception as e:
        print(f"CPU monitoring error: {e}")
    
    return cpu_usages

def process_directory(directory, output_file):
    results = {}
    file_list = [f for f in os.listdir(directory) if f.endswith('.py')]
    file_list.sort()
    
    for filename in file_list:
        filepath = os.path.join(directory, filename)
        print(f"Processing file: {filename}")
        
        try:
            # 分别运行内存和CPU监控
            # 内存监控
            process1 = run_python_script(filepath)
            max_memory_usage = monitor_memory_usage(process1)
            
            # CPU监控 (修复后的版本)
            process2 = run_python_script(filepath)
            cpu_usages = monitor_cpu_usage_fixed(process2)
            
            results[filename] = (max_memory_usage, cpu_usages)
            print(f"Processed {filename}: Max memory = {max_memory_usage:.3f} MB, CPU data points = {len(cpu_usages)}")
            
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            results[filename] = (0.0, [])
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as file:
        for filename in file_list:
            max_memory, cpu_usages = results.get(filename, (0.0, []))
            cpu_usage_str = ", ".join(f"{x:.1f}" for x in cpu_usages)
            file.write(f"{filename}: Max Memory = {max_memory:.3f} MB, CPU Usages = [{cpu_usage_str}]\\n")
    
    return results

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python cpu.py <directory> <output_file>")
        sys.exit(1)
    
    directory = sys.argv[1]
    output_file = sys.argv[2]
    
    results = process_directory(directory, output_file)
    print(f"CPU results saved to {output_file}")
'''

        # 保存修改后的脚本
        scripts = {
            'cprofile.py': cprofile_script,
            'memory.py': memory_script,
            'cpu.py': cpu_script
        }
        
        for script_name, script_content in scripts.items():
            script_path = os.path.join(self.scripts_dir, script_name)
            with open(script_path, 'w') as f:
                f.write(script_content)
            print(f"创建修改后的脚本: {script_name}")

    def _prepare_test_files(self, model: str, task_ids: List[str]) -> str:
        """预处理：为指定任务创建测试文件"""
        test_dir = os.path.join(self.temp_dir, f"test_files_{model}")
        os.makedirs(test_dir, exist_ok=True)
        
        file_count = 0
        for task_id in task_ids:
            # 获取生成的代码
            generated_code = self._get_generated_code(model, task_id)
            if generated_code:
                # 创建测试文件
                filename = f"task_{task_id.replace('/', '_')}.py"
                filepath = os.path.join(test_dir, filename)
                
                with open(filepath, 'w') as f:
                    f.write(generated_code)
                
                file_count += 1
        
        print(f"为模型 {model} 创建了 {file_count} 个测试文件")
        return test_dir
