#!/usr/bin/env python3
"""
CPU监控方法对比分析
"""

import subprocess
import psutil
import time
import threading
from typing import List

class CPUMonitoringComparison:
    """CPU监控方法对比"""
    
    def __init__(self):
        self.results = {}
    
    def current_method(self, script_path: str) -> List[float]:
        """当前的复杂预启动线程方法"""
        cpu_usages = []
        
        # 预启动CPU监控准备
        cpu_monitor_ready = threading.Event()
        cpu_monitor_started = threading.Event()
        
        def pre_monitor_cpu():
            nonlocal cpu_usages
            # 等待进程启动
            cpu_monitor_ready.wait()
            
            if ps_process is None:
                return
            
            # 标记监控已开始
            cpu_monitor_started.set()
            
            try:
                # 记录开始时间和初始CPU时间
                monitor_start_time = time.perf_counter()
                try:
                    initial_cpu_times = ps_process.cpu_times()
                    initial_cpu_time = initial_cpu_times.user + initial_cpu_times.system
                except:
                    initial_cpu_time = 0
                
                # 使用非常短的间隔来捕获短进程
                interval = 0.001  # 1ms间隔，极短
                
                # 立即开始监控，不等待
                while True:
                    try:
                        # 检查进程状态
                        if process.poll() is not None:  # 进程已结束
                            break
                        
                        # 监控CPU
                        cpu_usage = ps_process.cpu_percent(interval=interval)
                        cpu_usages.append(cpu_usage)
                        
                        # 检查进程状态
                        if not ps_process.is_running():
                            break
                            
                    except psutil.NoSuchProcess:
                        break
                    except Exception:
                        break
                
                # 改进的短进程CPU估算方法
                if not cpu_usages or all(cpu == 0.0 for cpu in cpu_usages):
                    try:
                        # 计算总执行时间
                        end_time = time.perf_counter()
                        wall_time = end_time - monitor_start_time
                        
                        # 获取最终CPU时间
                        final_cpu_times = ps_process.cpu_times()
                        final_cpu_time = final_cpu_times.user + final_cpu_times.system
                        
                        # 计算CPU使用率
                        cpu_time_used = final_cpu_time - initial_cpu_time
                        if wall_time > 0 and cpu_time_used > 0:
                            # CPU使用率 = CPU时间 / 墙钟时间 * 100%
                            estimated_cpu = min((cpu_time_used / wall_time) * 100, 800.0)
                            if estimated_cpu > 0.1:
                                cpu_usages.append(estimated_cpu)
                    except:
                        pass
                
                # 对于极短进程，使用进程总CPU时间作为估算
                if not cpu_usages:
                    try:
                        final_cpu_times = ps_process.cpu_times()
                        total_cpu_time = final_cpu_times.user + final_cpu_times.system
                        
                        if total_cpu_time > 0:
                            estimated_cpu = min(total_cpu_time * 1000, 100.0)
                            cpu_usages.append(estimated_cpu)
                    except:
                        pass
                        
            except Exception:
                pass
        
        # 预启动CPU监控线程
        cpu_thread = threading.Thread(target=pre_monitor_cpu)
        cpu_thread.start()
        
        # 启动进程
        process = subprocess.Popen(['python', script_path],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        # 立即尝试获取进程并初始化CPU监控
        ps_process = None
        try:
            ps_process = psutil.Process(process.pid)
            ps_process.cpu_percent()  # 立即初始化
            # 通知CPU监控线程可以开始
            cpu_monitor_ready.set()
            # 等待CPU监控真正开始
            cpu_monitor_started.wait(timeout=0.001)
        except:
            cpu_monitor_ready.set()
        
        # 等待进程结束
        process.wait()
        cpu_thread.join(timeout=1)
        
        return cpu_usages
    
    def simplified_method(self, script_path: str) -> List[float]:
        """简化的直接监控方法"""
        cpu_usages = []
        
        # 启动进程
        process = subprocess.Popen(['python', script_path],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        try:
            # 立即获取进程对象
            ps_process = psutil.Process(process.pid)
            
            # 记录开始时间和初始CPU状态
            start_time = time.perf_counter()
            initial_cpu_times = ps_process.cpu_times()
            initial_cpu_time = initial_cpu_times.user + initial_cpu_times.system
            
            # 初始化CPU监控
            ps_process.cpu_percent()
            
            # 直接在主线程中监控
            while process.poll() is None:
                try:
                    cpu_usage = ps_process.cpu_percent(interval=0.001)
                    if cpu_usage > 0:  # 只记录非零值
                        cpu_usages.append(cpu_usage)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    break
            
            # 如果没有捕获到实时数据，使用CPU时间估算
            if not cpu_usages:
                end_time = time.perf_counter()
                wall_time = end_time - start_time
                
                final_cpu_times = ps_process.cpu_times()
                final_cpu_time = final_cpu_times.user + final_cpu_times.system
                cpu_time_used = final_cpu_time - initial_cpu_time
                
                if wall_time > 0 and cpu_time_used > 0:
                    estimated_cpu = (cpu_time_used / wall_time) * 100
                    cpu_usages.append(min(estimated_cpu, 100.0))
        
        except Exception as e:
            print(f"CPU监控失败: {e}")
        
        # 等待进程结束
        process.wait()
        
        return cpu_usages
    
    def resource_based_method(self, script_path: str) -> List[float]:
        """基于资源使用的监控方法"""
        cpu_usages = []
        
        # 启动进程
        process = subprocess.Popen(['python', script_path],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        try:
            ps_process = psutil.Process(process.pid)
            
            # 使用资源使用情况监控
            start_time = time.perf_counter()
            initial_cpu_times = ps_process.cpu_times()
            
            # 等待进程结束
            process.wait()
            
            end_time = time.perf_counter()
            final_cpu_times = ps_process.cpu_times()
            
            # 计算总体CPU使用率
            wall_time = end_time - start_time
            cpu_time_used = (final_cpu_times.user + final_cpu_times.system) - \
                           (initial_cpu_times.user + initial_cpu_times.system)
            
            if wall_time > 0 and cpu_time_used > 0:
                avg_cpu_usage = (cpu_time_used / wall_time) * 100
                cpu_usages.append(min(avg_cpu_usage, 100.0))
        
        except Exception as e:
            print(f"资源监控失败: {e}")
        
        return cpu_usages
    
    def compare_methods(self, script_path: str):
        """对比不同的CPU监控方法"""
        print(f"=== CPU监控方法对比: {script_path} ===")
        
        methods = {
            "当前复杂方法": self.current_method,
            "简化直接方法": self.simplified_method,
            "资源基础方法": self.resource_based_method
        }
        
        for method_name, method_func in methods.items():
            print(f"\n{method_name}:")
            try:
                start = time.perf_counter()
                cpu_data = method_func(script_path)
                duration = time.perf_counter() - start
                
                print(f"  监控耗时: {duration:.4f}s")
                print(f"  CPU数据点: {len(cpu_data)}")
                if cpu_data:
                    non_zero = [x for x in cpu_data if x > 0]
                    print(f"  非零数据点: {len(non_zero)}")
                    if non_zero:
                        print(f"  CPU范围: {min(non_zero):.1f}% - {max(non_zero):.1f}%")
                        print(f"  平均CPU: {sum(non_zero)/len(non_zero):.1f}%")
                    print(f"  数据样例: {cpu_data[:5]}")
                else:
                    print("  无CPU数据")
                    
            except Exception as e:
                print(f"  方法失败: {e}")

if __name__ == "__main__":
    # 创建测试脚本
    test_script = """
import time
total = 0
for i in range(100000):
    total += i * i
print(f"Result: {total}")
"""
    
    with open("test_cpu_script.py", "w") as f:
        f.write(test_script)
    
    # 对比不同方法
    comparator = CPUMonitoringComparison()
    comparator.compare_methods("test_cpu_script.py")
