#!/usr/bin/env python3
"""
测试统计分析功能
"""

import numpy as np
from scipy import stats
from evalperf_integrated_tester import EvalPerfIntegratedTester


def test_statistical_methods():
    """测试统计方法"""
    print("=== Testing Statistical Methods ===")
    
    # 创建测试器实例
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path="/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl",
        original_humaneval_path="./json-prompt/human-eval-v2-20210705.jsonl",
        original_mbpp_path="./json-prompt/mbpp.jsonl"
    )
    
    # 创建模拟数据
    np.random.seed(42)  # 为了可重复性
    
    # 模拟生成代码的性能数据（稍微慢一些）
    generated_times = np.random.normal(0.025, 0.005, 10)  # 平均25ms，标准差5ms
    
    # 模拟canonical solution的性能数据（稍微快一些）
    canonical_times = np.random.normal(0.020, 0.003, 10)  # 平均20ms，标准差3ms
    
    print(f"Generated code times: {generated_times}")
    print(f"Canonical solution times: {canonical_times}")
    
    # 运行统计测试
    result = tester._calculate_statistical_tests(
        generated_times.tolist(),
        canonical_times.tolist(),
        'execution_time'
    )
    
    print(f"\n=== Statistical Test Results ===")
    print(f"Metric: {result['metric']}")
    
    # 描述性统计
    print(f"\nDescriptive Statistics:")
    gen_stats = result['descriptive_stats']['generated']
    canon_stats = result['descriptive_stats']['canonical']
    
    print(f"Generated code:")
    print(f"  Mean: {gen_stats['mean']:.6f}s")
    print(f"  Std: {gen_stats['std']:.6f}s")
    print(f"  95% CI: [{gen_stats['confidence_interval_95'][0]:.6f}, {gen_stats['confidence_interval_95'][1]:.6f}]")
    
    print(f"Canonical solution:")
    print(f"  Mean: {canon_stats['mean']:.6f}s")
    print(f"  Std: {canon_stats['std']:.6f}s")
    print(f"  95% CI: [{canon_stats['confidence_interval_95'][0]:.6f}, {canon_stats['confidence_interval_95'][1]:.6f}]")
    
    # 统计测试
    print(f"\nStatistical Tests:")
    tests = result['statistical_tests']
    
    if tests['paired_t_test']:
        paired = tests['paired_t_test']
        print(f"Paired t-test:")
        print(f"  t-statistic: {paired['t_statistic']:.3f}")
        print(f"  p-value: {paired['p_value']:.6f}")
        print(f"  Significant: {paired['significant']}")
    
    if tests['independent_t_test']:
        independent = tests['independent_t_test']
        print(f"Independent t-test:")
        print(f"  t-statistic: {independent['t_statistic']:.3f}")
        print(f"  p-value: {independent['p_value']:.6f}")
        print(f"  Significant: {independent['significant']}")
    
    if tests['wilcoxon_signed_rank']:
        wilcoxon = tests['wilcoxon_signed_rank']
        print(f"Wilcoxon signed-rank test:")
        print(f"  W-statistic: {wilcoxon['w_statistic']:.3f}")
        print(f"  p-value: {wilcoxon['p_value']:.6f}")
        print(f"  Significant: {wilcoxon['significant']}")
    
    if tests['mann_whitney_u']:
        mannwhitney = tests['mann_whitney_u']
        print(f"Mann-Whitney U test:")
        print(f"  U-statistic: {mannwhitney['u_statistic']:.3f}")
        print(f"  p-value: {mannwhitney['p_value']:.6f}")
        print(f"  Significant: {mannwhitney['significant']}")
    
    # 效应量
    print(f"\nEffect Size:")
    effect = result['effect_size']
    if 'cohens_d' in effect:
        print(f"  Cohen's d: {effect['cohens_d']:.3f}")
        print(f"  Interpretation: {effect['interpretation']}")
        print(f"  Direction: {effect['direction']}")
    
    # 性能比率
    print(f"\nPerformance Ratio:")
    ratio = result['performance_ratio']
    print(f"  Mean ratio: {ratio['mean_ratio']:.3f}")
    print(f"  Better performance: {ratio['better_performance']}")
    
    return result


def main():
    """主函数"""
    print("Statistical Analysis Test")
    print("=" * 50)
    
    try:
        result = test_statistical_methods()
        print("\n✅ Statistical analysis test completed successfully!")
        
        # 检查是否检测到显著差异
        tests = result['statistical_tests']
        significant_tests = []
        
        for test_name, test_result in tests.items():
            if test_result and 'significant' in test_result and test_result['significant']:
                significant_tests.append(test_name)
        
        if significant_tests:
            print(f"🎯 Detected significant differences in: {', '.join(significant_tests)}")
        else:
            print("📊 No significant differences detected (this is expected with small effect sizes)")
            
    except Exception as e:
        print(f"❌ Statistical analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    main()
