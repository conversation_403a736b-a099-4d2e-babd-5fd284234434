#!/usr/bin/env python3
"""
测试EvalPerf性能测试框架
"""

import json
import os
import sys
from pathlib import Path
from evalperf_config import EvalPerfConfig


def test_data_loading():
    """测试数据加载功能"""
    print("=== Testing Data Loading ===")

    config = EvalPerfConfig()

    # 测试EvalPerf数据加载
    try:
        evalperf_count = 0
        with open(config.evalperf_dataset_path, 'r') as f:
            for line in f:
                data = json.loads(line.strip())
                evalperf_count += 1
                if evalperf_count <= 3:  # 显示前3个样本
                    print(f"EvalPerf sample {evalperf_count}: {data['task_id']} - {data['entry_point']}")
                if evalperf_count >= 10:  # 只测试前10个
                    break
        print(f"✅ EvalPerf data loading successful (tested {evalperf_count} samples)")
    except Exception as e:
        print(f"❌ EvalPerf data loading failed: {e}")
        return False

    # 测试原始数据加载
    try:
        humaneval_count = 0
        with open(config.original_humaneval_path, 'r') as f:
            for line in f:
                data = json.loads(line.strip())
                humaneval_count += 1
                if humaneval_count >= 5:  # 只测试前5个
                    break
        print(f"✅ HumanEval data loading successful (tested {humaneval_count} samples)")
    except Exception as e:
        print(f"❌ HumanEval data loading failed: {e}")
        return False

    return True


def test_code_path_resolution():
    """测试代码路径解析功能"""
    print("\n=== Testing Code Path Resolution ===")

    config = EvalPerfConfig()

    # 测试样本任务
    test_tasks = [
        'HumanEval/0',
        'HumanEval/1',
        'HumanEval/9',
        'Mbpp/1',
        'Mbpp/2',
        'Mbpp/3'
    ]

    for model in config.available_models:
        print(f"\nTesting {model}:")
        found_count = 0

        for task_id in test_tasks:
            dataset = 'humaneval' if task_id.startswith('HumanEval/') else 'mbpp'
            path = config.get_generated_code_path(model, dataset, task_id)

            if path:
                print(f"  ✅ {task_id}: {path}")
                found_count += 1
            else:
                print(f"  ❌ {task_id}: Not found")

        print(f"  Summary: {found_count}/{len(test_tasks)} tasks found")

    return True


def test_evalperf_integration():
    """测试EvalPerf集成功能"""
    print("\n=== Testing EvalPerf Integration ===")

    config = EvalPerfConfig()

    try:
        # 加载一个EvalPerf样本
        with open(config.evalperf_dataset_path, 'r') as f:
            line = f.readline()
            sample = json.loads(line.strip())

        task_id = sample['task_id']
        entry_point = sample['entry_point']
        pe_input = sample['pe_input']
        references = sample['reference']
        scores = sample['scores']

        print(f"Sample task: {task_id}")
        print(f"Entry point: {entry_point}")
        print(f"PE input type: {type(pe_input)}")
        print(f"References count: {len(references)}")
        print(f"Scores: {scores[:3]}..." if len(scores) > 3 else f"Scores: {scores}")

        # 找到最佳reference
        best_idx = scores.index(max(scores))
        best_score = scores[best_idx]
        best_reference = references[best_idx]

        print(f"Best reference score: {best_score}")
        print(f"Best reference preview: {best_reference[:100]}...")

        print("✅ EvalPerf integration test successful")
        return True

    except Exception as e:
        print(f"❌ EvalPerf integration test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("EvalPerf Framework Testing")
    print("=" * 50)

    # 首先验证配置
    config = EvalPerfConfig()
    if not config.validate_paths():
        print("❌ Configuration validation failed. Please check your paths.")
        return False

    # 运行各项测试
    tests = [
        ("Data Loading", test_data_loading),
        ("Code Path Resolution", test_code_path_resolution),
        ("EvalPerf Integration", test_evalperf_integration),
    ]

    passed_tests = 0
    total_tests = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")

    print(f"\n{'='*50}")
    print(f"Test Results: {passed_tests}/{total_tests} tests passed")

    if passed_tests == total_tests:
        print("🎉 All tests passed! The framework is ready to use.")
        print("\nNext steps:")
        print("1. Run: python evalperf_integrated_tester.py")
        print("2. Check results in ./evalperf_results/")
        return True
    else:
        print("⚠️  Some tests failed. Please check the configuration and paths.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
