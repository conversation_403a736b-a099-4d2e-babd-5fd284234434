#!/usr/bin/env python3
"""
调试预处理功能
"""

import json
import tempfile
import subprocess
import sys
import os
from evalperf_integrated_tester import EvalPerfIntegratedTester


def debug_preprocessing():
    """调试预处理功能"""
    print("=== Debugging Preprocessing ===")
    
    # 创建测试器
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path="/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl",
        original_humaneval_path="./json-prompt/human-eval-v2-20210705.jsonl",
        original_mbpp_path="./json-prompt/mbpp.jsonl"
    )
    
    # 测试HumanEval/24
    task_id = 'HumanEval/24'
    
    # 获取任务信息
    evalperf_task = tester.evalperf_data[task_id]
    entry_point = evalperf_task['entry_point']
    pe_input = evalperf_task['pe_input']
    
    print(f"Task: {task_id}")
    print(f"Entry point: {entry_point}")
    
    # 加载生成的代码
    generated_code = tester._load_generated_code('copilot', task_id)
    if not generated_code:
        print("❌ Generated code not found")
        return
    
    # 提取函数
    function_code = tester._extract_function_from_code(generated_code, entry_point)
    if not function_code:
        print("❌ Function not found")
        return
    
    print(f"✅ Function extracted:")
    print(function_code[:200] + "..." if len(function_code) > 200 else function_code)
    
    # 解析pe_input
    try:
        pe_input_data = json.loads(pe_input) if isinstance(pe_input, str) else pe_input
        print(f"✅ PE input parsed: {type(pe_input_data)}")
        if isinstance(pe_input_data, list):
            print(f"PE input length: {len(pe_input_data)}")
            if len(pe_input_data) > 0:
                print(f"First element: {pe_input_data[0]}")
    except Exception as e:
        print(f"❌ PE input parsing failed: {e}")
        return
    
    # 创建预处理后的代码
    test_code = f"""
from memory_profiler import profile
from typing import List, Dict, Any, Optional, Tuple
import json

@profile
def encapsulated_function():
    # 原始函数定义
{tester._indent_code(function_code, 4)}
    
    # 解析输入数据
    pe_input_data = {repr(pe_input_data)}
    
    # 根据pe_input_data的结构调用函数
    if isinstance(pe_input_data, list):
        # 对于EvalPerf，通常是三层嵌套的列表
        # 最外层是测试用例列表，每个测试用例包含函数的参数
        for test_case in pe_input_data:
            if isinstance(test_case, list):
                # test_case是参数列表，使用*解包传递给函数
                {entry_point}(*test_case)
            else:
                # 如果test_case不是列表，直接传递
                {entry_point}(test_case)
    else:
        # 单个参数
        {entry_point}(pe_input_data)

if __name__ == "__main__":
    encapsulated_function()
"""
    
    # 保存到临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        temp_file = f.name
    
    print(f"\n=== Generated Test Code ===")
    print(f"Temp file: {temp_file}")
    print("First 50 lines:")
    lines = test_code.split('\n')
    for i, line in enumerate(lines[:50], 1):
        print(f"{i:2d}: {line}")
    
    # 尝试运行代码
    print(f"\n=== Testing Execution ===")
    try:
        result = subprocess.run([sys.executable, temp_file], 
                              capture_output=True, text=True, timeout=10)
        
        print(f"Return code: {result.returncode}")
        print(f"STDOUT:")
        print(result.stdout)
        if result.stderr:
            print(f"STDERR:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("❌ Execution timed out")
    except Exception as e:
        print(f"❌ Execution failed: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)


def main():
    """主函数"""
    debug_preprocessing()


if __name__ == "__main__":
    main()
