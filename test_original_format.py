#!/usr/bin/env python3
"""
测试原始格式的cProfile
"""

import cProfile
import pstats
import tempfile


def test_original_format():
    """测试原始格式"""
    
    # 原始格式的代码（带@profile装饰器和封装函数）
    original_format_code = """
from memory_profiler import profile

@profile
def encapsulated_function():
    def largest_divisor(n: int) -> int:
        for i in reversed(range(n)):
            if n % i == 0:
                return i
    
    # 运行测试
    largest_divisor(1000)

if __name__ == "__main__":
    encapsulated_function()
"""
    
    # 我们当前的格式
    our_format_code = """
from typing import List, Dict, Any, Optional, Tuple
import json

def largest_divisor(n: int) -> int:
    for i in reversed(range(n)):
        if n % i == 0:
            return i

def main():
    pe_input_data = [[1000]]
    
    if isinstance(pe_input_data, list):
        for test_case in pe_input_data:
            if isinstance(test_case, list):
                largest_divisor(*test_case)
            else:
                largest_divisor(test_case)
    else:
        largest_divisor(pe_input_data)

if __name__ == "__main__":
    main()
"""
    
    formats = [
        ("原始格式", original_format_code),
        ("我们的格式", our_format_code)
    ]
    
    for name, code in formats:
        print(f"\n=== {name} ===")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code)
            temp_file = f.name
        
        profile_file = tempfile.mktemp(suffix='.prof')
        
        try:
            # 使用原始方法
            with open(temp_file, "rb") as file:
                compiled_code = compile(file.read(), temp_file, 'exec')
                cProfile.run(compiled_code, profile_file)
            
            # 分析结果
            p = pstats.Stats(profile_file)
            if p.stats:
                print("前5个函数:")
                for i, (func, stats) in enumerate(list(p.stats.items())[:5]):
                    print(f"  {i+1}. {func}: cumtime={stats[3]:.8f}s")
                
                # 原始方法取的值
                first_cumtime = p.stats[list(p.stats.keys())[0]][3]
                print(f"原始方法取的值 (第一个函数): {first_cumtime:.8f}s")
            
        except Exception as e:
            print(f"执行失败: {e}")
        
        # 清理
        import os
        if os.path.exists(temp_file):
            os.unlink(temp_file)
        if os.path.exists(profile_file):
            os.unlink(profile_file)


if __name__ == "__main__":
    test_original_format()
