import os

folder_path = "./output/Humaneval-prompt/HumanEval-prompt1"

file_list = os.listdir(folder_path)

for file_name in file_list:
    file_path = os.path.join(folder_path, file_name)

    with open(file_path, 'r') as file:
        code = file.read()

    modified_code = '"""Complete the function with better-performing Python code. Use efficient function calls and looping structures, use efficient algorithms, avoid unnecessary complexity and waste of resources, ensure that the code is concise, and make full use of language features."""\n' + code

    with open(file_path, 'w') as file:
        file.write(modified_code)

    print(f'Processed file: {file_name}')