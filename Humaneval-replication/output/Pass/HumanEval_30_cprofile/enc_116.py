from memory_profiler import profile
@profile
def encapsulated_function():
    def sort_array(arr):
        """
        In this Kata, you have to sort an array of non-negative integers according to
        number of ones in their binary representation in ascending order.
        For similar number of ones, sort based on decimal value.
    
        It must be implemented like this:
        >>> sort_array([1, 5, 2, 3, 4]) == [1, 2, 3, 4, 5]
        >>> sort_array([-2, -3, -4, -5, -6]) == [-6, -5, -4, -3, -2]
        >>> sort_array([1, 0, 2, 3, 4]) [0, 1, 2, 3, 4]
        """
        return sorted(sorted(arr), key=lambda x: bin(x)[2:].count('1'))
    
    def check(candidate):
        for item in range(30):
            assert True, 'This prints if this assert fails 1 (good for debugging!)'
            assert candidate([1, 5, 2, 3, 4]) == [1, 2, 4, 3, 5]
            assert candidate([-2, -3, -4, -5, -6]) == [-4, -2, -6, -5, -3]
            assert candidate([1, 0, 2, 3, 4]) == [0, 1, 2, 4, 3]
            assert candidate([]) == []
            assert candidate([2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4]) == [2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77]
            assert candidate([3, 6, 44, 12, 32, 5]) == [32, 3, 5, 6, 12, 44]
            assert candidate([2, 4, 8, 16, 32]) == [2, 4, 8, 16, 32]
            assert candidate([2, 4, 8, 16, 32]) == [2, 4, 8, 16, 32]
            assert True, 'This prints if this assert fails 2 (also good for debugging!)'
    "\nStandard answer: \n    return sorted(sorted(arr), key=lambda x: bin(x)[2:].count('1'))\n"
    check(sort_array)
