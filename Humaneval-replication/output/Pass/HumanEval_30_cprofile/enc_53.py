from memory_profiler import profile
@profile
def encapsulated_function():
    def add(x: int, y: int):
        """Add two numbers x and y
        >>> add(2, 3)
        5
        >>> add(5, 7)
        12
        """
        return x + y
    METADATA = {}
    
    def check(candidate):
        for item in range(30):
            import random
            assert candidate(0, 1) == 1
            assert candidate(1, 0) == 1
            assert candidate(2, 3) == 5
            assert candidate(5, 7) == 12
            assert candidate(7, 5) == 12
            for i in range(100):
                (x, y) = (random.randint(0, 1000), random.randint(0, 1000))
                assert candidate(x, y) == x + y
    '\nStandard answer: \n    return x + y\n'
    check(add)
