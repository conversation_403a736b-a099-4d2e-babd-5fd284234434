from memory_profiler import profile
@profile
def encapsulated_function():
    def incr_list(l: list):
        """Return list with elements incremented by 1.
        >>> incr_list([1, 2, 3])
        [2, 3, 4]
        >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])
        [6, 4, 6, 3, 4, 4, 10, 1, 124]
        """
        return [e + 1 for e in l]
    METADATA = {}
    
    def check(candidate):
        for item in range(30):
            assert candidate([]) == []
            assert candidate([3, 2, 1]) == [4, 3, 2]
            assert candidate([5, 2, 5, 2, 3, 3, 9, 0, 123]) == [6, 3, 6, 3, 4, 4, 10, 1, 124]
    '\nStandard answer: \n    return [(e + 1) for e in l]\n'
    check(incr_list)
