from memory_profiler import profile
@profile
def encapsulated_function():
    def string_sequence(n: int) -> str:
        """ Return a string containing space-delimited numbers starting from 0 upto n inclusive.
        >>> string_sequence(0)
        '0'
        >>> string_sequence(5)
        '0 1 2 3 4 5'
        """
        result = ''
        for i in range(n + 1):
            result += str(i) + ' '
        return result[:-1]
    METADATA = {'author': 'jt', 'dataset': 'test'}
    
    def check(candidate):
        for item in range(30):
            assert candidate(0) == '0'
            assert candidate(3) == '0 1 2 3'
            assert candidate(10) == '0 1 2 3 4 5 6 7 8 9 10'
    "\nStandard answer: \n    return ' '.join([str(x) for x in range(n + 1)])\n"
    check(string_sequence)
