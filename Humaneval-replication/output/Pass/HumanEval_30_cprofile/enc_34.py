from memory_profiler import profile
@profile
def encapsulated_function():
    def unique(l: list):
        """Return sorted unique elements in a list
        >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])
        [0, 2, 3, 5, 9, 123]
        """
        return sorted(list(set(l)))
    METADATA = {}
    
    def check(candidate):
        for item in range(30):
            assert candidate([5, 3, 5, 2, 3, 3, 9, 0, 123]) == [0, 2, 3, 5, 9, 123]
    '\nStandard answer: \n    return sorted(list(set(l)))\n'
    check(unique)
