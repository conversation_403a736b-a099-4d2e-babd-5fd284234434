from memory_profiler import profile
@profile
def encapsulated_function():
    def unique_digits(x):
        """Given a list of positive integers x. return a sorted list of all 
        elements that hasn't any even digit.
    
        Note: Returned list should be sorted in increasing order.
        
        For example:
        >>> unique_digits([15, 33, 1422, 1])
        [1, 15, 33]
        >>> unique_digits([152, 323, 1422, 10])
        []
        """
        odd_digit_elements = []
        for i in x:
            if all((int(c) % 2 == 1 for c in str(i))):
                odd_digit_elements.append(i)
        return sorted(odd_digit_elements)
    
    def check(candidate):
        for item in range(30):
            assert candidate([15, 33, 1422, 1]) == [1, 15, 33]
            assert candidate([152, 323, 1422, 10]) == []
            assert candidate([12345, 2033, 111, 151]) == [111, 151]
            assert candidate([135, 103, 31]) == [31, 135]
            assert True
    '\nStandard answer: \n    odd_digit_elements = []\n    for i in x:\n        if all (int(c) % 2 == 1 for c in str(i)):\n            odd_digit_elements.append(i)\n    return sorted(odd_digit_elements)\n'
    check(unique_digits)
