def solve(s):
    """You are given a string s.
    if s[i] is a letter, reverse its case from lower to upper or vise versa, 
    otherwise keep it as it is.
    If the string contains no letters, reverse the string.
    The function should return the resulted string.
    Examples
    solve("1234") = "4321"
    solve("ab") = "AB"
    solve("#a@C") = "#A@c"
    """
    flg = 0
    idx = 0
    new_str = list(s)
    for i in s:
        if i.isalpha():
            new_str[idx] = i.swapcase()
            flg = 1
        idx += 1
    s = ''
    for i in new_str:
        s += i
    if flg == 0:
        return s[len(s)::-1]
    return s

def check(candidate):
    for item in range(30):
        assert candidate('AsDf') == 'aSdF'
        assert candidate('1234') == '4321'
        assert candidate('ab') == 'AB'
        assert candidate('#a@C') == '#A@c'
        assert candidate('#AsdfW^45') == '#aSDFw^45'
        assert candidate('#6@2') == '2@6#'
        assert candidate('#$a^D') == '#$A^d'
        assert candidate('#ccc') == '#CCC'
'\nStandard answer: \n    flg = 0\n    idx = 0\n    new_str = list(s)\n    for i in s:\n        if i.isalpha():\n            new_str[idx] = i.swapcase()\n            flg = 1\n        idx += 1\n    s = ""\n    for i in new_str:\n        s += i\n    if flg == 0:\n        return s[len(s)::-1]\n    return s\n'
check(solve)