def check_dict_case(dict):
    """
    Given a dictionary, return True if all keys are strings in lower 
    case or all keys are strings in upper case, else return False.
    The function should return False is the given dictionary is empty.
    Examples:
    check_dict_case({"a":"apple", "b":"banana"}) should return True.
    check_dict_case({"a":"apple", "A":"banana", "B":"banana"}) should return False.
    check_dict_case({"a":"apple", 8:"banana", "a":"apple"}) should return False.
    check_dict_case({"Name":"John", "Age":"36", "City":"Houston"}) should return False.
    check_dict_case({"STATE":"NC", "ZIP":"12345" }) should return True.
    """
    if len(dict.keys()) == 0:
        return False
    else:
        state = 'start'
        for key in dict.keys():
            if isinstance(key, str) == False:
                state = 'mixed'
                break
            if state == 'start':
                if key.isupper():
                    state = 'upper'
                elif key.islower():
                    state = 'lower'
                else:
                    break
            elif state == 'upper' and (not key.isupper()) or (state == 'lower' and (not key.islower())):
                state = 'mixed'
                break
            else:
                break
        return state == 'upper' or state == 'lower'

def check(candidate):
    for item in range(30):
        assert candidate({'p': 'pineapple', 'b': 'banana'}) == True, 'First test error: ' + str(candidate({'p': 'pineapple', 'b': 'banana'}))
        assert candidate({'p': 'pineapple', 'A': 'banana', 'B': 'banana'}) == False, 'Second test error: ' + str(candidate({'p': 'pineapple', 'A': 'banana', 'B': 'banana'}))
        assert candidate({'p': 'pineapple', 5: 'banana', 'a': 'apple'}) == False, 'Third test error: ' + str(candidate({'p': 'pineapple', 5: 'banana', 'a': 'apple'}))
        assert candidate({'Name': 'John', 'Age': '36', 'City': 'Houston'}) == False, 'Fourth test error: ' + str(candidate({'Name': 'John', 'Age': '36', 'City': 'Houston'}))
        assert candidate({'STATE': 'NC', 'ZIP': '12345'}) == True, 'Fifth test error: ' + str(candidate({'STATE': 'NC', 'ZIP': '12345'}))
        assert candidate({'fruit': 'Orange', 'taste': 'Sweet'}) == True, 'Fourth test error: ' + str(candidate({'fruit': 'Orange', 'taste': 'Sweet'}))
        assert candidate({}) == False, '1st edge test error: ' + str(candidate({}))
'\nStandard answer: \n    if len(dict.keys()) == 0:\n        return False\n    else:\n        state = "start"\n        for key in dict.keys():\n\n            if isinstance(key, str) == False:\n                state = "mixed"\n                break\n            if state == "start":\n                if key.isupper():\n                    state = "upper"\n                elif key.islower():\n                    state = "lower"\n                else:\n                    break\n            elif (state == "upper" and not key.isupper()) or (state == "lower" and not key.islower()):\n                    state = "mixed"\n                    break\n            else:\n                break\n        return state == "upper" or state == "lower" \n'
check(check_dict_case)