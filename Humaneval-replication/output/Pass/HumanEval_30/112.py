def reverse_delete(s, c):
    """Task
    We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c
    then check if the result string is palindrome.
    A string is called palindrome if it reads the same backward as forward.
    You should return a tuple containing the result string and True/False for the check.
    Example
    For s = "abcde", c = "ae", the result should be ('bcd',False)
    For s = "abcdef", c = "b"  the result should be ('acdef',False)
    For s = "abcdedcba", c = "ab", the result should be ('cdedc',True)
    """
    for i in c:
        s = s.replace(i, '')
    return (s, s[::-1] == s)

def check(candidate):
    for item in range(30):
        assert candidate('abcde', 'ae') == ('bcd', False)
        assert candidate('abcdef', 'b') == ('acdef', False)
        assert candidate('abcdedcba', 'ab') == ('cdedc', True)
        assert candidate('dwik', 'w') == ('dik', False)
        assert candidate('a', 'a') == ('', True)
        assert candidate('abcdedcba', '') == ('abcdedcba', True)
        assert candidate('abcdedcba', 'v') == ('abcdedcba', True)
        assert candidate('vabba', 'v') == ('abba', True)
        assert candidate('mamma', 'mia') == ('', True)
"\nStandard answer: \n    s = ''.join([char for char in s if char not in c])\n    return (s,s[::-1] == s)\n"
check(reverse_delete)