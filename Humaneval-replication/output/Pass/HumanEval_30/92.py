def any_int(x, y, z):
    """
    Create a function that takes 3 numbers.
    Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.
    Returns false in any other cases.
    
    Examples
    any_int(5, 2, 7) ➞ True
    
    any_int(3, 2, 2) ➞ False

    any_int(3, -2, 1) ➞ True
    
    any_int(3.6, -2.2, 2) ➞ False
  

    
    """
    if isinstance(x, int) and isinstance(y, int) and isinstance(z, int):
        if x + y == z or x + z == y or y + z == x:
            return True
        else:
            return False
    else:
        return False

def check(candidate):
    for item in range(30):
        assert candidate(2, 3, 1) == True, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(2.5, 2, 3) == False, 'This prints if this assert fails 2 (good for debugging!)'
        assert candidate(1.5, 5, 3.5) == False, 'This prints if this assert fails 3 (good for debugging!)'
        assert candidate(2, 6, 2) == False, 'This prints if this assert fails 4 (good for debugging!)'
        assert candidate(4, 2, 2) == True, 'This prints if this assert fails 5 (good for debugging!)'
        assert candidate(2.2, 2.2, 2.2) == False, 'This prints if this assert fails 6 (good for debugging!)'
        assert candidate(-4, 6, 2) == True, 'This prints if this assert fails 7 (good for debugging!)'
        assert candidate(2, 1, 1) == True, 'This prints if this assert fails 8 (also good for debugging!)'
        assert candidate(3, 4, 7) == True, 'This prints if this assert fails 9 (also good for debugging!)'
        assert candidate(3.0, 4, 7) == False, 'This prints if this assert fails 10 (also good for debugging!)'
'\nStandard answer: \n    \n    if isinstance(x,int) and isinstance(y,int) and isinstance(z,int):\n        if (x+y==z) or (x+z==y) or (y+z==x):\n            return True\n        return False\n    return False\n'
check(any_int)