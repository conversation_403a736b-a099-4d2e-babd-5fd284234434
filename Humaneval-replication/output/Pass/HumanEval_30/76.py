def is_simple_power(x, n):
    """Your task is to write a function that returns true if a number x is a simple
    power of n and false in other cases.
    x is a simple power of n if n**int=x
    For example:
    is_simple_power(1, 4) => true
    is_simple_power(2, 2) => true
    is_simple_power(8, 2) => true
    is_simple_power(3, 2) => false
    is_simple_power(3, 1) => false
    is_simple_power(5, 3) => false
    """
    if x == 1:
        return True
    if x < n:
        return False
    if x % n != 0:
        return False
    return is_simple_power(x // n, n)

def check(candidate):
    for item in range(30):
        assert candidate(16, 2) == True, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(143214, 16) == False, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(4, 2) == True, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(9, 3) == True, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(16, 4) == True, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(24, 2) == False, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(128, 4) == False, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(12, 6) == False, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate(1, 1) == True, 'This prints if this assert fails 2 (also good for debugging!)'
        assert candidate(1, 12) == True, 'This prints if this assert fails 2 (also good for debugging!)'
'\nStandard answer: \n    if (n == 1): \n        return (x == 1) \n    power = 1\n    while (power < x): \n        power = power * n \n    return (power == x) \n'
check(is_simple_power)