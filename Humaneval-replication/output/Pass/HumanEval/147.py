
def get_max_triples(n):
    """
    You are given a positive integer n. You have to create an integer array a of length n.
        For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.
        Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, 
    and a[i] + a[j] + a[k] is a multiple of 3.

    Example :
        Input: n = 5
        Output: 1
        Explanation: 
        a = [1, 3, 7, 13, 21]
        The only valid triple is (1, 7, 13).
    """
    # Your code here
    # create a list to store the values of a
    a = []
    # use a for loop to iterate through the numbers from 1 to n
    for i in range(1, n+1):
        # append i*i-i+1 to the list
        a.append(i*i-i+1)
    # create a variable to store the number of triples
    count = 0
    # use a for loop to iterate through the list
    for i in range(len(a)):
        # use a for loop to iterate through the list
        for j in range(i+1, len(a)):
            # use a for loop to iterate through the list
            for k in range(j+1, len(a)):
                # if the sum of the elements is divisible by 3
                if (a[i]+a[j]+a[k])%3 == 0:
                    # increase the count by 1
                    count += 1
    # return the count
    return count


def check(candidate):

    assert candidate(5) == 1
    assert candidate(6) == 4
    assert candidate(10) == 36
    assert candidate(100) == 53361
'''
Standard answer: 
    A = [i*i - i + 1 for i in range(1,n+1)]
    ans = []
    for i in range(n):
        for j in range(i+1,n):
            for k in range(j+1,n):
                if (A[i]+A[j]+A[k])%3 == 0:
                    ans += [(A[i],A[j],A[k])]
    return len(ans)
'''
check(get_max_triples)
