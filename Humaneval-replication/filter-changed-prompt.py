import os
import shutil

folder1 = './output/Humaneval-prompt/HumanEval-prompt2'
folder2 = './output/Pass/HumanEval'
new_folder = './output/Humaneval-prompt/pass/HumanEval-prompt2'

os.makedirs(new_folder, exist_ok=True)

files_in_folder1 = set(os.listdir(folder1))
files_in_folder2 = set(os.listdir(folder2))

common_files = files_in_folder1.intersection(files_in_folder2)

for file_name in common_files:
    src_path = os.path.join(folder1, file_name)
    dest_path = os.path.join(new_folder, file_name)
    shutil.copy(src_path, dest_path)
    print(f'Copied {file_name} to {new_folder}')

print("Completed copying files.")
