import matplotlib.pyplot as plt
import re

def count_differences(array1, array2):
    indices = []
    for i, (x, y) in enumerate(zip(array1, array2)):
        if (y - x) / x > 0.2:
        # if abs(x - y) / max(x, y) > 0.2:
            indices.append(i)
    return indices, len(indices)

def read_data(file_path):
    data = {}
    with open(file_path, 'r') as file:
        for line in file:
            index, value = line.strip().split(':')
            data[int(index)] = float(value)
    return data

time_data1 = read_data('./result/Execution-Time/numbered_answers_cprofile_prompt1.txt')
time_data2 = read_data('./result/Execution-Time/numbered_cprofile_prompt1.txt')

common_indices = set(time_data1.keys()) & set(time_data2.keys())
array_time1 = [time_data1[i] for i in sorted(common_indices)]
array_time2 = [time_data2[i] for i in sorted(common_indices)]


def read_file_memory(filepath):
    """Read performance data from the given file and return a dictionary."""
    memory_usages = {}
    with open(filepath, 'r') as file:
        for line in file:
            mem_match = re.search(r': (\d+\.\d+) MB', line)
            index_match = re.search(r'enc_(\d+).py', line)
            if mem_match and index_match:
                index = int(index_match.group(1))
                memory_usage = float(mem_match.group(1))
                memory_usages[index] = memory_usage
    # print(f"Loaded {len(memory_usages)} entries from {filepath}")
    return memory_usages

def extract_number(key):
     return key
    # match = re.search(r'\d+', filename)
    # return int(match.group()) if match else None

data1 = read_file_memory('./result/Memory/Answers_memory_prompt1.txt')
data2 = read_file_memory('./result/Memory/memory_prompt1.txt')


filenames = sorted(set(data1.keys()) & set(data2.keys()))
array_memory1 = [data1[name] for name in filenames]
array_memory2 = [data2[name] for name in filenames]



def parse_data(filename):
    cpu_usages = {}
    with open(filename, 'r') as file:
        for line in file:
            cpu_match = re.search(r'CPU Usages = \[(\d+\.\d+)\]', line)
            index_match = re.search(r'enc_(\d+)', line)
            if cpu_match and index_match:
                index = int(index_match.group(1))
                cpu_usage = float(cpu_match.group(1))
                cpu_usages[index] = cpu_usage
    return cpu_usages

data_cpu1 = parse_data('./result/CPU/Answers_cpu_prompt1.txt')
data_cpu2 = parse_data('./result/CPU/cpu_prompt1.txt')


filenames = sorted(set(data_cpu1.keys()) & set(data_cpu2.keys()))
array_cpu1 = [data_cpu1[name] for name in filenames]
array_cpu2 = [data_cpu2[name] for name in filenames]

result1 = count_differences(array_time1, array_time2)
print("Number of scripts with more than 20% difference in runtime:", result1)
result2 = count_differences(array_memory1, array_memory2)
print("Number of scripts with more than 20% difference in Memory Usage:", result2)
result3 = count_differences(array_cpu1, array_cpu2)
print("Number of scripts with more than 20% difference in CPU Utilization:", result3)

union = set(result1[0]) | set(result2[0]) | set(result3[0])
print("the union of three sets:", union, len(union))