'''Write a function to sort the given list based on the occurrence of first element of tuples.
'''
def sort_on_occurence(lst):
  dct = {}
  for i, j in lst:
    dct.setdefault(i, []).append(j)
  return ([(i, *dict.fromkeys(j), len(j)) for i, j in dct.items()])
'''
Standard answer: 
def sort_on_occurence(lst): 
	dct = {} 
	for i, j in lst: 
		dct.setdefault(i, []).append(j) 
	return ([(i, *dict.fromkeys(j), len(j)) 
				for i, j in dct.items()]) 
'''
for _ in range(30):
    assert sort_on_occurence([(1, '<PERSON>'), (2, '<PERSON>'), (1, '<PERSON>')]) == [(1, '<PERSON>', '<PERSON>', 2), (2, '<PERSON>', 1)]
    assert sort_on_occurence([('b', 'ball'), ('a', 'arm'), ('b', 'b'), ('a', 'ant')]) == [('b', 'ball', 'b', 2), ('a', 'arm', 'ant', 2)]
    assert sort_on_occurence([(2, '<PERSON>'), (3, '<PERSON><PERSON>'), (2, '<PERSON>')]) == [(2, '<PERSON>', '<PERSON>', 2), (3, '<PERSON><PERSON>', 1)]
