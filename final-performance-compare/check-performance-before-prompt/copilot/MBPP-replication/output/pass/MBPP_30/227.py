'''Write a function to find minimum of three numbers.
'''
def min_of_three(a,b,c):
      if (a <= b) and (a <= c):
        smallest = a
      elif (b <= a) and (b <= c):
        smallest = b
      else:
        smallest = c
      return smallest
'''
Standard answer: 
def min_of_three(a,b,c): 
      if (a <= b) and (a <= c): 
        smallest = a 
      elif (b <= a) and (b <= c): 
        smallest = b 
      else: 
        smallest = c 
      return smallest 
'''
for _ in range(30):
    assert min_of_three(10,20,0)==0
    assert min_of_three(19,15,18)==15
    assert min_of_three(-10,-20,-30)==-30
