'''Write a function to sum all amicable numbers from 1 to a specified number.
'''
def amicable_numbers_sum(n):
    def d(n):
        return sum([i for i in range(1, n) if n % i == 0])
    amicable = set()
    for i in range(2, n+1):
        a = d(i)
        if a != i and d(a) == i:
            amicable.add(i)
            amicable.add(a)
    return sum(amicable)
'''
Standard answer: 
def amicable_numbers_sum(limit):
    if not isinstance(limit, int):
        return "Input is not an integer!"
    if limit < 1:
        return "Input must be bigger than 0!"
    amicables = set()
    for num in range(2, limit+1):
        if num in amicables:
            continue
        sum_fact = sum([fact for fact in range(1, num) if num % fact == 0])
        sum_fact2 = sum([fact for fact in range(1, sum_fact) if sum_fact % fact == 0])
        if num == sum_fact2 and num != sum_fact:
            amicables.add(num)
            amicables.add(sum_fact2)
    return sum(amicables)
'''
for _ in range(30):
    assert amicable_numbers_sum(999)==504
    assert amicable_numbers_sum(9999)==31626
    assert amicable_numbers_sum(99)==0
