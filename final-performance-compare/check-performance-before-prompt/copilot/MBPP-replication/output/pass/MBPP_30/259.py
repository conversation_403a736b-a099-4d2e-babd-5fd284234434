'''Write a function to maximize the given two tuples.
'''
def maximize_elements(test_tup1, test_tup2):
  res = []
  for i in range(len(test_tup1)):
    res.append(tuple(max(test_tup1[i][j], test_tup2[i][j]) for j in range(len(test_tup1[i])))
    )
  return (tuple(res))
'''
Standard answer: 
def maximize_elements(test_tup1, test_tup2):
  res = tuple(tuple(max(a, b) for a, b in zip(tup1, tup2))
   for tup1, tup2 in zip(test_tup1, test_tup2))
  return (res) 
'''
for _ in range(30):
    assert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))
    assert maximize_elements(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((7, 8), (5, 10), (3, 10), (8, 11))
    assert maximize_elements(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((8, 9), (6, 11), (4, 11), (9, 12))
