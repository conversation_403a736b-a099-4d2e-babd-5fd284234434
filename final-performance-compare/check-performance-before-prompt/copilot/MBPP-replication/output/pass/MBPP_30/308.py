'''Write a function to find the specified number of largest products from two given lists.
'''
def large_product(nums1, nums2, N):
    result = []
    for i in nums1:
        for j in nums2:
            result.append(i*j)
    result.sort(reverse=True)
    return result[:N]
'''
Standard answer: 
def large_product(nums1, nums2, N):
    result = sorted([x*y for x in nums1 for y in nums2], reverse=True)[:N]
    return result
'''
for _ in range(30):
    assert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],3)==[60, 54, 50]
    assert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],4)==[60, 54, 50, 48]
    assert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],5)==[60, 54, 50, 48, 45]
