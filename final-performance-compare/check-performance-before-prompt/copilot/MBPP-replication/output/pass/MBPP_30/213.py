'''Write a function to perform the concatenation of two string tuples.
'''
def concatenate_strings(test_tup1, test_tup2):
  res = []
  for i in range(len(test_tup1)):
    res.append(test_tup1[i] + test_tup2[i])
  return (tuple(res))
'''
Standard answer: 
def concatenate_strings(test_tup1, test_tup2):
  res = tuple(ele1 + ele2 for ele1, ele2 in zip(test_tup1, test_tup2))
  return (res) 
'''
for _ in range(30):
    assert concatenate_strings(("<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"), (" <PERSON>", " <PERSON><PERSON><PERSON>", " Garg")) == ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON> Garg')
    assert concatenate_strings(("<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"), (" <PERSON><PERSON>", " <PERSON><PERSON>", " <PERSON>")) == ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>')
    assert concatenate_strings(("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"), ("<PERSON><PERSON>", " <PERSON><PERSON><PERSON>", "<PERSON><PERSON>")) == ('<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>nSethi')
