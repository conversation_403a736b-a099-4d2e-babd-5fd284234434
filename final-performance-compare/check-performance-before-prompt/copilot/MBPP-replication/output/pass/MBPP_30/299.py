'''Write a function to calculate the maximum aggregate from the list of tuples.
'''
def max_aggregate(stdata):
    temp = {}
    for name, marks in stdata:
        if name not in temp:
            temp[name] = marks
        else:
            temp[name] += marks
    max_aggregate = max(temp, key=temp.get)
    return (max_aggregate, temp[max_aggregate])
'''
Standard answer: 
from collections import defaultdict
def max_aggregate(stdata):
    temp = defaultdict(int)
    for name, marks in stdata:
        temp[name] += marks
    return max(temp.items(), key=lambda x: x[1])
'''
for _ in range(30):
    assert max_aggregate([('<PERSON>',90),('<PERSON>',88),('<PERSON>',7),('<PERSON>',122),('<PERSON>',84)])==('<PERSON>', 212)
    assert max_aggregate([('<PERSON>',50),('<PERSON>',48),('<PERSON>',37),('<PERSON>',22),('<PERSON>',14)])==('<PERSON>', 72)
    assert max_aggregate([('<PERSON>',10),('<PERSON>',20),('<PERSON>',30),('<PERSON>',40),('<PERSON>',50)])==('<PERSON>', 70)
