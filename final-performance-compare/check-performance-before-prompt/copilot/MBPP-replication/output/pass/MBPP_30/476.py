'''Write a python function to find the sum of the largest and smallest value in a given array.
'''
def big_sum(nums):
    max_num = max(nums)
    min_num = min(nums)
    return max_num + min_num
'''
Standard answer: 
def big_sum(nums):
      sum= max(nums)+min(nums)
      return sum
'''
for _ in range(30):
    assert big_sum([1,2,3]) == 4
    assert big_sum([-1,2,3,4]) == 3
    assert big_sum([2,3,6]) == 8
