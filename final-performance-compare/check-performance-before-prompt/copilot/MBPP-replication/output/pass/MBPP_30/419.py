'''Write a function to round every number of a given list of numbers and print the total sum multiplied by the length of the list.
'''
def round_and_sum(list1):
  for i in range(len(list1)):
    list1[i]=round(list1[i])
  round_and_sum=sum(list1)*len(list1)
  return round_and_sum
'''
Standard answer: 
def round_and_sum(list1):
  lenght=len(list1)
  round_and_sum=sum(list(map(round,list1))* lenght)
  return round_and_sum
'''
for _ in range(30):
    assert round_and_sum([22.4, 4.0, -16.22, -9.10, 11.00, -12.22, 14.20, -5.20, 17.50])==243
    assert round_and_sum([5,2,9,24.3,29])==345
    assert round_and_sum([25.0,56.7,89.2])==513
