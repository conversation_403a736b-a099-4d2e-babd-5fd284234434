'''Write a function to check if all values are same in a dictionary.
'''
def check_value(dict, n):
    for x in dict.values():
        if x != n:
            return False
    return True
'''
Standard answer: 
def check_value(dict, n):
    result = all(x == n for x in dict.values()) 
    return result
'''
for _ in range(30):
    assert check_value({'Cierra Vega': 12, 'Al<PERSON> Cantrell': 12, 'Kierra Gentry': 12, '<PERSON>': 12},10)==False
    assert check_value({'Cierra Vega': 12, '<PERSON><PERSON>': 12, 'Kierra Gentry': 12, '<PERSON>': 12},12)==True
    assert check_value({'Cierra Vega': 12, 'Al<PERSON> Cantrell': 12, 'Kierra Gentry': 12, '<PERSON>': 12},5)==False
