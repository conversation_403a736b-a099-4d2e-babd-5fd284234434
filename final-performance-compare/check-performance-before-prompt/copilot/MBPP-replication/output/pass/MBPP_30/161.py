'''Write a function to remove all elements from a given list present in another list.
'''
def remove_elements(list1, list2):
    result = []
    for x in list1:
        if x not in list2:
            result.append(x)
    return result
'''
Standard answer: 
def remove_elements(list1, list2):
    result = [x for x in list1 if x not in list2]
    return result
'''
for _ in range(30):
    assert remove_elements([1,2,3,4,5,6,7,8,9,10],[2,4,6,8])==[1, 3, 5, 7, 9, 10]
    assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],[1, 3, 5, 7])==[2, 4, 6, 8, 9, 10]
    assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],[5,7])==[1, 2, 3, 4, 6, 8, 9, 10]
