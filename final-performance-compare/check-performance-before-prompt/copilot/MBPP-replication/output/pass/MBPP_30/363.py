'''Write a function to add the k elements to each element in the tuple.
'''
def add_K_element(test_list, K):
  res = []
  for i in test_list:
    temp = []
    for j in i:
      temp.append(j+K)
    res.append(tuple(temp))
  return (res)
'''
Standard answer: 
def add_K_element(test_list, K):
  res = [tuple(j + K for j in sub ) for sub in test_list]
  return (res) 
'''
for _ in range(30):
    assert add_K_element([(1, 3, 4), (2, 4, 6), (3, 8, 1)], 4) == [(5, 7, 8), (6, 8, 10), (7, 12, 5)]
    assert add_K_element([(1, 2, 3), (4, 5, 6), (7, 8, 9)], 8) == [(9, 10, 11), (12, 13, 14), (15, 16, 17)]
    assert add_K_element([(11, 12, 13), (14, 15, 16), (17, 18, 19)], 9) == [(20, 21, 22), (23, 24, 25), (26, 27, 28)]
