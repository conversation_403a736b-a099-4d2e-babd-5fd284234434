'''Write a function to find the number of possible sequences of length n such that each of the next element is greater than or equal to twice of the previous element but less than or equal to m.
'''
def get_total_number_of_sequences(m, n):
    T = [[0 for i in range(n+1)] for i in range(m+1)]
    for i in range(m+1):
        for j in range(n+1):
            if i==0 or j==0:
                T[i][j] = 0
            elif i<j:
                T[i][j] = 0
            elif j==1:
                T[i][j] = i
            else:
                T[i][j] = T[i-1][j] + T[i//2][j-1]
    return T[m][n]
'''
Standard answer: 
def get_total_number_of_sequences(m,n): 
	T=[[0 for i in range(n+1)] for i in range(m+1)] 
	for i in range(m+1): 
		for j in range(n+1): 
			if i==0 or j==0: 
				T[i][j]=0
			elif i<j: 
				T[i][j]=0
			elif j==1: 
				T[i][j]=i 
			else: 
				T[i][j]=T[i-1][j]+T[i//2][j-1] 
	return T[m][n]
'''
for _ in range(30):
    assert get_total_number_of_sequences(10, 4) == 4
    assert get_total_number_of_sequences(5, 2) == 6
    assert get_total_number_of_sequences(16, 3) == 84
