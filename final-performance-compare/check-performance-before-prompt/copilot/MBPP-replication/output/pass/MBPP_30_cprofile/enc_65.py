from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function of recursion list sum.
    '''
    def recursive_list_sum(data_list):
        total = 0
        for element in data_list:
            if type(element) == type([]):
                total = total + recursive_list_sum(element)
            else:
                total = total + element
        return total
    '''
    Standard answer: 
    def recursive_list_sum(data_list):
    	total = 0
    	for element in data_list:
    		if type(element) == type([]):
    			total = total + recursive_list_sum(element)
    		else:
    			total = total + element
    	return total
    '''
    for _ in range(30):
        assert recursive_list_sum(([1, 2, [3,4],[5,6]]))==21
        assert recursive_list_sum(([7, 10, [15,14],[19,41]]))==106
        assert recursive_list_sum(([10, 20, [30,40],[50,60]]))==210
