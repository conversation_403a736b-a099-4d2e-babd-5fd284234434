from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to print all permutations of a given string including duplicates.
    '''
    def permute_string(str):
        if len(str) == 0:
            return ['']
        prev_list = permute_string(str[1:len(str)])
        next_list = []
        for i in range(0,len(prev_list)):
            for j in range(0,len(str)):
                new_str = prev_list[i][0:j]+str[0]+prev_list[i][j:len(str)-1]
                if new_str not in next_list:
                    next_list.append(new_str)
        return next_list
    '''
    Standard answer: 
    def permute_string(str):
        if len(str) == 0:
            return ['']
        prev_list = permute_string(str[1:len(str)])
        next_list = []
        for i in range(0,len(prev_list)):
            for j in range(0,len(str)):
                new_str = prev_list[i][0:j]+str[0]+prev_list[i][j:len(str)-1]
                if new_str not in next_list:
                    next_list.append(new_str)
        return next_list
    '''
    for _ in range(30):
        assert permute_string('ab')==['ab', 'ba']
        assert permute_string('abc')==['abc', 'bac', 'bca', 'acb', 'cab', 'cba']
        assert permute_string('abcd')==['abcd', 'bacd', 'bcad', 'bcda', 'acbd', 'cabd', 'cbad', 'cbda', 'acdb', 'cadb', 'cdab', 'cdba', 'abdc', 'badc', 'bdac', 'bdca', 'adbc', 'dabc', 'dbac', 'dbca', 'adcb', 'dacb', 'dcab', 'dcba']
