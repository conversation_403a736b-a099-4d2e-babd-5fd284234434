from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to find the maximum of two numbers.
    '''
    def maximum(a,b):
        if a >= b:
            return a
        else:
            return b
    '''
    Standard answer: 
    def maximum(a,b):   
        if a >= b: 
            return a 
        else: 
            return b 
    '''
    for _ in range(30):
        assert maximum(5,10) == 10
        assert maximum(-1,-2) == -1
        assert maximum(9,7) == 9
