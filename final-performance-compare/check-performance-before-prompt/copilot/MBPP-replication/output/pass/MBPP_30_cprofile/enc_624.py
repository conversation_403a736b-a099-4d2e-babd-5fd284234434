from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to convert the given string to upper case.
    '''
    def is_upper(string):
      return (string.upper())
    '''
    Standard answer: 
    def is_upper(string):
      return (string.upper())
    '''
    for _ in range(30):
        assert is_upper("person") =="PERSON"
        assert is_upper("final") == "FINAL"
        assert is_upper("Valid") == "VALID"
