from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to check if the given integer is a prime number.
    '''
    def prime_num(num):
      if num >=1:
       for i in range(2, num//2):
         if (num % i) == 0:
                    return False
         else:
                    return True
      else:
              return False
    '''
    Standard answer: 
    def prime_num(num):
      if num >=1:
       for i in range(2, num//2):
         if (num % i) == 0:
                    return False
         else:
                    return True
      else:
              return False
    '''
    for _ in range(30):
        assert prime_num(13)==True
        assert prime_num(7)==True
        assert prime_num(-1010)==False
