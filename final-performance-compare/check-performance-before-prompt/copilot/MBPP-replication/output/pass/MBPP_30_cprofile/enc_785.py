from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to convert tuple string to integer tuple.
    '''
    def tuple_str_int(test_str):
      res = tuple(int(num) for num in test_str.replace('(', '').replace(')', '').replace('...', '').split(', '))
      return (res)
    '''
    Standard answer: 
    def tuple_str_int(test_str):
      res = tuple(int(num) for num in test_str.replace('(', '').replace(')', '').replace('...', '').split(', '))
      return (res) 
    '''
    for _ in range(30):
        assert tuple_str_int("(7, 8, 9)") == (7, 8, 9)
        assert tuple_str_int("(1, 2, 3)") == (1, 2, 3)
        assert tuple_str_int("(4, 5, 6)") == (4, 5, 6)
