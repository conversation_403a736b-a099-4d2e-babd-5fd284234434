from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to check if the string is a valid email address or not using regex.
    '''
    def check_email(email):
        import re
        regex = '^[a-z0-9]+[\._]?[a-z0-9]+[@]\w+[.]\w{2,3}$'
        if(re.search(regex,email)):
            return ("Valid Email")
        else:
            return ("Invalid Email")
    '''
    Standard answer: 
    import re 
    regex = '^[a-z0-9]+[\._]?[a-z0-9]+[@]\w+[.]\w{2,3}$'
    def check_email(email): 
    	if(re.search(regex,email)): 
    		return ("Valid Email") 
    	else: 
    		return ("Invalid Email") 
    '''
    for _ in range(30):
        assert check_email("<EMAIL>") == 'Valid Email'
        assert check_email("<EMAIL>") == 'Valid Email'
        assert check_email("ankitaoie326.com") == 'Invalid Email'
