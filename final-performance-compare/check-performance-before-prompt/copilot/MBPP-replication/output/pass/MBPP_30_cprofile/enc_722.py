from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to filter the height and width of students which are stored in a dictionary.
    '''
    def filter_data(students,h,w):
        result = {k: s for k, s in students.items() if s[0] >=h and s[1] >=w}
        return result
    '''
    Standard answer: 
    def filter_data(students,h,w):
        result = {k: s for k, s in students.items() if s[0] >=h and s[1] >=w}
        return result    
    '''
    for _ in range(30):
        assert filter_data({'Cierra Vega': (6.2, 70), '<PERSON><PERSON>ll': (5.9, 65), 'Kierra Gentry': (6.0, 68), '<PERSON>': (5.8, 66)},6.0,70)=={'Cierra Vega': (6.2, 70)}
        assert filter_data({'Cierra Vega': (6.2, 70), '<PERSON><PERSON>': (5.9, 65), 'Kierra Gentry': (6.0, 68), '<PERSON>': (5.8, 66)},5.9,67)=={'<PERSON><PERSON><PERSON>': (6.2, 70),'<PERSON><PERSON>ra <PERSON>': (6.0, 68)}
        assert filter_data({'Cier<PERSON> Vega': (6.2, 70), '<PERSON><PERSON>ll': (5.9, 65), '<PERSON><PERSON><PERSON>': (6.0, 68), '<PERSON>': (5.8, 66)},5.7,64)=={'<PERSON><PERSON><PERSON>': (6.2, 70),'<PERSON><PERSON> <PERSON><PERSON>': (5.9, 65),'<PERSON><PERSON><PERSON>': (6.0, 68),'<PERSON> <PERSON>': (5.8, 66)}
