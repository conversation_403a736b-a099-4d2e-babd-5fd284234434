from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to extract the nth element from a given list of tuples.
    '''
    def extract_nth_element(list1, n):
        result = []
        for i in list1:
            result.append(i[n])
        return result
    '''
    Standard answer: 
    def extract_nth_element(list1, n):
        result = [x[n] for x in list1]
        return result
    '''
    for _ in range(30):
        assert extract_nth_element([('<PERSON>son Fulton', 98, 99), ('Brady Kent', 97, 96), ('<PERSON>', 91, 94), ('<PERSON>', 94, 98)] ,0)==['<PERSON><PERSON> Fulton', '<PERSON>', '<PERSON>', '<PERSON>']
        assert extract_nth_element([('<PERSON><PERSON> Fulton', 98, 99), ('<PERSON>', 97, 96), ('<PERSON>', 91, 94), ('<PERSON>', 94, 98)] ,2)==[99, 96, 94, 98]
        assert extract_nth_element([('<PERSON><PERSON> Fulton', 98, 99), ('<PERSON>', 97, 96), ('<PERSON>', 91, 94), ('<PERSON>', 94, 98)],1)==[98, 97, 91, 94]
