from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the intersection of two arrays using lambda function.
    '''
    def intersection_array(array_nums1,array_nums2):
     result = list(filter(lambda x: x in array_nums1, array_nums2)) 
     return result
    '''
    Standard answer: 
    def intersection_array(array_nums1,array_nums2):
     result = list(filter(lambda x: x in array_nums1, array_nums2)) 
     return result
    '''
    for _ in range(30):
        assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]
        assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[3,5,7,9])==[3,5,7,9]
        assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[10,20,30,40])==[10]
