from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to perfom the rear element extraction from list of tuples records.
    '''
    def rear_extract(test_list):
      res = [lis[-1] for lis in test_list]
      return (res)
    '''
    Standard answer: 
    def rear_extract(test_list):
      res = [lis[-1] for lis in test_list]
      return (res) 
    '''
    for _ in range(30):
        assert rear_extract([(1, '<PERSON><PERSON>', 21), (2, '<PERSON><PERSON><PERSON>', 20), (3, 'Ki<PERSON>', 19)]) == [21, 20, 19]
        assert rear_extract([(1, '<PERSON>', 36), (2, '<PERSON><PERSON>', 25), (3, '<PERSON><PERSON>', 45)]) == [36, 25, 45]
        assert rear_extract([(1, '<PERSON><PERSON><PERSON>', 14), (2, '<PERSON><PERSON><PERSON>', 36), (3, '<PERSON><PERSON>', 56)]) == [14, 36, 56]
