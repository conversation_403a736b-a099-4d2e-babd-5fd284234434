from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to filter even numbers using lambda function.
    '''
    def filter_evennumbers(nums):
      even_nums = list(filter(lambda x: x%2 == 0, nums))
      return even_nums
    '''
    Standard answer: 
    def filter_evennumbers(nums):
     even_nums = list(filter(lambda x: x%2 == 0, nums))
     return even_nums
    '''
    for _ in range(30):
        assert filter_evennumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[2, 4, 6, 8, 10]
        assert filter_evennumbers([10,20,45,67,84,93])==[10,20,84]
        assert filter_evennumbers([5,7,9,8,6,4,3])==[8,6,4]
