from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find minimum k records from tuple list.
    '''
    def min_k(test_list, K):
      res = sorted(test_list, key = lambda x: x[1])[:K]
      return (res)
    '''
    Standard answer: 
    def min_k(test_list, K):
      res = sorted(test_list, key = lambda x: x[1])[:K]
      return (res) 
    '''
    for _ in range(30):
        assert min_k([('<PERSON><PERSON><PERSON>', 10), ('Akshat', 4), ('Akash', 2), ('<PERSON><PERSON>', 8)], 2) == [('Akash', 2), ('Akshat', 4)]
        assert min_k([('Sanjeev', 11), ('Angat', 5), ('Akash', 3), ('Nepin', 9)], 3) == [('Akash', 3), ('Angat', 5), ('Nepin', 9)]
        assert min_k([('tan<PERSON><PERSON>', 14), ('<PERSON><PERSON>', 11), ('<PERSON><PERSON>', 9), ('SKD', 16)], 1) == [('<PERSON><PERSON>', 9)]
