from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to sum the length of the names of a given list of names after removing the names that start with a lowercase letter.
    '''
    def sample_nam(sample_names):
      result = []
      for i in sample_names:
        if i[0].isupper() and i[1:].islower():
          result.append(i)
      return len(''.join(result))
    '''
    Standard answer: 
    def sample_nam(sample_names):
      sample_names=list(filter(lambda el:el[0].isupper() and el[1:].islower(),sample_names))
      return len(''.join(sample_names))
    '''
    for _ in range(30):
        assert sample_nam(['sally', '<PERSON>', 'rebecca', '<PERSON>', '<PERSON>', 'keith'])==16
        assert sample_nam(["php", "res", "Python", "abcd", "Java", "aaa"])==10
        assert sample_nam(["abcd", "Python", "abba", "aba"])==6
