from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to extract only the rear index element of each string in the given tuple.
    '''
    def extract_rear(test_tuple):
      res = list(sub[len(sub) - 1] for sub in test_tuple)
      return (res)
    '''
    Standard answer: 
    def extract_rear(test_tuple):
      res = list(sub[len(sub) - 1] for sub in test_tuple)
      return (res) 
    '''
    for _ in range(30):
        assert extract_rear(('Mers', 'for', 'Vers') ) == ['s', 'r', 's']
        assert extract_rear(('Avenge', 'for', 'People') ) == ['e', 'r', 'e']
        assert extract_rear(('Gotta', 'get', 'go') ) == ['a', 't', 'o']
