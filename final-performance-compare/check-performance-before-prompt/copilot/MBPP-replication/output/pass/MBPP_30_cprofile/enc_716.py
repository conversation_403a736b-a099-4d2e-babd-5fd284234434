from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the perimeter of a rombus.
    '''
    def rombus_perimeter(a):
      perimeter=4*a
      return perimeter
    '''
    Standard answer: 
    def rombus_perimeter(a):
      perimeter=4*a
      return perimeter
    '''
    for _ in range(30):
        assert rombus_perimeter(10)==40
        assert rombus_perimeter(5)==20
        assert rombus_perimeter(4)==16
