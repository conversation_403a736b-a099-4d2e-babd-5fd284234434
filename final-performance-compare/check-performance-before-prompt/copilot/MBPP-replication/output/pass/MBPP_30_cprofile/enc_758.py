from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to count number of unique lists within a list.
    '''
    def unique_sublists(list1):
        result ={}
        for l in  list1: 
            result.setdefault(tuple(l), list()).append(1) 
        for a, b in result.items(): 
            result[a] = sum(b)
        return result
    '''
    Standard answer: 
    def unique_sublists(list1):
        result ={}
        for l in  list1: 
            result.setdefault(tuple(l), list()).append(1) 
        for a, b in result.items(): 
            result[a] = sum(b)
        return result
    '''
    for _ in range(30):
        assert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]] )=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}
        assert unique_sublists([['green', 'orange'], ['black'], ['green', 'orange'], ['white']])=={('green', 'orange'): 2, ('black',): 1, ('white',): 1}
        assert unique_sublists([[10, 20, 30, 40], [60, 70, 50, 50], [90, 100, 200]])=={(10, 20, 30, 40): 1, (60, 70, 50, 50): 1, (90, 100, 200): 1}
