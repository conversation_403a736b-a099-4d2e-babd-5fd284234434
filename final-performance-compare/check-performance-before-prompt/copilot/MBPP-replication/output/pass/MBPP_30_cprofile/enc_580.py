from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to extract the even elements in the nested mixed tuple.
    '''
    def extract_even(test_tuple):
      res = tuple()
      def even_ele(test_tuple, even_fnc): 
        res = tuple() 
        for ele in test_tuple: 
          if isinstance(ele, tuple): 
            res += (even_ele(ele, even_fnc), ) 
          elif even_fnc(ele): 
            res += (ele, ) 
        return res 
      res = even_ele(test_tuple, lambda x: x % 2 == 0)
      return (res)
    '''
    Standard answer: 
    def even_ele(test_tuple, even_fnc): 
    	res = tuple() 
    	for ele in test_tuple: 
    		if isinstance(ele, tuple): 
    			res += (even_ele(ele, even_fnc), ) 
    		elif even_fnc(ele): 
    			res += (ele, ) 
    	return res 
    def extract_even(test_tuple):
      res = even_ele(test_tuple, lambda x: x % 2 == 0)
      return (res) 
    '''
    for _ in range(30):
        assert extract_even((4, 5, (7, 6, (2, 4)), 6, 8)) == (4, (6, (2, 4)), 6, 8)
        assert extract_even((5, 6, (8, 7, (4, 8)), 7, 9)) == (6, (8, (4, 8)))
        assert extract_even((5, 6, (9, 8, (4, 6)), 8, 10)) == (6, (8, (4, 6)), 8, 10)
