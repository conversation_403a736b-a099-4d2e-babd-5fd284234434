from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the n'th star number.
    '''
    def find_star_num(n):
        return (6 * n * (n - 1) + 1)
    '''
    Standard answer: 
    def find_star_num(n): 
    	return (6 * n * (n - 1) + 1) 
    '''
    for _ in range(30):
        assert find_star_num(3) == 37
        assert find_star_num(4) == 73
        assert find_star_num(5) == 121
