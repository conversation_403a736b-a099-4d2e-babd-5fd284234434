from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the lateral surface area of a cube.
    '''
    def lateralsurface_cube(l):
      LSA = 4 * (l * l)
      return LSA
    '''
    Standard answer: 
    def lateralsurface_cube(l):
      LSA = 4 * (l * l)
      return LSA
    '''
    for _ in range(30):
        assert lateralsurface_cube(5)==100
        assert lateralsurface_cube(9)==324
        assert lateralsurface_cube(10)==400
