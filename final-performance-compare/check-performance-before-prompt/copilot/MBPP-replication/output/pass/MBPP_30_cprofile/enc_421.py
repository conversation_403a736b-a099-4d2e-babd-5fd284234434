from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to concatenate each element of tuple by the delimiter.
    '''
    def concatenate_tuple(test_tup):
        delim = "-"
        res = ''.join([str(ele) + delim for ele in test_tup])
        res = res[ : len(res) - len(delim)]
        return (str(res))
    '''
    Standard answer: 
    def concatenate_tuple(test_tup):
        delim = "-"
        res = ''.join([str(ele) + delim for ele in test_tup])
        res = res[ : len(res) - len(delim)]
        return (str(res)) 
    '''
    for _ in range(30):
        assert concatenate_tuple(("ID", "is", 4, "UTS") ) == 'ID-is-4-UTS'
        assert concatenate_tuple(("QWE", "is", 4, "RTY") ) == 'QWE-is-4-RTY'
        assert concatenate_tuple(("ZEN", "is", 4, "OP") ) == 'ZEN-is-4-OP'
