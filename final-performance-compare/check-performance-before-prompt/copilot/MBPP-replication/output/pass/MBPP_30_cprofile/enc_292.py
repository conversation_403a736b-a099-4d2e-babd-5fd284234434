from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to find quotient of two numbers.
    '''
    def find(n,m):
        q = n//m
        return (q)
    '''
    Standard answer: 
    def find(n,m):  
        q = n//m 
        return (q)
    '''
    for _ in range(30):
        assert find(10,3) == 3
        assert find(4,2) == 2
        assert find(20,5) == 4
