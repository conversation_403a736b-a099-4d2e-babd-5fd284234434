from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to extract the index minimum value record from the given tuples.
    '''
    def index_minimum(test_list):
      res = min(test_list, key = lambda x: x[1])[0]
      return (res)
    '''
    Standard answer: 
    from operator import itemgetter 
    def index_minimum(test_list):
      res = min(test_list, key = itemgetter(1))[0]
      return (res) 
    '''
    for _ in range(30):
        assert index_minimum([('<PERSON><PERSON>', 143), ('<PERSON><PERSON><PERSON>', 200), ('<PERSON>arsha', 100)]) == 'Varsha'
        assert index_minimum([('<PERSON>sh', 185), ('<PERSON><PERSON>', 125), ('<PERSON><PERSON>', 175)]) == 'Dawood'
        assert index_minimum([('Sai', 345), ('<PERSON><PERSON>', 145), ('<PERSON><PERSON>', 96)]) == 'Ayesha'
