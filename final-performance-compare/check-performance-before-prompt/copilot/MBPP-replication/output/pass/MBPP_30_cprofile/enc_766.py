from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to iterate over all pairs of consecutive items in a given list.
    '''
    def pair_wise(l1):
        temp = []
        for i in range(len(l1) - 1):
            current_element, next_element = l1[i], l1[i + 1]
            x = (current_element, next_element)
            temp.append(x)
        return temp
    '''
    Standard answer: 
    def pair_wise(l1):
        temp = []
        for i in range(len(l1) - 1):
            current_element, next_element = l1[i], l1[i + 1]
            x = (current_element, next_element)
            temp.append(x)
        return temp
    '''
    for _ in range(30):
        assert pair_wise([1,1,2,3,3,4,4,5])==[(1, 1), (1, 2), (2, 3), (3, 3), (3, 4), (4, 4), (4, 5)]
        assert pair_wise([1,5,7,9,10])==[(1, 5), (5, 7), (7, 9), (9, 10)]
        assert pair_wise([1,2,3,4,5,6,7,8,9,10])==[(1, 2), (2, 3), (3, 4), (4, 5), (5, 6), (6, 7), (7, 8), (8, 9), (9, 10)]
