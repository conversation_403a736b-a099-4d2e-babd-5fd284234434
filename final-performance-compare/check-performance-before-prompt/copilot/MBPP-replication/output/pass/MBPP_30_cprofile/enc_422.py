from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to find the average of cubes of first n natural numbers.
    '''
    def find_Average_Of_Cube(n):  
        sum = 0
        for i in range(1, n + 1): 
            sum += i * i * i  
        return round(sum / n, 6)
    '''
    Standard answer: 
    def find_Average_Of_Cube(n):  
        sum = 0
        for i in range(1, n + 1): 
            sum += i * i * i  
        return round(sum / n, 6) 
    '''
    for _ in range(30):
        assert find_Average_Of_Cube(2) == 4.5
        assert find_Average_Of_Cube(3) == 12
        assert find_Average_Of_Cube(1) == 1
