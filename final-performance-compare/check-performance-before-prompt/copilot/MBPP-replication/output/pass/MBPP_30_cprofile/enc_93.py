from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to calculate the value of 'a' to the power 'b'.
    '''
    def power(a, b):
      if b == 0:
        return 1
      elif a == 0:
        return 0
      elif b == 1:
        return a
      else:
        return a * power(a, b-1)
    '''
    Standard answer: 
    def power(a,b):
    	if b==0:
    		return 1
    	elif a==0:
    		return 0
    	elif b==1:
    		return a
    	else:
    		return a*power(a,b-1)
    '''
    for _ in range(30):
        assert power(3,4) == 81
        assert power(2,3) == 8
        assert power(5,5) == 3125
