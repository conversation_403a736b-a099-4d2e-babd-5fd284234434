from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to find the length of the longest word.
    '''
    def len_log(list1):
        max=len(list1[0])
        for i in list1:
            if len(i)>max:
                max=len(i)
        return max
    '''
    Standard answer: 
    def len_log(list1):
        max=len(list1[0])
        for i in list1:
            if len(i)>max:
                max=len(i)
        return max
    '''
    for _ in range(30):
        assert len_log(["python","PHP","bigdata"]) == 7
        assert len_log(["a","ab","abc"]) == 3
        assert len_log(["small","big","tall"]) == 5
