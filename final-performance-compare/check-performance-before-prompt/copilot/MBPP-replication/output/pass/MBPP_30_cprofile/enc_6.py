from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to check whether the two numbers differ at one bit position only or not.
    '''
    def differ_At_One_Bit_Pos(a,b):
        return bin(a^b).count('1') == 1
    '''
    Standard answer: 
    def is_Power_Of_Two (x): 
        return x and (not(x & (x - 1))) 
    def differ_At_One_Bit_Pos(a,b): 
        return is_Power_Of_Two(a ^ b)
    '''
    for _ in range(30):
        assert differ_At_One_Bit_Pos(13,9) == True
        assert differ_At_One_Bit_Pos(15,8) == False
        assert differ_At_One_Bit_Pos(2,4) == False
