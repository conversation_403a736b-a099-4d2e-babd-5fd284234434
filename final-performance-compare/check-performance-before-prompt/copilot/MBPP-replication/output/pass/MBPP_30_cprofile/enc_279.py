from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the nth decagonal number.
    '''
    def is_num_decagonal(n):
        return 4 * n * n - 3 * n
    '''
    Standard answer: 
    def is_num_decagonal(n): 
    	return 4 * n * n - 3 * n 
    '''
    for _ in range(30):
        assert is_num_decagonal(3) == 27
        assert is_num_decagonal(7) == 175
        assert is_num_decagonal(10) == 370
