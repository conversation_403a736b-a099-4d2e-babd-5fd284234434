from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to sort a list of tuples using lambda.
    '''
    def subject_marks(subjectmarks):
      subjectmarks.sort(key = lambda x: x[1])
      return subjectmarks
    '''
    Standard answer: 
    def subject_marks(subjectmarks):
    #subject_marks = [('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)])
     subjectmarks.sort(key = lambda x: x[1])
     return subjectmarks
    '''
    for _ in range(30):
        assert subject_marks([('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)])==[('Social sciences', 82), ('English', 88), ('Science', 90), ('Maths', 97)]
        assert subject_marks([('Telugu',49),('Hindhi',54),('Social',33)])==([('Social',33),('Telugu',49),('Hindhi',54)])
        assert subject_marks([('Physics',96),('Chemistry',97),('Biology',45)])==([('Biology',45),('Physics',96),('Chemistry',97)])
