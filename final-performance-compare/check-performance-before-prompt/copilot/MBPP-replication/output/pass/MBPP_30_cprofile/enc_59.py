from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the nth octagonal number.
    '''
    def is_octagonal(n):
    	return 3 * n * n - 2 * n
    '''
    Standard answer: 
    def is_octagonal(n): 
    	return 3 * n * n - 2 * n 
    '''
    for _ in range(30):
        assert is_octagonal(5) == 65
        assert is_octagonal(10) == 280
        assert is_octagonal(15) == 645
