from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to count the number of sublists containing a particular element.
    '''
    def count_element_in_list(list1, x): 
        ctr = 0
        for i in range(len(list1)): 
            if x in list1[i]: 
                ctr+= 1          
        return ctr
    '''
    Standard answer: 
    def count_element_in_list(list1, x): 
        ctr = 0
        for i in range(len(list1)): 
            if x in list1[i]: 
                ctr+= 1          
        return ctr
    '''
    for _ in range(30):
        assert count_element_in_list([[1, 3], [5, 7], [1, 11], [1, 15, 7]],1)==3
        assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'A')==3
        assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'E')==1
