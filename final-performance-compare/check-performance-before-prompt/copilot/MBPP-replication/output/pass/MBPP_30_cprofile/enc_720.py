from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to add a dictionary to the tuple.
    '''
    def add_dict_to_tuple(test_tup, test_dict):
      test_tup = list(test_tup)
      test_tup.append(test_dict)
      test_tup = tuple(test_tup)
      return (test_tup)
    '''
    Standard answer: 
    def add_dict_to_tuple(test_tup, test_dict):
      test_tup = list(test_tup)
      test_tup.append(test_dict)
      test_tup = tuple(test_tup)
      return (test_tup) 
    '''
    for _ in range(30):
        assert add_dict_to_tuple((4, 5, 6), {"MSAM" : 1, "is" : 2, "best" : 3} ) == (4, 5, 6, {'MSAM': 1, 'is': 2, 'best': 3})
        assert add_dict_to_tuple((1, 2, 3), {"UTS" : 2, "is" : 3, "Worst" : 4} ) == (1, 2, 3, {'UTS': 2, 'is': 3, 'Worst': 4})
        assert add_dict_to_tuple((8, 9, 10), {"POS" : 3, "is" : 4, "Okay" : 5} ) == (8, 9, 10, {'POS': 3, 'is': 4, 'Okay': 5})
