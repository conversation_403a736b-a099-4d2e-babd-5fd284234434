from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to check whether every odd index contains odd numbers of a given list.
    '''
    def odd_position(nums):
        for i in range(len(nums)):
            if i%2 != 0 and nums[i]%2 == 0:
                return False
        return True
    '''
    Standard answer: 
    def odd_position(nums):
    	return all(nums[i]%2==i%2 for i in range(len(nums)))
    '''
    for _ in range(30):
        assert odd_position([2,1,4,3,6,7,6,3]) == True
        assert odd_position([4,1,2]) == True
        assert odd_position([1,2,3]) == False
