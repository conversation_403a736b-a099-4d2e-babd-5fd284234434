from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to calculate the perimeter of a regular polygon.
    '''
    def perimeter_polygon(s,l):
      perimeter = s*l
      return perimeter
    '''
    Standard answer: 
    from math import tan, pi
    def perimeter_polygon(s,l):
      perimeter = s*l
      return perimeter
    '''
    for _ in range(30):
        assert perimeter_polygon(4,20)==80
        assert perimeter_polygon(10,15)==150
        assert perimeter_polygon(9,7)==63
