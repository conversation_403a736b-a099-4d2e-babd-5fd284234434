from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to remove spaces from a given string.
    '''
    def remove_spaces(str1):
      str1 = str1.replace(' ','')
      return str1
    '''
    Standard answer: 
    def remove_spaces(str1):
      str1 = str1.replace(' ','')
      return str1
    '''
    for _ in range(30):
        assert remove_spaces("a b c") == "abc"
        assert remove_spaces("1 2 3") == "123"
        assert remove_spaces(" b c") == "bc"
