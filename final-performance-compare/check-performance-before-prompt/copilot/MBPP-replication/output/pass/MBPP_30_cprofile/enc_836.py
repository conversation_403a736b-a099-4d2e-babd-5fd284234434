from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find length of the subarray having maximum sum.
    '''
    def max_sub_array_sum(a,size):
        max_so_far = -100000
        max_ending_here = 0
        start = 0
        end = 0
        s = 0
        for i in range(0,size):
            max_ending_here += a[i]
            if max_so_far < max_ending_here:
                max_so_far = max_ending_here
                start = s
                end = i
            if max_ending_here < 0:
                max_ending_here = 0
                s = i+1
        return (end - start + 1)
    '''
    Standard answer: 
    from sys import maxsize 
    def max_sub_array_sum(a,size): 
    	max_so_far = -maxsize - 1
    	max_ending_here = 0
    	start = 0
    	end = 0
    	s = 0
    	for i in range(0,size): 
    		max_ending_here += a[i] 
    		if max_so_far < max_ending_here: 
    			max_so_far = max_ending_here 
    			start = s 
    			end = i 
    		if max_ending_here < 0: 
    			max_ending_here = 0
    			s = i+1
    	return (end - start + 1)
    '''
    for _ in range(30):
        assert max_sub_array_sum([-2, -3, 4, -1, -2, 1, 5, -3],8) == 5
        assert max_sub_array_sum([1, -2, 1, 1, -2, 1],6) == 2
        assert max_sub_array_sum([-1, -2, 3, 4, 5],5) == 3
