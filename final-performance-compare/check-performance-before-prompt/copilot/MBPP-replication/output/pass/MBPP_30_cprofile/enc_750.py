from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to add the given tuple to the given list.
    '''
    def add_tuple(test_list, test_tup):
      for i in test_tup:
        test_list.append(i)
      return test_list
    '''
    Standard answer: 
    def add_tuple(test_list, test_tup):
      test_list += test_tup
      return (test_list) 
    '''
    for _ in range(30):
        assert add_tuple([5, 6, 7], (9, 10)) == [5, 6, 7, 9, 10]
        assert add_tuple([6, 7, 8], (10, 11)) == [6, 7, 8, 10, 11]
        assert add_tuple([7, 8, 9], (11, 12)) == [7, 8, 9, 11, 12]
