from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to convert a string to a list.
    '''
    def Convert(string):
        li = list(string.split(" "))
        return li
    '''
    Standard answer: 
    def Convert(string): 
        li = list(string.split(" ")) 
        return li 
    '''
    for _ in range(30):
        assert Convert('python program') == ['python','program']
        assert Convert('Data Analysis') ==['Data','Analysis']
        assert Convert('Hadoop Training') == ['Hadoop','Training']
