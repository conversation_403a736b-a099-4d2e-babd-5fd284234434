from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to check if the given expression is balanced or not.
    '''
    def check_expression(exp):
        stack = []
        for char in exp:
            if char in ['(', '{', '[']:
                stack.append(char)
            else:
                if not stack:
                    return False
                current_char = stack.pop()
                if current_char == '(':
                    if char != ')':
                        return False
                if current_char == '{':
                    if char != '}':
                        return False
                if current_char == '[':
                    if char != ']':
                        return False
        if stack:
            return False
        return True
    '''
    Standard answer: 
    from collections import deque
    def check_expression(exp):
        if len(exp) & 1:
            return False
        stack = deque()
        for ch in exp:
            if ch == '(' or ch == '{' or ch == '[':
                stack.append(ch)
            if ch == ')' or ch == '}' or ch == ']':
                if not stack:
                    return False
                top = stack.pop()
                if (top == '(' and ch != ')') or (top == '{' and ch != '}' or (top == '[' and ch != ']')):
                    return False
        return not stack
    '''
    for _ in range(30):
        assert check_expression("{()}[{}]") == True
        assert check_expression("{()}[{]") == False
        assert check_expression("{()}[{}][]({})") == True
