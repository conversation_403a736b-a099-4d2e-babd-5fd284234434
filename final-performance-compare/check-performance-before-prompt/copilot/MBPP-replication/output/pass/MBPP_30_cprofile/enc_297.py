from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to flatten a given nested list structure.
    '''
    def flatten_list(list1):
        result_list = []
        if not list1: return result_list
        stack = [list(list1)]
        while stack:
            c_num = stack.pop()
            next = c_num.pop()
            if c_num: stack.append(c_num)
            if isinstance(next, list):
                if next: stack.append(list(next))
            else: result_list.append(next)
        result_list.reverse()
        return result_list
    '''
    Standard answer: 
    def flatten_list(list1):
        result_list = []
        if not list1: return result_list
        stack = [list(list1)]
        while stack:
            c_num = stack.pop()
            next = c_num.pop()
            if c_num: stack.append(c_num)
            if isinstance(next, list):
                if next: stack.append(list(next))
            else: result_list.append(next)
        result_list.reverse()
        return result_list 
    '''
    for _ in range(30):
        assert flatten_list([0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]])==[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]
        assert flatten_list([[10, 20], [40], [30, 56, 25], [10, 20], [33], [40]])==[10, 20, 40, 30, 56, 25, 10, 20, 33, 40]
        assert flatten_list([[1,2,3], [4,5,6], [10,11,12], [7,8,9]])==[1, 2, 3, 4, 5, 6, 10, 11, 12, 7, 8, 9]
