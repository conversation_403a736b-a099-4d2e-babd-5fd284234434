from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to check if a dictionary is empty or not.
    '''
    def my_dict(dict1):
      if bool(dict1)==False:
         return True
      else:
         return False
    '''
    Standard answer: 
    def my_dict(dict1):
      if bool(dict1):
         return False
      else:
         return True
    '''
    for _ in range(30):
        assert my_dict({10})==False
        assert my_dict({11})==False
        assert my_dict({})==True
