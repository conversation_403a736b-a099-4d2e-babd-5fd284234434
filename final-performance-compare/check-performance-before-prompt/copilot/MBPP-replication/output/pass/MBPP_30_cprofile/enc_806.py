from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find maximum run of uppercase characters in the given string.
    '''
    def max_run_uppercase(test_str):
      cnt = 0
      res = 0
      for idx in range(0, len(test_str)):
        if test_str[idx].isupper():
          cnt += 1
        else:
          res = cnt
          cnt = 0
      if test_str[len(test_str) - 1].isupper():
        res = cnt
      return (res)
    '''
    Standard answer: 
    def max_run_uppercase(test_str):
      cnt = 0
      res = 0
      for idx in range(0, len(test_str)):
        if test_str[idx].isupper():
          cnt += 1
        else:
          res = cnt
          cnt = 0
      if test_str[len(test_str) - 1].isupper():
        res = cnt
      return (res)
    '''
    for _ in range(30):
        assert max_run_uppercase('GeMKSForGERksISBESt') == 5
        assert max_run_uppercase('PrECIOusMOVemENTSYT') == 6
        assert max_run_uppercase('GooGLEFluTTER') == 4
