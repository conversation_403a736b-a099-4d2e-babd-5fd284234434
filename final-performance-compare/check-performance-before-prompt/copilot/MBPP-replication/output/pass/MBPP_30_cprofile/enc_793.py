from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to find the last position of an element in a sorted array.
    '''
    def last(arr,x,n):
        low = 0
        high = n - 1
        res = -1  
        while (low <= high):
            mid = (low + high) // 2 
            if arr[mid] > x:
                high = mid - 1
            elif arr[mid] < x:
                low = mid + 1
            else:
                res = mid
                low = mid + 1
        return res
    '''
    Standard answer: 
    def last(arr,x,n):
        low = 0
        high = n - 1
        res = -1  
        while (low <= high):
            mid = (low + high) // 2 
            if arr[mid] > x:
                high = mid - 1
            elif arr[mid] < x:
                low = mid + 1
            else:
                res = mid
                low = mid + 1
        return res
    '''
    for _ in range(30):
        assert last([1,2,3],1,3) == 0
        assert last([1,1,1,2,3,4],1,6) == 2
        assert last([2,3,2,3,6,8,9],3,8) == 3
