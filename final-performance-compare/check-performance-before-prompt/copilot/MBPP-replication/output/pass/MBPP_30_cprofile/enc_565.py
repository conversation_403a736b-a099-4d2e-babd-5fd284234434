from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to split a string into characters.
    '''
    def split(word): 
        return [char for char in word]
    '''
    Standard answer: 
    def split(word): 
        return [char for char in word] 
    '''
    for _ in range(30):
        assert split('python') == ['p','y','t','h','o','n']
        assert split('Name') == ['N','a','m','e']
        assert split('program') == ['p','r','o','g','r','a','m']
