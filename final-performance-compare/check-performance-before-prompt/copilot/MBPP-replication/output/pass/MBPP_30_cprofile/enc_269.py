from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the ascii value of a character.
    '''
    def ascii_value(k):
      ch=k
      return ord(ch)
    '''
    Standard answer: 
    def ascii_value(k):
      ch=k
      return ord(ch)
    '''
    for _ in range(30):
        assert ascii_value('A')==65
        assert ascii_value('R')==82
        assert ascii_value('S')==83
