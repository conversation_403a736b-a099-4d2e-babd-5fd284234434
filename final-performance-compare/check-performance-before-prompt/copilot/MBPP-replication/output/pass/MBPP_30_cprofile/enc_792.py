from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to count the number of lists in a given number of lists.
    '''
    def count_list(input_list):
        count = 0
        for i in range(0, len(input_list)):
            count += 1
        return count
    '''
    Standard answer: 
    def count_list(input_list): 
        return len(input_list)
    '''
    for _ in range(30):
        assert count_list([[1, 3], [5, 7], [9, 11], [13, 15, 17]]) == 4
        assert count_list([[1,2],[2,3],[4,5]]) == 3
        assert count_list([[1,0],[2,0]]) == 2
