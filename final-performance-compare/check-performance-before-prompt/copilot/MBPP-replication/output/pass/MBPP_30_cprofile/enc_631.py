from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to replace whitespaces with an underscore and vice versa in a given string by using regex.
    '''
    def replace_spaces(text):
      text =text.replace (" ", "_")
      return (text)
      text =text.replace ("_", " ")
      return (text)
    '''
    Standard answer: 
    import re
    text = 'Python Exercises'
    def replace_spaces(text):
      text =text.replace (" ", "_")
      return (text)
      text =text.replace ("_", " ")
      return (text)
    '''
    for _ in range(30):
        assert replace_spaces('Jumanji <PERSON> Jungle') == 'Jumanji_The_Jungle'
        assert replace_spaces('The Avengers') == 'The_Avengers'
        assert replace_spaces('Fast and Furious') == 'Fast_and_Furious'
