from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the surface area of a cylinder.
    '''
    def surfacearea_cylinder(r,h):
      surfacearea=((2*3.1415*r*r) +(2*3.1415*r*h))
      return surfacearea
    '''
    Standard answer: 
    def surfacearea_cylinder(r,h):
      surfacearea=((2*3.1415*r*r) +(2*3.1415*r*h))
      return surfacearea
    '''
    for _ in range(30):
        assert surfacearea_cylinder(10,5)==942.45
        assert surfacearea_cylinder(4,5)==226.18800000000002
        assert surfacearea_cylinder(4,10)==351.848
