from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to find the last digit of a given number.
    '''
    def last_Digit(n) :
        return (n % 10)
    '''
    Standard answer: 
    def last_Digit(n) :
        return (n % 10) 
    '''
    for _ in range(30):
        assert last_Digit(123) == 3
        assert last_Digit(25) == 5
        assert last_Digit(30) == 0
