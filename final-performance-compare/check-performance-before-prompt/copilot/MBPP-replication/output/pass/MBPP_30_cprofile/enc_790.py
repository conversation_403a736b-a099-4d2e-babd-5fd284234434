from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to check whether every even index contains even numbers of a given list.
    '''
    def even_position(nums):
    	for i in range(len(nums)):
    		if nums[i] % 2 != i % 2:
    			return False
    	return True
    '''
    Standard answer: 
    def even_position(nums):
    	return all(nums[i]%2==i%2 for i in range(len(nums)))
    '''
    for _ in range(30):
        assert even_position([3,2,1]) == False
        assert even_position([1,2,3]) == False
        assert even_position([2,1,4]) == True
