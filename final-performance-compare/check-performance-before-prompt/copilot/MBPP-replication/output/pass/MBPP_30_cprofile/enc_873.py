from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to solve the fibonacci sequence using recursion.
    '''
    def fibonacci(n):
      if n <= 1:
        return n
      else:
        return (fi<PERSON><PERSON><PERSON>(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2))
    '''
    Standard answer: 
    def fibonacci(n):
      if n == 1 or n == 2:
        return 1
      else:
        return (fi<PERSON><PERSON><PERSON>(n - 1) + (fi<PERSON><PERSON><PERSON>(n - 2)))
    '''
    for _ in range(30):
        assert fibon<PERSON>ci(7) == 13
        assert fibonacci(8) == 21
        assert fibonacci(9) == 34
