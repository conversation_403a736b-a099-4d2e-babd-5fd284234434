from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to check whether all the characters are same or not.
    '''
    def all_Characters_Same(s) :
        n = len(s)
        for i in range(1,n) :
            if s[i] != s[0] :
                return False
        return True
    '''
    Standard answer: 
    def all_Characters_Same(s) :
        n = len(s)
        for i in range(1,n) :
            if s[i] != s[0] :
                return False
        return True
    '''
    for _ in range(30):
        assert all_Characters_Same("python") == False
        assert all_Characters_Same("aaa") == True
        assert all_Characters_Same("data") == False
