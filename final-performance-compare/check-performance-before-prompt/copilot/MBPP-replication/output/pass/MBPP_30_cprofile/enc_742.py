from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to caluclate the area of a tetrahedron.
    '''
    def area_tetrahedron(side):
      area = (side**2) * (3**0.5)
      return area
    '''
    Standard answer: 
    import math
    def area_tetrahedron(side):
      area = math.sqrt(3)*(side*side)
      return area
    '''
    for _ in range(30):
        assert area_tetrahedron(3)==15.588457268119894
        assert area_tetrahedron(20)==692.8203230275509
        assert area_tetrahedron(10)==173.20508075688772
