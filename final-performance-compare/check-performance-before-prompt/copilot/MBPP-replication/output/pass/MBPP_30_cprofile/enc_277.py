from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to filter a dictionary based on values.
    '''
    def dict_filter(dict,n):
      result = {}
      for key, value in dict.items():
        if value >= n:
          result[key] = value
      return result
    '''
    Standard answer: 
    def dict_filter(dict,n):
     result = {key:value for (key, value) in dict.items() if value >=n}
     return result
    '''
    for _ in range(30):
        assert dict_filter({'Cierra Vega': 175, '<PERSON><PERSON>trell': 180, 'Kierra Gentry': 165, '<PERSON>': 190},170)=={'Cierra Vega': 175, '<PERSON><PERSON> Cantrell': 180, '<PERSON>': 190}
        assert dict_filter({'Cierra Vega': 175, 'Al<PERSON> Cantrell': 180, 'Kierra Gentry': 165, '<PERSON>': 190},180)=={ 'Alden Cantrell': 180, '<PERSON>': 190}
        assert dict_filter({'Cierra Vega': 175, '<PERSON><PERSON>trell': 180, 'Kierra Gentry': 165, '<PERSON>': 190},190)=={ '<PERSON>': 190}
