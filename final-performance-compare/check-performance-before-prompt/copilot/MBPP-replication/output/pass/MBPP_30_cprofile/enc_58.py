from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to check whether the given two integers have opposite sign or not.
    '''
    def opposite_Signs(x, y):
        return (x < 0) ^ (y < 0)
    '''
    Standard answer: 
    def opposite_Signs(x,y): 
        return ((x ^ y) < 0); 
    '''
    for _ in range(30):
        assert opposite_Signs(1,-2) == True
        assert opposite_Signs(3,2) == False
        assert opposite_Signs(-10,-10) == False
