from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to find the cube sum of first n even natural numbers.
    '''
    def cube_Sum(n): 
        sum = 0
        for i in range(1,n + 1): 
            sum += (2*i)*(2*i)*(2*i) 
        return sum
    '''
    Standard answer: 
    def cube_Sum(n): 
        sum = 0
        for i in range(1,n + 1): 
            sum += (2*i)*(2*i)*(2*i) 
        return sum
    '''
    for _ in range(30):
        assert cube_Sum(2) == 72
        assert cube_Sum(3) == 288
        assert cube_Sum(4) == 800
