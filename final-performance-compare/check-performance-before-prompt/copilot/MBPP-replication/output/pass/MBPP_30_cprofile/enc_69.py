from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to check whether a list contains the given sublist or not.
    '''
    def is_sublist(l, s):
        sub_set = False
        if s == []:
            sub_set = True
        elif s == l:
            sub_set = True
        elif len(s) > len(l):
            sub_set = False
        else:
            for i in range(len(l)):
                if l[i] == s[0]:
                    n = 1
                    while (n < len(s)) and (l[i+n] == s[n]):
                        n += 1                
                    if n == len(s):
                        sub_set = True
        return sub_set
    '''
    Standard answer: 
    def is_sublist(l, s):
    	sub_set = False
    	if s == []:
    		sub_set = True
    	elif s == l:
    		sub_set = True
    	elif len(s) > len(l):
    		sub_set = False
    	else:
    		for i in range(len(l)):
    			if l[i] == s[0]:
    				n = 1
    				while (n < len(s)) and (l[i+n] == s[n]):
    					n += 1				
    				if n == len(s):
    					sub_set = True
    	return sub_set
    '''
    for _ in range(30):
        assert is_sublist([2,4,3,5,7],[3,7])==False
        assert is_sublist([2,4,3,5,7],[4,3])==True
        assert is_sublist([2,4,3,5,7],[1,6])==False
