from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the maximum of nth column from the given tuple list.
    '''
    def max_of_nth(test_list, N):
      res = 0
      for i in test_list:
        if i[N] > res:
          res = i[N]
      return (res)
    '''
    Standard answer: 
    def max_of_nth(test_list, N):
      res = max([sub[N] for sub in test_list])
      return (res) 
    '''
    for _ in range(30):
        assert max_of_nth([(5, 6, 7), (1, 3, 5), (8, 9, 19)], 2) == 19
        assert max_of_nth([(6, 7, 8), (2, 4, 6), (9, 10, 20)], 1) == 10
        assert max_of_nth([(7, 8, 9), (3, 5, 7), (10, 11, 21)], 1) == 11
