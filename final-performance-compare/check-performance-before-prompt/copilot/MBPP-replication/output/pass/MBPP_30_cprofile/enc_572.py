from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to remove two duplicate numbers from a given number of lists.
    '''
    def two_unique_nums(nums):
      result = []
      for i in nums:
        if nums.count(i) == 1:
          result.append(i)
      return result
    '''
    Standard answer: 
    def two_unique_nums(nums):
      return [i for i in nums if nums.count(i)==1]
    '''
    for _ in range(30):
        assert two_unique_nums([1,2,3,2,3,4,5]) == [1, 4, 5]
        assert two_unique_nums([1,2,3,2,4,5]) == [1, 3, 4, 5]
        assert two_unique_nums([1,2,3,4,5]) == [1, 2, 3, 4, 5]
