from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find squares of individual elements in a list using lambda function.
    '''
    def square_nums(nums):
      square_nums = list(map(lambda x: x ** 2, nums))
      return square_nums
    '''
    Standard answer: 
    def square_nums(nums):
     square_nums = list(map(lambda x: x ** 2, nums))
     return square_nums
    '''
    for _ in range(30):
        assert square_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]
        assert square_nums([10,20,30])==([100,400,900])
        assert square_nums([12,15])==([144,225])
