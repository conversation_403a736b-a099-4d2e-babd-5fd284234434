from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to check if roots of a quadratic equation are reciprocal of each other or not.
    '''
    def Check_Solution(a,b,c):
        if (a == c):
            return ("Yes")
        else:
            return ("No")
    '''
    Standard answer: 
    def Check_Solution(a,b,c): 
        if (a == c): 
            return ("Yes"); 
        else: 
            return ("No"); 
    '''
    for _ in range(30):
        assert Check_Solution(2,0,2) == "Yes"
        assert Check_Solution(2,-5,2) == "Yes"
        assert Check_Solution(1,2,3) == "No"
