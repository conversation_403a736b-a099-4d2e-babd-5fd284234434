from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the index of the first occurrence of a given number in a sorted array.
    '''
    def find_first_occurrence(A, x):
        for i in range(len(A)):
            if A[i] == x:
                return i
        return -1
    '''
    Standard answer: 
    def find_first_occurrence(A, x):
        (left, right) = (0, len(A) - 1)
        result = -1
        while left <= right:
            mid = (left + right) // 2
            if x == A[mid]:
                result = mid
                right = mid - 1
            elif x < A[mid]:
                right = mid - 1
            else:
                left = mid + 1
        return result
    '''
    for _ in range(30):
        assert find_first_occurrence([2, 5, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 1
        assert find_first_occurrence([2, 3, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 2
        assert find_first_occurrence([2, 4, 1, 5, 6, 6, 8, 9, 9, 9], 6) == 4
