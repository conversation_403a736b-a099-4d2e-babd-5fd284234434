from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to count numeric values in a given string.
    '''
    def number_ctr(str):
        number_ctr= 0
        for i in range(len(str)):
            if str[i] >= '0' and str[i] <= '9': number_ctr += 1     
        return  number_ctr
    '''
    Standard answer: 
    def number_ctr(str):
          number_ctr= 0
          for i in range(len(str)):
              if str[i] >= '0' and str[i] <= '9': number_ctr += 1     
          return  number_ctr
    '''
    for _ in range(30):
        assert number_ctr('program2bedone') == 1
        assert number_ctr('3wonders') ==1
        assert number_ctr('123') == 3
