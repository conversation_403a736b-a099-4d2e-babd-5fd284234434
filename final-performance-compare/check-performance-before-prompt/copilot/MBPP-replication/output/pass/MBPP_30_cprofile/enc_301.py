from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the depth of a dictionary.
    '''
    def dict_depth(d):
        if isinstance(d, dict):
            return 1 + (max(map(dict_depth, d.values())) if d else 0)
        return 0
    '''
    Standard answer: 
    def dict_depth(d):
        if isinstance(d, dict):
            return 1 + (max(map(dict_depth, d.values())) if d else 0)
        return 0
    '''
    for _ in range(30):
        assert dict_depth({'a':1, 'b': {'c': {'d': {}}}})==4
        assert dict_depth({'a':1, 'b': {'c':'python'}})==2
        assert dict_depth({1: 'Sun', 2: {3: {4:'Mon'}}})==3
