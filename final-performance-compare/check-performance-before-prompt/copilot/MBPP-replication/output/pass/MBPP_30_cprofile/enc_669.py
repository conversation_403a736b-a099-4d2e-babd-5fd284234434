from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to check whether the given ip address is valid or not using regex.
    '''
    def check_IP(Ip):
    	import re
    	regex = '''^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.( 
    				25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.( 
    				25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.( 
    				25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)$'''
    	if(re.search(regex, Ip)):
    		return ("Valid IP address")
    	else:
    		return ("Invalid IP address")
    """
    Standard answer: 
    import re 
    regex = '''^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.( 
    			25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.( 
    			25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.( 
    			25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)$'''
    def check_IP(Ip): 
    	if(re.search(regex, Ip)): 
    		return ("Valid IP address") 
    	else: 
    		return ("Invalid IP address") 
    """
    
    for _ in range(30):
        assert check_IP("***********") == 'Valid IP address'
        assert check_IP("**************") == 'Valid IP address'
        assert check_IP("366.1.2.2") == 'Invalid IP address'
