from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the nth tetrahedral number.
    '''
    def tetrahedral_number(n):
        return (n * (n + 1) * (n + 2)) / 6
    '''
    Standard answer: 
    def tetrahedral_number(n): 
    	return (n * (n + 1) * (n + 2)) / 6
    '''
    for _ in range(30):
        assert tetrahedral_number(5) == 35.0
        assert tetrahedral_number(6) == 56.0
        assert tetrahedral_number(7) == 84.0
