from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find perfect squares between two given numbers.
    '''
    def perfect_squares(a, b):
        lists=[]
        for i in range (a,b+1):
            j = 1;
            while j*j <= i:
                if j*j == i:
                     lists.append(i)  
                j = j+1
            i = i+1
        return lists
    '''
    Standard answer: 
    def perfect_squares(a, b):
        lists=[]
        for i in range (a,b+1):
            j = 1;
            while j*j <= i:
                if j*j == i:
                     lists.append(i)  
                j = j+1
            i = i+1
        return lists
    '''
    for _ in range(30):
        assert perfect_squares(1,30)==[1, 4, 9, 16, 25]
        assert perfect_squares(50,100)==[64, 81, 100]
        assert perfect_squares(100,200)==[100, 121, 144, 169, 196]
