from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to find the closest smaller number than n.
    '''
    def closest_num(N):
      return (N - 1)
    '''
    Standard answer: 
    def closest_num(N):
      return (N - 1)
    '''
    for _ in range(30):
        assert closest_num(11) == 10
        assert closest_num(7) == 6
        assert closest_num(12) == 11
