from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to sort the given string.
    '''
    def sort_String(str) : 
        str = ''.join(sorted(str)) 
        return (str)
    '''
    Standard answer: 
    def sort_String(str) : 
        str = ''.join(sorted(str)) 
        return (str) 
    '''
    for _ in range(30):
        assert sort_String("cba") == "abc"
        assert sort_String("data") == "aadt"
        assert sort_String("zxy") == "xyz"
