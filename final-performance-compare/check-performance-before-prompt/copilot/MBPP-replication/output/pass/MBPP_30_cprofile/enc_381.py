from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to sort a list of lists by a given index of the inner list.
    '''
    def index_on_inner_list(list_data, index_no):
        result = sorted(list_data, key=lambda x: x[index_no])
        return result
    '''
    Standard answer: 
    from operator import itemgetter
    def index_on_inner_list(list_data, index_no):
        result = sorted(list_data, key=itemgetter(index_no))
        return result
    '''
    for _ in range(30):
        assert index_on_inner_list([('<PERSON><PERSON>', 98, 99), ('<PERSON>', 97, 96), ('<PERSON>', 91, 94), ('<PERSON>', 94, 98)] ,0)==[('<PERSON>', 94, 98), ('<PERSON>', 97, 96), ('<PERSON><PERSON>', 98, 99), ('<PERSON>', 91, 94)]
        assert index_on_inner_list([('<PERSON><PERSON>', 98, 99), ('<PERSON>', 97, 96), ('<PERSON>', 91, 94), ('<PERSON>', 94, 98)] ,1)==[('<PERSON>', 91, 94), ('<PERSON>', 94, 98), ('<PERSON>', 97, 96), ('<PERSON><PERSON>', 98, 99)]
        assert index_on_inner_list([('<PERSON><PERSON>', 98, 99), ('<PERSON> <PERSON>', 97, 96), ('<PERSON> <PERSON><PERSON>t', 91, 94), ('<PERSON> <PERSON><PERSON>', 94, 98)] ,2)==[('<PERSON> <PERSON><PERSON>t', 91, 94), ('<PERSON> <PERSON>', 97, 96), ('<PERSON> <PERSON><PERSON>', 94, 98), ('<PERSON><PERSON> <PERSON>', 98, 99)]
