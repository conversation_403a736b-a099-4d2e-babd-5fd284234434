from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a function to get a colon of a tuple.
    '''
    def colon_tuplex(tuplex,m,n):
      tuplex = list(tuplex)
      tuplex[m].append(n)
      return tuple(tuplex)
    '''
    Standard answer: 
    from copy import deepcopy
    def colon_tuplex(tuplex,m,n):
      tuplex_colon = deepcopy(tuplex)
      tuplex_colon[m].append(n)
      return tuplex_colon
    '''
    for _ in range(30):
        assert colon_tuplex(("HELLO", 5, [], True) ,2,50)==("HELLO", 5, [50], True) 
        assert colon_tuplex(("HELLO", 5, [], True) ,2,100)==(("HELLO", 5, [100],True))
        assert colon_tuplex(("HELLO", 5, [], True) ,2,500)==("HELLO", 5, [500], True)
