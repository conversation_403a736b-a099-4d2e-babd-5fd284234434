from memory_profiler import profile
@profile
def encapsulated_function():
    '''Write a python function to check whether the given array is monotonic or not.
    '''
    def is_Monotonic(A):
        return (all(A[i] <= A[i + 1] for i in range(len(A) - 1)) or
                all(A[i] >= A[i + 1] for i in range(len(A) - 1)))
    '''
    Standard answer: 
    def is_Monotonic(A): 
        return (all(A[i] <= A[i + 1] for i in range(len(A) - 1)) or
                all(A[i] >= A[i + 1] for i in range(len(A) - 1))) 
    '''
    for _ in range(30):
        assert is_Monotonic([6, 5, 4, 4]) == True
        assert is_Monotonic([1, 2, 2, 3]) == True
        assert is_Monotonic([1, 3, 2]) == False
