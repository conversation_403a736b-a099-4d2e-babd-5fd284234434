#!/usr/bin/env python3
"""
测试单个EvalPerf任务的完整流程
用于验证性能测试框架是否正常工作
"""

import json
import sys
from evalperf_config import EvalPerfConfig
from evalperf_integrated_tester import EvalPerfIntegratedTester


def test_single_task_performance():
    """测试单个任务的性能测试流程"""
    print("=== Testing Single Task Performance ===")

    # 配置
    config = EvalPerfConfig()

    # 创建测试器
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path=str(config.evalperf_dataset_path),
        original_humaneval_path=str(config.original_humaneval_path),
        original_mbpp_path=str(config.original_mbpp_path),
        generated_code_base_path=str(config.workspace_root / "prompt")
    )

    # 选择一个测试任务
    test_task_id = "HumanEval/9"  # rolling_max

    print(f"Testing task: {test_task_id}")

    # 检查任务是否在EvalPerf数据集中
    if test_task_id not in tester.evalperf_data:
        print(f"❌ Task {test_task_id} not found in EvalPerf dataset")
        return False

    evalperf_task = tester.evalperf_data[test_task_id]
    entry_point = evalperf_task['entry_point']
    pe_input = evalperf_task['pe_input']

    print(f"Entry point: {entry_point}")
    print(f"PE input type: {type(pe_input)}")

    # 测试copilot生成的代码
    model = 'copilot'
    print(f"\nTesting {model} generated code...")

    # 加载生成的代码
    generated_code = tester._load_generated_code(model, test_task_id)
    if not generated_code:
        print(f"❌ Generated code not found for {model}")
        return False

    print(f"✅ Generated code loaded ({len(generated_code)} characters)")

    # 提取函数
    generated_function = tester._extract_function_from_code(generated_code, entry_point)
    if not generated_function:
        print(f"❌ Function {entry_point} not found in generated code")
        return False

    print(f"✅ Function extracted ({len(generated_function)} characters)")
    print("Function preview:")
    print(generated_function[:200] + "..." if len(generated_function) > 200 else generated_function)

    # 获取canonical solution
    evalperf_canonical, evalperf_score = tester._get_canonical_solution(test_task_id)
    print(f"\n✅ EvalPerf canonical solution (score: {evalperf_score})")

    # 运行性能测试（只运行3次以节省时间）
    print(f"\nRunning performance test (3 runs)...")

    try:
        # 测试生成的代码
        generated_results = tester._run_performance_test(
            generated_function, entry_point, pe_input, num_runs=3)

        print(f"Generated code results:")
        print(f"  Success: {generated_results['success_count']}/3")
        if generated_results['success_count'] > 0:
            print(f"  Avg execution time: {generated_results['avg_execution_time']:.6f}s")
            print(f"  Avg memory usage: {generated_results['avg_memory_usage']:.2f}MB")
            print(f"  Avg CPU usage: {generated_results['avg_cpu_usage']:.2f}%")
        else:
            print(f"  Errors: {generated_results['errors'][:2]}")  # 显示前2个错误

        # 测试canonical solution
        canonical_results = tester._run_performance_test(
            evalperf_canonical, entry_point, pe_input, num_runs=3)

        print(f"\nCanonical solution results:")
        print(f"  Success: {canonical_results['success_count']}/3")
        if canonical_results['success_count'] > 0:
            print(f"  Avg execution time: {canonical_results['avg_execution_time']:.6f}s")
            print(f"  Avg memory usage: {canonical_results['avg_memory_usage']:.2f}MB")
            print(f"  Avg CPU usage: {canonical_results['avg_cpu_usage']:.2f}%")
        else:
            print(f"  Errors: {canonical_results['errors'][:2]}")  # 显示前2个错误

        # 比较性能
        if (generated_results['success_count'] > 0 and
            canonical_results['success_count'] > 0):

            time_ratio = generated_results['avg_execution_time'] / canonical_results['avg_execution_time']
            memory_ratio = generated_results['avg_memory_usage'] / canonical_results['avg_memory_usage']

            print(f"\nPerformance comparison:")
            print(f"  Execution time ratio: {time_ratio:.3f} ({'better' if time_ratio < 1 else 'worse'})")
            print(f"  Memory usage ratio: {memory_ratio:.3f} ({'better' if memory_ratio < 1 else 'worse'})")

        print("✅ Performance test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("EvalPerf Single Task Performance Test")
    print("=" * 50)

    # 验证配置
    config = EvalPerfConfig()
    if not config.validate_paths():
        print("❌ Configuration validation failed")
        return False

    # 运行单任务测试
    success = test_single_task_performance()

    print(f"\n{'='*50}")
    if success:
        print("🎉 Single task test passed! The framework is working correctly.")
        print("\nYou can now run the full test with:")
        print("python evalperf_integrated_tester.py")
    else:
        print("❌ Single task test failed. Please check the configuration and code.")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
