#!/usr/bin/env python3
"""
调试失败的任务
"""

import json
import tempfile
import subprocess
import sys
from evalperf_integrated_tester import EvalPerfIntegratedTester

def debug_failed_task(task_id):
    """调试单个失败任务"""
    print(f"=== 调试 {task_id} ===")
    
    # 创建测试器
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path="/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl",
        original_humaneval_path="./json-prompt/human-eval-v2-20210705.jsonl",
        original_mbpp_path="./json-prompt/mbpp.jsonl"
    )
    
    try:
        # 获取EvalPerf任务信息
        evalperf_task = tester.evalperf_data[task_id]
        entry_point = evalperf_task['entry_point']
        pe_input = evalperf_task['pe_input']
        
        print(f"Entry point: {entry_point}")
        print(f"PE input: {pe_input}")
        
        # 加载生成的代码
        generated_code = tester._load_generated_code('copilot', task_id)
        if generated_code is None:
            print("❌ 无法加载生成代码")
            return
        
        print(f"生成代码长度: {len(generated_code)} 字符")
        
        # 提取函数
        generated_function = tester._extract_function_from_code(generated_code, entry_point)
        if not generated_function:
            print("❌ 无法提取函数")
            return
        
        print(f"提取的函数:")
        print(generated_function[:200] + "..." if len(generated_function) > 200 else generated_function)
        
        # 尝试运行性能测试
        print("\n尝试运行性能测试...")
        try:
            result = tester._run_performance_test(
                generated_function, entry_point, pe_input, 1  # 只运行1次
            )
            print(f"测试结果: {result}")
        except Exception as e:
            print(f"❌ 性能测试失败: {e}")
            
            # 尝试手动执行代码
            print("\n尝试手动执行...")
            try:
                # 解析pe_input
                if isinstance(pe_input, str):
                    pe_input_data = json.loads(pe_input)
                else:
                    pe_input_data = pe_input
                
                # 创建测试脚本
                test_code = f"""
{generated_function}

# 解析输入数据
pe_input_data = {repr(pe_input_data)}

# 根据pe_input_data的结构调用函数
if isinstance(pe_input_data, list):
    for test_case in pe_input_data:
        if isinstance(test_case, list):
            result = {entry_point}(*test_case)
        else:
            result = {entry_point}(test_case)
        print(f"Result: {{result}}")
else:
    result = {entry_point}(pe_input_data)
    print(f"Result: {{result}}")
"""
                
                # 写入临时文件并执行
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                    f.write(test_code)
                    temp_file = f.name
                
                # 执行
                result = subprocess.run([sys.executable, temp_file], 
                                      capture_output=True, text=True, timeout=10)
                
                print(f"执行结果:")
                print(f"  返回码: {result.returncode}")
                print(f"  输出: {result.stdout}")
                if result.stderr:
                    print(f"  错误: {result.stderr}")
                
                # 清理
                import os
                os.unlink(temp_file)
                
            except Exception as e2:
                print(f"❌ 手动执行也失败: {e2}")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")

def main():
    """主函数"""
    failed_tasks = ['HumanEval/58', 'Mbpp/757']
    
    for task_id in failed_tasks:
        debug_failed_task(task_id)
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    main()
