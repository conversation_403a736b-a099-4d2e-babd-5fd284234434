#!/usr/bin/env python3
"""
快速EvalPerf测试脚本
用于测试少量任务以验证框架功能
"""

from evalperf_integrated_tester import EvalPerfIntegratedTester


def main():
    """快速测试主函数"""
    print("=== EvalPerf Quick Test with Statistical Analysis ===")
    print("Testing 3 tasks with 10 runs each for copilot model")

    # 配置路径
    evalperf_dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    original_humaneval_path = "./json-prompt/human-eval-v2-20210705.jsonl"
    original_mbpp_path = "./json-prompt/mbpp.jsonl"
    generated_code_base_path = "./replication-final/result"

    # 创建测试器
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path=evalperf_dataset_path,
        original_humaneval_path=original_humaneval_path,
        original_mbpp_path=original_mbpp_path
    )

    # 快速测试：只测试copilot，3个任务，每个任务2次
    # 选择数据量较小的任务
    quick_tasks = ['HumanEval/24', 'HumanEval/9', 'HumanEval/1']

    results = tester.test_model_on_evalperf(
        model='copilot',
        num_runs=10,  # 增加到10次以获得有意义的统计结果
        output_dir="./evalperf_results",
        specific_tasks=quick_tasks
    )

    print(f"\n=== Quick Test Results ===")
    print(f"Model: {results['model']}")
    print(f"Total tasks: {results['total_tasks']}")
    print(f"Tested tasks: {results['tested_tasks']}")
    print(f"Successful tasks: {results['successful_tasks']}")
    print(f"Success rate: {results['successful_tasks']/results['tested_tasks']*100:.1f}%")

    # 显示每个任务的详细结果
    for task_id, task_result in results['task_results'].items():
        print(f"\n--- {task_id} ---")
        gen_results = task_result['generated_code_results']
        canon_results = task_result['evalperf_canonical_results']

        if gen_results['success_count'] > 0:
            print(f"Generated code: {gen_results['success_count']}/2 runs")
            print(f"  Avg time: {gen_results['avg_execution_time']:.6f}s")
            print(f"  Avg memory: {gen_results['avg_memory_usage']:.2f}MB")
            print(f"  Avg CPU: {gen_results['avg_cpu_usage']:.2f}%")
        else:
            print(f"Generated code: FAILED")

        if canon_results['success_count'] > 0:
            print(f"Canonical solution: {canon_results['success_count']}/2 runs")
            print(f"  Avg time: {canon_results['avg_execution_time']:.6f}s")
            print(f"  Avg memory: {canon_results['avg_memory_usage']:.2f}MB")
            print(f"  Avg CPU: {canon_results['avg_cpu_usage']:.2f}%")
        else:
            print(f"Canonical solution: FAILED")

        # 性能比较
        comparison = task_result.get('performance_comparison', {})
        if 'vs_evalperf_canonical' in comparison:
            comp = comparison['vs_evalperf_canonical']
            print(f"Performance comparison:")
            print(f"  Time ratio: {comp['execution_time_ratio']:.3f}")
            print(f"  Memory ratio: {comp['memory_usage_ratio']:.3f}")
            print(f"  CPU ratio: {comp['cpu_usage_ratio']:.3f}")

    return results


if __name__ == "__main__":
    main()
