#!/usr/bin/env python3
"""
EvalPerf Canonical Solution 测试器 - 仅测试最高score的canonical solution
"""

import json
import ast
from evalperf_config import EvalPerfConfig

class SimpleEvalPerfTester:
    
    def __init__(self, evalperf_dataset_path):
        self.config = EvalPerfConfig()
        self.evalperf_dataset_path = evalperf_dataset_path
        # 加载数据集
        self.evalperf_tasks = self._load_evalperf_dataset()

    def _load_evalperf_dataset(self):
        """加载EvalPerf数据集"""
        tasks = {}
        with open(self.evalperf_dataset_path, 'r') as f:
            for line in f:
                task = json.loads(line.strip())
                tasks[task['task_id']] = task
        return tasks

    def create_preprocessed_script(self, code_content, test_type="evalperf_canonical", pe_input=None):
        """创建预处理后的脚本 - 仅支持EvalPerf canonical solution"""
        print(f"DEBUG: create_preprocessed_script called with test_type={test_type}, pe_input={'available' if pe_input else 'None'}")

        if pe_input is not None:
            import re
            func_match = re.search(r'def (\w+)\(', code_content)
            func_name = func_match.group(1) if func_match else 'main_function'
            print(f"DEBUG: 检测到函数名: {func_name}")

            # 处理pe_input - 用作rolling_max的输入数据进行性能测试
            if isinstance(pe_input, list) and pe_input:
                # EvalPerf canonical 包含完整函数定义
                test_code = f"{code_content}\n\n"
                
                # 添加性能测试代码 - 直接使用pe_input作为numbers参数
                test_code += "# Performance test with pe_input\n"
                test_code += f"test_data = {pe_input}\n"
                test_code += f"result = {func_name}(test_data)\n"
                test_code += "print('测试完成，结果长度:', len(result))"
                print(f"DEBUG: 使用pe_input数组 (长度: {len(pe_input)}) 进行性能测试")
            else:
                test_code = f"{code_content}\n\nprint('无pe_input数据')"
                print("DEBUG: pe_input 无效，使用空的测试代码")

            return test_code
        else:
            return f"{code_content}\n\nprint('pe_input为None')"

    def test_single_task(self, task_id):
        """测试单个任务 - 仅测试EvalPerf最高score的canonical solution"""
        print(f"Testing {task_id}...")

        # 获取EvalPerf数据
        if task_id not in self.evalperf_tasks:
            print(f"ERROR: 任务 {task_id} 不存在于EvalPerf数据集中")
            return

        # 解析pe_input
        pe_input = self._parse_pe_input(task_id)
        
        # 获取最高score的canonical solution
        evalperf_code = self._get_best_canonical_solution(task_id)
        
        if not evalperf_code:
            print(f"ERROR: 无法获取任务 {task_id} 的canonical solution")
            return

        print(f"DEBUG: EvalPerf Canonical Code (first 200 chars):\n{evalperf_code[:200]}...")

        # 生成预处理后的脚本
        preprocessed_code = self.create_preprocessed_script(
            evalperf_code, test_type="evalperf_canonical", pe_input=pe_input
        )
        
        print("\n=== 生成的测试脚本 ===")
        print(preprocessed_code)

    def _parse_pe_input(self, task_id):
        """解析pe_input数据"""
        try:
            pe_input_raw = self.evalperf_tasks[task_id]['pe_input']
            print(f"DEBUG: pe_input_raw type: {type(pe_input_raw)}, length: {len(pe_input_raw) if isinstance(pe_input_raw, str) else 'N/A'}")
            
            if isinstance(pe_input_raw, str):
                pe_input_parsed = ast.literal_eval(pe_input_raw)
                print(f"DEBUG: pe_input_parsed type: {type(pe_input_parsed)}, length: {len(pe_input_parsed) if isinstance(pe_input_parsed, list) else 'N/A'}")
                
                # pe_input是三层嵌套: [[[数组]]]，我们需要最内层的数组
                if isinstance(pe_input_parsed, list) and pe_input_parsed:
                    level1 = pe_input_parsed[0]  # 第一层解嵌套
                    if isinstance(level1, list) and level1:
                        pe_input = level1[0]  # 第二层解嵌套，得到实际的数字数组
                        print(f"DEBUG: 成功解析pe_input，类型: {type(pe_input)}, 长度: {len(pe_input) if isinstance(pe_input, list) else 'N/A'}")
                        if isinstance(pe_input, list) and len(pe_input) > 0:
                            print(f"DEBUG: pe_input前5个元素: {pe_input[:5]}")
                        return pe_input
            return None
        except Exception as e:
            print(f"DEBUG: pe_input处理失败: {e}")
            return None

    def _get_best_canonical_solution(self, task_id):
        """获取最高score的canonical solution"""
        task_data = self.evalperf_tasks[task_id]
        scores = task_data.get('scores', [])
        references = task_data.get('reference', [])
        
        if scores and references:
            # 找到最高分对应的索引
            max_score_idx = scores.index(max(scores))
            evalperf_code = references[max_score_idx]
            print(f"DEBUG: 选择第{max_score_idx}个reference作为canonical (score: {scores[max_score_idx]})")
            return evalperf_code
        elif references:
            # 备用方案：使用第一个reference
            print("DEBUG: 使用第0个reference作为canonical (无scores数据)")
            return references[0]
        else:
            return None

def main():
    # 初始化测试器
    tester = SimpleEvalPerfTester(
        evalperf_dataset_path='/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl'
    )

    print("=== EvalPerf Canonical Solution 测试 ===")
    print("仅测试最高score的canonical solution")
    print()

    # 测试HumanEval/9 - 仅使用最高score的canonical solution
    tester.test_single_task('HumanEval/9')

if __name__ == "__main__":
    main()
