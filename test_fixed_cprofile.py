#!/usr/bin/env python3
"""
测试修复后的cProfile方法
"""

import cProfile
import pstats
import tempfile
import time


def test_original_cprofile_method():
    """测试原始的cProfile方法"""
    
    # 创建测试代码
    test_code = """
def largest_divisor(n: int) -> int:
    for i in reversed(range(n)):
        if n % i == 0:
            return i

def main():
    largest_divisor(15)  # 使用小数据测试

if __name__ == "__main__":
    main()
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        temp_file = f.name
    
    print("=== 测试原始cProfile方法 ===")
    
    # 方法1: 原始方法
    profile_file = tempfile.mktemp(suffix='.prof')
    
    try:
        # 使用与原始脚本相同的方法
        with open(temp_file, "rb") as file:
            code = compile(file.read(), temp_file, 'exec')
            cProfile.run(code, profile_file)
        
        # 提取cumtime - 与原始方法完全一致
        p = pstats.Stats(profile_file)
        if p.stats:
            execution_time = p.stats[list(p.stats.keys())[0]][3]  # cumtime
            print(f"原始方法执行时间: {execution_time:.8f}s")
            
            # 显示所有函数
            print("所有函数统计:")
            for i, (func, stats) in enumerate(p.stats.items()):
                print(f"  {i+1}. {func}: cumtime={stats[3]:.8f}s")
        else:
            print("没有统计数据")
            
    except Exception as e:
        print(f"原始方法失败: {e}")
    
    # 方法2: 手动计时对比
    print("\n=== 手动计时对比 ===")
    start = time.perf_counter()
    with open(temp_file, "rb") as file:
        code = compile(file.read(), temp_file, 'exec')
        exec(code)
    end = time.perf_counter()
    print(f"手动计时: {end - start:.8f}s")
    
    # 清理
    import os
    if os.path.exists(temp_file):
        os.unlink(temp_file)
    if os.path.exists(profile_file):
        os.unlink(profile_file)


def test_with_large_data():
    """测试大数据情况"""
    
    # 创建测试代码 - 高效算法
    efficient_code = """
def largest_divisor(n: int) -> int:
    if n < 2:
        return n
    if n % 2 == 0:
        return n // 2
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return n // i
    return 1

def main():
    largest_divisor(42973891)

if __name__ == "__main__":
    main()
"""
    
    # 创建测试代码 - 低效算法
    inefficient_code = """
def largest_divisor(n: int) -> int:
    for i in reversed(range(n)):
        if n % i == 0:
            return i

def main():
    largest_divisor(1000)  # 使用较小的数据避免超时

if __name__ == "__main__":
    main()
"""
    
    algorithms = [
        ("高效算法", efficient_code),
        ("低效算法", inefficient_code)
    ]
    
    print("\n=== 大数据测试 ===")
    
    for name, code in algorithms:
        print(f"\n{name}:")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code)
            temp_file = f.name
        
        profile_file = tempfile.mktemp(suffix='.prof')
        
        try:
            # 使用原始方法
            with open(temp_file, "rb") as file:
                compiled_code = compile(file.read(), temp_file, 'exec')
                cProfile.run(compiled_code, profile_file)
            
            # 提取时间
            p = pstats.Stats(profile_file)
            if p.stats:
                execution_time = p.stats[list(p.stats.keys())[0]][3]
                print(f"  执行时间: {execution_time:.8f}s")
            
        except Exception as e:
            print(f"  执行失败: {e}")
        
        # 清理
        import os
        if os.path.exists(temp_file):
            os.unlink(temp_file)
        if os.path.exists(profile_file):
            os.unlink(profile_file)


if __name__ == "__main__":
    test_original_cprofile_method()
    test_with_large_data()
