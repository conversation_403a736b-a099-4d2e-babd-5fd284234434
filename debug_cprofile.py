#!/usr/bin/env python3
"""
调试cProfile测量问题
"""

import cProfile
import pstats
import tempfile
import subprocess
import sys
import time


def debug_cprofile():
    """调试cProfile测量"""

    # 测试代码
    test_code = """
def largest_divisor(n: int) -> int:
    for i in reversed(range(n)):
        if n % i == 0:
            return i

def main():
    largest_divisor(42973891)

if __name__ == "__main__":
    main()
"""

    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        temp_file = f.name

    print("=== cProfile详细分析 ===")

    # 方法1: 直接使用cProfile
    print("1. 直接cProfile:")
    profile_file = tempfile.mktemp(suffix='.prof')

    start = time.perf_counter()
    result = subprocess.run([
        sys.executable, '-m', 'cProfile', '-o', profile_file, temp_file
    ], capture_output=True, text=True, timeout=30)
    end = time.perf_counter()

    print(f"   subprocess总时间: {end - start:.6f}s")
    print(f"   返回码: {result.returncode}")

    if result.returncode == 0:
        # 分析profile结果
        p = pstats.Stats(profile_file)
        print(f"   总函数调用数: {len(p.stats)}")

        # 显示所有统计信息
        print("   前10个最耗时的函数:")
        for i, (func, stats) in enumerate(sorted(p.stats.items(),
                                                key=lambda x: x[1][3], reverse=True)[:10]):
            print(f"     {i+1}. {func}: stats={stats}")

        # 我们当前使用的方法
        first_key = list(p.stats.keys())[0]
        first_cumtime = p.stats[first_key][3]
        print(f"   我们当前取的值 (第一个函数的cumtime): {first_cumtime:.6f}s")

        # 查找main函数
        main_stats = None
        for func, stats in p.stats.items():
            if 'main' in str(func):
                main_stats = stats
                print(f"   main函数: {func}, cumtime={stats[3]:.6f}s")
                break

        # 查找largest_divisor函数
        largest_divisor_stats = None
        for func, stats in p.stats.items():
            if 'largest_divisor' in str(func):
                largest_divisor_stats = stats
                print(f"   largest_divisor函数: {func}, cumtime={stats[3]:.6f}s")
                break

    # 方法2: 手动计时对比
    print("\n2. 手动计时对比:")
    start = time.perf_counter()
    result = subprocess.run([sys.executable, temp_file],
                          capture_output=True, text=True, timeout=30)
    end = time.perf_counter()
    print(f"   手动计时: {end - start:.6f}s")

    # 清理
    import os
    if os.path.exists(temp_file):
        os.unlink(temp_file)
    if os.path.exists(profile_file):
        os.unlink(profile_file)


def test_cprofile_accuracy():
    """测试cProfile的准确性"""
    print("\n=== cProfile准确性测试 ===")

    # 创建不同复杂度的测试
    test_cases = [
        ("快速函数", """
def test_func():
    return sum(range(1000))

def main():
    test_func()

if __name__ == "__main__":
    main()
"""),
        ("中等函数", """
def test_func():
    total = 0
    for i in range(100000):
        total += i * i
    return total

def main():
    test_func()

if __name__ == "__main__":
    main()
"""),
        ("慢函数", """
def test_func():
    total = 0
    for i in range(1000000):
        total += i * i
    return total

def main():
    test_func()

if __name__ == "__main__":
    main()
""")
    ]

    for name, code in test_cases:
        print(f"\n{name}:")

        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code)
            temp_file = f.name

        # 手动计时
        start = time.perf_counter()
        result = subprocess.run([sys.executable, temp_file],
                              capture_output=True, text=True)
        end = time.perf_counter()
        manual_time = end - start

        # cProfile计时
        profile_file = tempfile.mktemp(suffix='.prof')
        result = subprocess.run([
            sys.executable, '-m', 'cProfile', '-o', profile_file, temp_file
        ], capture_output=True, text=True)

        if result.returncode == 0:
            p = pstats.Stats(profile_file)
            # 找到最大的cumtime
            max_cumtime = max(stats[3] for stats in p.stats.values())

            print(f"  手动计时: {manual_time:.6f}s")
            print(f"  cProfile最大cumtime: {max_cumtime:.6f}s")
            print(f"  比率: {max_cumtime/manual_time:.3f}")

        # 清理
        import os
        if os.path.exists(temp_file):
            os.unlink(temp_file)
        if os.path.exists(profile_file):
            os.unlink(profile_file)


if __name__ == "__main__":
    debug_cprofile()
    test_cprofile_accuracy()
