#!/usr/bin/env python3
"""
调试实际生成的测试代码
"""

import json
import tempfile
from evalperf_integrated_tester import EvalPerfIntegratedTester


def debug_generated_test_code():
    """调试实际生成的测试代码"""
    
    tester = EvalPerfIntegratedTester(
        evalperf_dataset_path="/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl",
        original_humaneval_path="./json-prompt/human-eval-v2-20210705.jsonl",
        original_mbpp_path="./json-prompt/mbpp.jsonl"
    )
    
    # 测试HumanEval/24 (我们知道这个有性能问题)
    task_id = 'HumanEval/24'
    
    # 获取任务信息
    evalperf_task = tester.evalperf_data[task_id]
    entry_point = evalperf_task['entry_point']
    pe_input = evalperf_task['pe_input']
    
    print(f"=== {task_id} 调试信息 ===")
    print(f"Entry point: {entry_point}")
    print(f"PE input: {pe_input}")
    
    # 解析pe_input
    pe_input_data = json.loads(pe_input) if isinstance(pe_input, str) else pe_input
    print(f"PE input parsed: {pe_input_data}")
    
    # 获取生成的代码
    generated_code = tester._load_generated_code('copilot', task_id)
    function_code = tester._extract_function_from_code(generated_code, entry_point)
    
    print(f"\n=== Generated Function ===")
    print(function_code)
    
    # 获取canonical solution
    canonical_code, _ = tester._get_canonical_solution(task_id)
    print(f"\n=== Canonical Solution ===")
    print(canonical_code)
    
    # 生成我们实际运行的测试代码
    test_code = f"""
from typing import List, Dict, Any, Optional, Tuple
import json

{function_code}

def main():
    # 解析输入数据
    pe_input_data = {repr(pe_input_data)}
    
    # 根据pe_input_data的结构调用函数，运行所有测试用例
    if isinstance(pe_input_data, list):
        # 对于EvalPerf，通常是三层嵌套的列表
        # 最外层是测试用例列表，每个测试用例包含函数的参数
        for test_case in pe_input_data:
            if isinstance(test_case, list):
                # test_case是参数列表，使用*解包传递给函数
                {entry_point}(*test_case)
            else:
                # 如果test_case不是列表，直接传递
                {entry_point}(test_case)
    else:
        # 单个参数
        {entry_point}(pe_input_data)

if __name__ == "__main__":
    main()
"""
    
    print(f"\n=== 实际运行的测试代码 ===")
    print(test_code)
    
    # 保存到文件并手动测试运行时间
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        temp_file = f.name
    
    print(f"\n=== 手动测试 ===")
    print(f"测试文件保存到: {temp_file}")
    
    # 手动运行并计时
    import time
    import subprocess
    import sys
    
    times = []
    for i in range(5):
        start = time.perf_counter()
        result = subprocess.run([sys.executable, temp_file], 
                              capture_output=True, text=True, timeout=30)
        end = time.perf_counter()
        
        if result.returncode == 0:
            times.append(end - start)
            print(f"运行 {i+1}: {end - start:.6f}s")
        else:
            print(f"运行 {i+1} 失败: {result.stderr}")
            break
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"平均时间: {avg_time:.6f}s")
    
    # 清理
    import os
    if os.path.exists(temp_file):
        os.unlink(temp_file)


def compare_algorithms():
    """比较不同算法的性能"""
    print("\n=== 算法性能对比 ===")
    
    # 高效算法
    efficient_code = """
def largest_divisor(n: int) -> int:
    if n < 2:
        return n
    if n % 2 == 0:
        return n // 2
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return n // i
    return 1

def main():
    largest_divisor(42973891)

if __name__ == "__main__":
    main()
"""
    
    # 低效算法
    inefficient_code = """
def largest_divisor(n: int) -> int:
    for i in reversed(range(n)):
        if n % i == 0:
            return i

def main():
    largest_divisor(42973891)

if __name__ == "__main__":
    main()
"""
    
    algorithms = [
        ("高效算法", efficient_code),
        ("低效算法", inefficient_code)
    ]
    
    import time
    import subprocess
    import sys
    import tempfile
    
    for name, code in algorithms:
        print(f"\n测试 {name}:")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code)
            temp_file = f.name
        
        try:
            start = time.perf_counter()
            result = subprocess.run([sys.executable, temp_file], 
                                  capture_output=True, text=True, timeout=10)
            end = time.perf_counter()
            
            if result.returncode == 0:
                print(f"  运行时间: {end - start:.6f}s")
            else:
                print(f"  运行失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"  运行超时 (>10秒)")
        
        import os
        if os.path.exists(temp_file):
            os.unlink(temp_file)


if __name__ == "__main__":
    debug_generated_test_code()
    compare_algorithms()
